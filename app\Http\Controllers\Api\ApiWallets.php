<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\UserWalletTransactions;
use App\Traits\WalletsTraits;

class ApiWallets
{
    use WalletsTraits;

    function topup(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $rules = [
            'amount' => 'required|numeric',
            'payment_method' => 'required',
        ];
        $validator = Validator::make(request()->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        // insert to payment transaction for payment
        $topup_data = [
            'user_id' => $request->user_id ?? auth()->user()->id,
            'gateway' => $request->payment_method,
            'type' => 'user-topup-wallet',
            'amount' => $request->amount,
        ];
        if($request->payment_method == 'tmp' && isset($request->status)):
            $topup_data['status'] = Constants::paymentStatus['failed'];
        endif;
        
        return $this->topupWallet($topup_data);
    }

    function listing(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $per_page = $request->per_page ?? 10;
        $data = UserWalletTransactions::where('user_id', auth()->user()->id)
            ->with('payment_transaction')
            ->orderBy('id', 'desc')
            ->paginate($per_page);

        if($data->count() > 0):
            $bymonth = [];
            foreach($data as $item):
                $month = date('F Y', strtotime($item['created_at']));

                if(!isset($bymonth[$month])):
                    $bymonth[$month] = [];
                endif;
                $bymonth[$month][] = $item;

                unset($item['created_by']);
                unset($item['updated_at']);
                $item['booking_ref_no'] = null;

                if($item['payment_transaction'] && $item['type'] == 'Top-Up'):
                    $payment = $item['payment_transaction'];
                    unset($payment['user_id']);
                    unset($payment['type']);
                    unset($payment['type_ref_id']);
                    unset($payment['created_at']);
                    unset($payment['updated_at']);

                    if($payment['gateway']):
                        $payment['gateway'] = Constants::paymentGateway[$payment['gateway']] ?? null;
                    endif;
                    $payment['amount'] = number_format($payment['amount'], 2);
                    if($payment['details']):
                        $payment['details'] = json_decode($payment['details']);

                        if($payment['details']->extraP):
                            $payment['details']->extraP = json_decode($payment['details']->extraP);
                        endif;
                    endif;
                elseif($item['type'] == 'Stringing Booking'):
                    $item['booking_ref_no'] = $item->booking->ref_no;
                    unset($item['booking']);
                else:
                    unset($item['payment_transaction']);
                endif;
            endforeach;

            $bymonth_list = [];
            foreach($bymonth as $key => $item):
                $bymonth_list[] = [
                    'month' => $key,
                    'data' => $item,
                ];
            endforeach;

            $data->setCollection(collect($bymonth_list));
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail(Request $request)
    {
        $data = UserWalletTransactions::where('user_id', auth()->user()->id)
            ->with('payment_transaction')
            ->find($request->id)
            ->makeHidden(['created_by', 'updated_at']);
        
        if($data->payment_transaction && $data->type == 'Top-Up'):
            $payment = $data->payment_transaction->makeHidden([
                'user_id',
                'type',
                'type_ref_id',
                'created_at',
                'updated_at',
            ]);

            if($payment->gateway):
                $payment->gateway = Constants::paymentGateway[$payment->gateway] ?? null;
            endif;
            $payment->amount = number_format($payment->amount, 2);
            if($payment->details):
                $payment->details = json_decode($payment->details);

                if($payment->details->extraP):
                    $payment->details->extraP = json_decode($payment->details->extraP);
                endif;
            endif;
        else:
            unset($data->payment_transaction);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}