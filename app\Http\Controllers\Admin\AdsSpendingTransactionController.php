<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\SalesChannel;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\AdsSpendingTransaction;

class AdsSpendingTransactionController extends Controller
{
    function index()
    {
        if (!checkPermission('view_ads_spending_transaction')):
            return redirect(route('index'))->with('error', __('You do not have permission to view ads spending transaction.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('adsSpendingTransactionDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('adsSpendingTransactionExport'),
                'filename' => 'ads_spending_transaction'
            ]
        ];

        if (!checkPermission('delete_ads_spending_transaction')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_ads_spending_transaction')):
            unset($bulk_action['export']);
        endif;

        $channels = SalesChannel::orderBy('title', 'ASC')
            ->get();

        $moduleID = 'ads_spending_transaction';
        return view('ads-spending-transaction', compact('bulk_action', 'channels','moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = AdsSpendingTransaction::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
            $q->where('title', 'LIKE', "%{$search}%");
        })
            ->whereIn('brand', userBrand())
            ->when(isset($request->brand) && $request->brand != 'all', function ($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                        <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="adsSpendingTransactionList">
                        <span></span>
                    </label>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_ads_spending_transaction')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_ads_spending_transaction')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    config('staticdata.brand')[$item->brand],
                    isset($item->salesChannel) ? $item->salesChannel->channel_name : '-',
                    $item->is_online == 1 ? 'Yes' : 'No',
                    $item->total_cost_spent,
                    $item->leads_count,
                    $item->new_connection_count,
                    number_format($item->cost_per_lead, 2),
                    $item->return_on_ad_spend,
                    $item->view_count,
                    Carbon::parse($item->date)->format('d-m-Y'),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_ads_spending_transaction')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = AdsSpendingTransaction::find($request->id);
        if ($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;
        $data->date = Carbon::parse($data->date)->format('d-m-Y');
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = AdsSpendingTransaction::find($id);
        else:
            $msg = __('Added!');
            $query = new AdsSpendingTransaction();
        endif;

        $query->brand = $request->brand;
        $query->sales_channel_id = $request->sales_channel_id;
        $query->total_cost_spent = $request->total_cost_spent;
        $query->leads_count = $request->leads_count;
        $query->new_connection_count = $request->new_connection_count;
        $query->cost_per_lead = $request->cost_per_lead;
        $query->return_on_ad_spend = $request->return_on_ad_spend;
        $query->view_count = $request->view_count;
        $query->description = $request->description;
        $query->is_online = $request->is_online ?? 0;
        $query->date = Carbon::createFromFormat(config('app.display_date_format'), $request->date)->format('Y-m-d');

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if ($request->id):
            AdsSpendingTransaction::find($request->id)->delete();
        else:
            AdsSpendingTransaction::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = AdsSpendingTransaction::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = AdsSpendingTransaction::when($request->search, function ($q) use ($request) {
            $search = $request->search;
            $q->where('title', 'LIKE', "%{$search}%");
        })
            ->whereIn('brand', userBrand())
            ->when($request->selected, function ($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->brand) && $request->brand != 'all', function ($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'ads_spending_transaction_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'ads_spending_transaction'),
            $fileName
        );
    }
}
