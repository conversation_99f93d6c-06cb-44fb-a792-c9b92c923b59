<?php

namespace App\Models;

use App\Models\Scopes\RetailerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Retailer extends Model
{
    use HasFactory;

    protected $table = 'admin_user';
    public $primaryKey = 'user_id';
    public $timestamps = false;

    protected $fillable = [
        'user_name',
        'user_password',
        'user_type',
        'company_name',
        'first_name',
        'last_name',
        'phone_number',
        'role_id',
        'retailer_price_group_id',
        'is_active',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected static function boot()
    {
        static::addGlobalScope(new RetailerScope());

        parent::boot();
    }

    public function role()
    {
        return $this->hasOne(Roles::class, 'id', 'role_id');
    }

    public function priceGroup()
    {
        return $this->hasOne(PriceGroups::class, 'id', 'retailer_price_group_id');
    }

    public function members()
    {
        return $this->hasMany(Users::class, 'retailer_id', 'user_id');
    }

    public function retailerProducts()
    {
        return $this->hasMany(Products::class, 'retailer_id', 'user_id');
    }
}
