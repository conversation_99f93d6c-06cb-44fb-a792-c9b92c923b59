<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\ShippingFee;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\UserAddressBook;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class ShippingFeeController extends Controller
{
    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function index()
    {
        if (!checkPermission('view_shipping_fee')):
            return redirect(route('index'))->with('error', __('You do not have permission to view shipping fee.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('shippingFeeDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('shippingFeeExport'),
                'filename' => 'shipping_fees'
            ]
        ];

        if (!checkPermission('delete_shipping_fee')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_shipping_fee')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'shipping_fees';
        return view('shipping-fee', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = ShippingFee::when($request->region, function ($q) use ($request) {
            $q->whereJsonContains('region', $request->region);
        })
            ->when($this->brandSelector == true, function ($q) {
                foreach (userBrand() as $brand) {
                    $q->orWhereJsonContains('brand', $brand);
                }
            })
            ->when($this->brandSelector && isset($request->brand), function ($query) use ($request) {
                $query->whereJsonContains('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by)
            ->when($sort_col == 'total_from', function ($q) use ($sort_by) {
                $q->orderBy('total_to', $sort_by);
            });
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="shipping_feesList">
                    <span></span>
                </label>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_shipping_fee')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '" data-brand="' . $item->brand . '" data-totalfrom="' . $item->total_from . '" data-totalto="' . $item->total_to . '" data-cost="' . $item->cost . '" data-region="' . $item->region . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_shipping_fee')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $brand = $item->brand ? implode(', ', array_map(function ($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';
                $region = $item->region ? implode(', ', array_map(function ($regionItem) {
                    return config('staticdata.region.' . $regionItem);
                }, json_decode($item->region))) : '';

                $param = [
                    $checkbox,
                    // $brand,
                    $item->total_from . ' - ' . $item->total_to,
                    formatNumber($item->cost, 2),
                    // $region,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_shipping_fee')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = ShippingFee::find($id);
        else:
            $msg = __('Added!');
            $query = new ShippingFee();
        endif;

        $total_from = $request->total_from;
        $total_to = $request->total_to;
        if ($total_from > $total_to):
            return GlobalFunction::sendSimpleResponse(false, __('Total from must be less than total to.'));
        endif;

        if ($total_to < $total_from):
            return GlobalFunction::sendSimpleResponse(false, __('Total to must be greater than total from.'));
        endif;

        $exist = ShippingFee::where(function ($q) use ($total_from, $total_to) {
            $q->where(function ($query) use ($total_from, $total_to) {
                $query->where('total_from', '<=', $total_to)
                    ->where('total_to', '>=', $total_from);
            });
        })
            ->when($id, function ($q) use ($id) {
                $q->where('id', '!=', $id);
            })
            ->when(isset($request->brand), function ($q) use ($request) {
                foreach ($request->brand as $brand) {
                    $q->orWhereJsonContains('brand', $brand);
                }
            })
            ->when(isset($request->region), function ($q) use ($request) {
                foreach ($request->region as $region) {
                    $q->orWhereJsonContains('region', $region);
                }
            })
            ->count();
        if ($exist > 0):
            return GlobalFunction::sendSimpleResponse(false, __('Order Threshold already exists.'));
        endif;

        if (isset($request->brand)) {
            $query->brand = json_encode($request->brand);
        }

        if (isset($request->region)) {
            $query->region = json_encode($request->region);
        }
        $query->total_from = $total_from;
        $query->total_to = $total_to;
        $query->cost = $request->cost;

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function detail(Request $request)
    {
        $data = ShippingFee::find($request->id);
        $data->brand = $data->brand ? json_decode($data->brand) : [];
        $data->region = $data->region ? json_decode($data->region) : [];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function delete(Request $request)
    {
        if ($request->id):
            $delete_id = [$request->id];
        else:
            $delete_id = $request->selected;
        endif;

        ShippingFee::whereIn('id', $delete_id)->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = ShippingFee::when($request->region, function ($q) use ($request) {
            $q->whereJsonContains('region', $request->region);
        })
            ->when($this->brandSelector == true, function ($q) {
                foreach (userBrand() as $brand) {
                    $q->orWhereJsonContains('brand', $brand);
                }
            })
            ->when($this->brandSelector && isset($request->brand), function ($query) use ($request) {
                $query->whereJsonContains('brand', $request->brand);
            })
            ->when($request->selected, function ($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->region), function ($q) use ($request) {
                foreach ($request->region as $region) {
                    $q->orWhereJsonContains('region', $region);
                }
            })
            ->orderBy($sort_col, $sort_by)
            ->when($sort_col == 'total_from', function ($q) use ($sort_by) {
                $q->orderBy('total_to', $sort_by);
            })
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $item->brand = $item->brand ? implode(', ', array_map(function ($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';
                $item->region = $item->region ? implode(', ', array_map(function ($regionItem) {
                    return config('staticdata.region.' . $regionItem);
                }, json_decode($item->region))) : '';
            endforeach;
        endif;

        $fileName = 'shipping_fees' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'shipping_fee'),
            $fileName
        );
    }
}
