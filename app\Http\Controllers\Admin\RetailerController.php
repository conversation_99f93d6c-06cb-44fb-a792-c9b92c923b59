<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Retailer;
use App\Models\PriceGroups;
use App\Traits\EmailTraits;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;

class RetailerController extends Controller
{
    use EmailTraits;
    function index()
    {
        if(!checkPermission('view_retailers')):
            return redirect(route('index'))->with('error', __('You do not have permission to view retailers.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('retailersDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('retailersExport'),
                'filename' => 'retailers'
            ]
        ];

        if(!checkPermission('delete_retailers')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_retailers')):
            unset($bulk_action['export']);
        endif;

        $price_group = PriceGroups::where('type', 'retailer')
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $moduleID = 'retailers';
        return view('retailers', compact('bulk_action', 'moduleID', 'price_group'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Retailer::whereNull('deleted_at')
            ->when($request->input('search.value'), function($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        // ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('user_name', 'LIKE', "%{$search}%");
                });
            })
            ->when($request->brand, function($query) use ($request) {
                $query->whereJsonContains('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->whereJsonContains('country', $request->country);
            })
            ->when($request->pricegroup, function($query) use ($request) {
                $query->where('retailer_price_group_id', $request->pricegroup);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->type, function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();
       
            foreach ($result as $item):
                $id = $item->user_id;
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$id.'" class="check-selected form-check-input" data-datatable="retailersList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_retailers')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_retailers')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$id.'" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_retailers')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $brand = $item->brand ? implode(', ', array_map(function($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';
                $country = $item->country ? implode(', ', array_map(function($countryItem) {
                    return config('staticdata.country.' . $countryItem);
                }, json_decode($item->country))) : '';

                $type = config('staticdata.retailer_type')[$item->type] ?? '-';
                if($item->type == 'credit_terms'):
                    $type .= ' <div class="text-muted">'.number_format($item->credit_limit, 0).'</div>';
                endif;

                $param = [
                    $checkbox,
                    $item->user_name,
                    $item->first_name,
                    $item->phone_number,
                    $type,
                    $item->margin ? $item->margin.'%' : '',
                    $brand,
                    $country,
                    $item->priceGroup->name ?? '-',
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action,
                ];
                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_retailers') && !checkPermission('delete_retailers')):
                    unset($param[10]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = Retailer::find($request->id);
        if($data):
            if($data->brand):
                $data->brand = json_decode($data->brand);
            endif;
            if($data->country):
                $data->country = json_decode($data->country);
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $email = $request->email_address;
        $phone = $request->phone_number;

        // check for email unique
        $checkExist = Retailer::whereNull('deleted_at')
            ->where('user_name', $email)
            ->when($id > 0, function($q) use($id) {
                $q->where('user_id', '!=', $id);
            })
            ->first();
        if($checkExist):
            return GlobalFunction::sendSimpleResponse(false, __('Email address is already taken.'));
        endif;
        // END check for email unique

        if(!GlobalFunction::validatePhoneNumber($phone)):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
        endif;

        if($request->password && $request->password != $request->confirm_password):
            return GlobalFunction::sendSimpleResponse(false, __('Password and confirm password does not match.'));
        endif;

        if($id):
            $msg = __('Updated!');
            $user = Retailer::find($id);
        else:
            $msg = __('Added!');
            $user = new Retailer();
        endif;

        $user->company_name = $request->company_name;
        $user->type = $request->type;

        $user->user_name = $email;
        $user->first_name = $request->first_name;
        $user->phone_number = $request->phone_number;
        $user->brand = $request->brand ? json_encode($request->brand) : null;
        $user->country = $request->country ? json_encode($request->country) : null;
        $user->margin = $request->margin;
        $user->retailer_price_group_id = $request->price_group;
        $user->is_active = $request->status ?? 0;
        $user->user_type = 2;
        $user->role_id = 2;

        if(!$id):
            $password = $request->password;
            $user->user_password = Hash::make($password);
            $user->created_at = Carbon::now();

            // email user for password
            $email_data = [
                'type' => 'retailer',
                'email_type' => 'account_created',
                'name' => $request->first_name.' '.$request->last_name,
                'email' => $email,
                'password' => $password,
            ];
            $this->sendEmail($email_data);
            // END email user for password
        else:
            if($request->password && $request->password == $request->confirm_password):
                $user->user_password = Hash::make($request->password);
            endif;
            $user->updated_at = Carbon::now();
        endif;

        $user->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = Retailer::find($request->id);
        else:
            $query = Retailer::whereIn('user_id', $request->selected);
        endif;

        $query = $query->update(['deleted_at' => Carbon::now()]);

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Retailer::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Retailer::whereNull('deleted_at')
            ->when($request->input('search.value'), function($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        // ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('user_name', 'LIKE', "%{$search}%");
                });
            })
            ->when($request->brand, function($query) use ($request) {
                $query->whereJsonContains('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->whereJsonContains('country', $request->country);
            })
            ->when($request->pricegroup, function($query) use ($request) {
                $query->where('retailer_price_group_id', $request->pricegroup);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->type, function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));

                $item->brand = $item->brand ? implode(', ', array_map(function($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';
                $item->country = $item->country ? implode(', ', array_map(function($countryItem) {
                    return config('staticdata.country.' . $countryItem);
                }, json_decode($item->country))) : '';
            endforeach;
        endif;

        $fileName = 'retailers_'.date('Ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'retailers'),
            $fileName
        );
    }

    function dropdownList($first='')
    {
        $result = Retailer::whereNull('deleted_at')
            ->when(request()->search, function($q) {
                $search = request()->search;
                $q->where(function($q) use ($search) {
                    $q->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('user_name', 'LIKE', "%{$search}%");
                });
            })
            ->when(request()->brand && request()->brand != 'all', function ($q) {
                if (json_decode(request()->brand)) {
                    $brand = json_decode(request()->brand);
                } else {
                    $brand = request()->brand;
                }

                if (is_array($brand)) {
                    foreach ($brand as $item) {
                        $q->orWhereJsonContains('brand', $item);
                    }
                } else {
                    $q->WhereJsonContains('brand', $brand);
                }
            })
            ->when(request()->type, function($q) {
                $q->where('type', request()->type);
            })
            ->orderBy('first_name', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            if (isset(request()->json)) {
                $user = [];
                foreach ($result as $item) {
                    $user[] = [
                        'id' => $item->user_id,
                        'text' => $item->first_name . ' ' . $item->last_name . ' (' . $item->user_name . ')',
                    ];
                }

                return response()->json([
                    'results' => $user,
                ]);
            }
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->first_name.' '.$item->last_name.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }
}
