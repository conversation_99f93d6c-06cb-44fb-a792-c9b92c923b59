<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RedemptionVoucherProducts extends Model
{
    use HasFactory;

    public function product()
    {
        return $this->belongsTo(Products::class, 'product_id');
    }

    public function variation()
    {
        return $this->belongsTo(ProductVariations::class, 'variation_id');
    }
}
