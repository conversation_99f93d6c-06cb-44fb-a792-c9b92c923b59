<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\UserAddressBook;
use App\Models\Retailer;
use App\Models\Country;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class UserAddressBookController extends Controller
{
    function index()
    {
        if (!checkPermission('view_user_address_books')):
            return redirect(route('index'))->with('error', __('You do not have permission to view user address book.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('userAddressBookDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('userAddressBookExport'),
                'filename' => 'user_address_books'
            ]
        ];

        if(!checkPermission('delete_user_address_books')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_user_address_books')):
            unset($bulk_action['export']);
        endif;

        $countries = Country::all();
        $moduleID = 'user_address_books';
        return view('user-address-book', compact('bulk_action', 'moduleID', 'countries'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';

        $result = UserAddressBook::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('recipient', 'LIKE', "%{$search}%");
            })
            ->when($request->state, function ($q) use ($request) {
                $q->where('state', $request->state);
            })
            ->when($request->type, function ($q) use ($request) {
                $q->where('user_type', $request->type);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($q) use ($request) {
                $q->where('user_id', $request->retailer);
            })
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="user_address_booksList">
                    <span></span>
                </label>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_user_address_books')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Edit').'" data-recipient="' . $item->recipient . '" data-address="'.$item->address.'" data-city="'.$item->city.'" data-postcode="'.$item->postcode.'" data-state="'.$item->state.'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_user_address_books')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                if($item->user_type == 'retailer'):
                    $user_name = Retailer::where('user_id', $item->user_id)->first()->first_name;
                else:
                    $user_name = User::find($item->user_id)->name;
                endif;

                $state = config('staticdata.states.' . $item->state) ?? $item->state;
                $address = $item->address . ', ' . $item->postcode . ' ' . $item->city . ', ' . $state.', '.getCountryName($item->country);

                $param = [
                    $checkbox,
                    // ucwords($item->user_type),
                    $user_name,
                    $item->label,
                    $item->recipient,
                    $item->phone_number,
                    $address,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_user_address_books')):
                    unset($param[6]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = UserAddressBook::find($request->id);

        if ($data) {
            if($data->user_type == 'retailer'):
                $data->user = Retailer::where('user_id', $data->user_id)->first();
            else:
                $data->user = User::find($data->user_id);
            endif;
        }

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $phone = $request->phone_number;

        if(!GlobalFunction::validatePhoneNumber($phone)):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
        endif;

        if($id):
            $msg = __('Updated!');
            $query = UserAddressBook::find($id);
        else:
            $msg = __('Added!');
            $query = new UserAddressBook();
        endif;

        $query->user_type = $request->user_type ?? 'customer';
        if($request->user_type == 'retailer'):
            $query->user_id = $request->retailer_id;
        else:
            $query->user_id = $request->user_id;
        endif;
        $query->label = $request->label;
        $query->recipient = $request->recipient;
        $query->phone_number = $phone;
        $query->address = $request->address;
        $query->longitude = $request->longitude;
        $query->latitude = $request->latitude;
        $query->city = $request->city;
        if($request->country == 129):
            $query->state = $request->state_my;
        else:
            $query->state = $request->state;
        endif;
        $query->postcode = $request->postcode;
        $query->country = $request->country;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $delete_id = [$request->id];
        else:
            $delete_id = $request->selected;
        endif;

        UserAddressBook::whereIn('id', $delete_id)->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = UserAddressBook::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('recipient', 'LIKE', "%{$search}%");
            })
            ->when($request->state, function ($q) use ($request) {
                $q->where('state', $request->state);
            })
            ->when($request->type, function ($q) use ($request) {
                $q->where('user_type', $request->type);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($q) use ($request) {
                $q->where('user_id', $request->retailer);
            })
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                if($item->user_type == 'retailer'):
                    $user_name = Retailer::where('user_id', $item->user_id)->first()->first_name;
                else:
                    $user_name = User::find($item->user_id)->name;
                endif;

                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $item->username = $user_name;
                $item->state = config('staticdata.states.'.$item->state) ?? $item->state;
                $item->country = getCountryName($item->country);
            endforeach;
        endif;

        $fileName = 'user_address_books_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'user_address_book'),
            $fileName
        );
    }

    function dropdownList(Request $request)
    {
        $results = UserAddressBook::when($request->user_id, function($q) use ($request) {
                $q->where('user_id', $request->user_id);
            })
            ->when($request->user_type, function($q) use ($request) {
                $q->where('user_type', $request->user_type);
            })
            ->get();
            
        if($results->count() > 0):
            foreach ($results as $item):
                $state = config('staticdata.states.' . $item->state) ?? $item->state;
                $full_address = $item->address . ', ' . $item->city . ', ' . $item->postcode . ', ' . $state . ', ' . getCountryName($item->country);

                $item->full_address = $full_address;
            endforeach;
        else:
            $results = null;
        endif;

        return response()->json([
            'results' => $results,
        ]);
    }
}