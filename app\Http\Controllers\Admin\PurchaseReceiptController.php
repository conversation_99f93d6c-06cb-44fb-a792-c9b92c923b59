<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Traits\PointsTraits;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Models\PurchaseReceipt;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class PurchaseReceiptController extends Controller
{
    use PointsTraits;

    function index()
    {
        if (!checkPermission('view_purchase_receipts')):
            return redirect(route('index'))->with('error', __('You do not have permission to view purchase receipt.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('purchaseReceiptDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('purchaseReceiptExport'),
                'filename' => 'purchase_receipts'
            ],
            'approve_receipt' => [
                'text' => __('Approve Selected'),
            ],
            'reject_receipt' => [
                'text' => __('Reject Selected'),
            ]
        ];

        if(!checkPermission('delete_purchase_receipts')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_purchase_receipts')):
            unset($bulk_action['export']);
        endif;

        $point_convert = GlobalSettings::where('name', 'bookings_points_rm_ratio')->first()->value;

        $moduleID = 'purchase_receipts';
        return view('purchase-receipt', compact('bulk_action', 'moduleID', 'point_convert'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = PurchaseReceipt::when($request->search, function($q) use ($request) {
            $search = $request->input('search.value');
            $q->where('ref_no', 'LIKE', "%{$search}%");
        })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when(isset($request->user) && $request->user != 'all', function ($query) use ($request) {
                $query->where('user_id', $request->user);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();
            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="purchase_receiptsList">
                    <span></span>
                </label>';

                $image_src = GlobalFunction::createMediaUrl($item->image);
                $image = '<a href="' . $image_src . '" target="_blank" data-toggle="tooltip" data-title="' . __('Enlarge') . '">
                            <img src="' . $image_src . '" style="max-width:120px; max-height: 120px;">
                        </a>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_news_and_announcements')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_news_and_announcements')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $user = User::find($item->user_id);

                $param = [
                    $checkbox,
                    $user->name,
                    $image,
                    $item->ref_no,
                    config('staticdata.purchase_receipt_status')[$item->status],
                    $item->amount,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_purchase_receipts')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = PurchaseReceipt::find($request->id);
        if ($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;

        if ($data) {
            $data->user = User::find($data->user_id);
        }
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = PurchaseReceipt::find($id);
        else:
            if (!$request->has('image')):
                return GlobalFunction::sendSimpleResponse(false, __('Please select image.'));
            endif;

            $msg = __('Added!');
            $query = new PurchaseReceipt();
            $query->ref_no = GlobalFunction::generateRandomNumber();
        endif;

        if ($request->status == 'approve') {
            if (!$request->amount) {
                return GlobalFunction::sendSimpleResponse(false, __('Amount is required.'));
            }
        } else if ($request->status == 'reject') {
            if (!$request->reason) {
                return GlobalFunction::sendSimpleResponse(false, __('Reason is required.'));
            }
        }

        $query->user_id = $request->user_id;
        $query->status = $request->status;
        $query->reason = $request->reason ?? null;
        $query->amount = $request->amount ?? null;

        if ($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        $query->save();

        if ($query->status == 'approve') {
            $convert_rate = GlobalSettings::where('name', 'bookings_points_rm_ratio')->first()->value ?? 1;
            $point = intval($query->amount) * $convert_rate;

            $point_data = [
                'user_id' => $query->user_id,
                'type' => 'purchase_receipt',
                'type_id' => null,
                'points_prefix' => '+',
                'points' => $point,
                'status' => 'ready',
                'brand' => $query->user->brand
            ];
            $this->insertPointTransaction($point_data);
        }

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function approveReceipt(Request $request)
    {
        $ids = $request->ids;
        $ids = explode(',', $ids);

        if (!$request->amount) {
            return GlobalFunction::sendSimpleResponse(false, __('Amount is required.'));
        }

        foreach ($ids as $id) {
            $purchase_receipt = PurchaseReceipt::find($id);

            if (!$purchase_receipt) {
                return false;
            }

            $purchase_receipt->status = 'approve';
            $purchase_receipt->amount = $request->amount;
            $purchase_receipt->save();

            $convert_rate = GlobalSettings::where('name', 'bookings_points_rm_ratio')->first()->value ?? 1;
            $point = intval($purchase_receipt->amount) * $convert_rate;

            $point_data = [
                'user_id' => $purchase_receipt->user_id,
                'type' => 'purchase_receipt',
                'type_id' => null,
                'points_prefix' => '+',
                'points' => $point,
                'status' => 'ready',
                'brand' => $purchase_receipt->user->brand
            ];
            $this->insertPointTransaction($point_data);
        }

        return redirect()->route('purchaseReceipt')->with([
            'status' => true,
            'message' => 'Updated!'
        ]);
    }

    function rejectReceipt(Request $request)
    {
        $ids = $request->ids;
        $ids = explode(',', $ids);

        if (!$request->reason) {
            return GlobalFunction::sendSimpleResponse(false, __('Reason is required.'));
        }

        foreach ($ids as $id) {
            $purchase_receipt = PurchaseReceipt::find($id);

            if (!$purchase_receipt) {
                return false;
            }

            $purchase_receipt->status = 'reject';
            $purchase_receipt->reason = $request->reason;
            $purchase_receipt->save();
        }

        return redirect()->route('purchaseReceipt')->with([
            'status' => true,
            'message' => 'Updated!'
        ]);
    }

    function delete(Request $request)
    {
        if($request->id):
            PurchaseReceipt::find($request->id)->delete();
        else:
            PurchaseReceipt::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = PurchaseReceipt::when($request->search, function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('ref_no', 'LIKE', "%{$search}%");
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when(isset($request->user_id) && $request->user_id != 'all', function ($query) use ($request) {
                $query->where('user_id', $request->brand);
            })
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));

                $item->username = User::find($item->user_id)->name;
                $item->status = config('staticdata.purchase_receipt_status')[$item->status];
            endforeach;
        endif;

        $fileName = 'purchase_receipt_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'purchase_receipt'),
            $fileName
        );
    }
}
