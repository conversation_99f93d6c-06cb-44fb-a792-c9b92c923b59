<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\Appointments;
use App\Models\Constants;
use App\Models\CronjobLogs as Cronjob;
use App\Models\GlobalFunction;
use App\Emails\AppointmentReminder;

class AppointmentReminders extends Command
{
    protected $signature = 'appointment_reminder:cron';
    protected $description = 'send email reminder a day before of confirmed booking date';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $reminder_period = global_settings('appointment_reminder_duration')." ".global_settings('appointment_reminder_unit');
        $reminder_date = strtotime("+".$reminder_period);

        $appointments = Appointments::with(['user', 'doctor'])
            ->where('status', Constants::orderAccepted)
            ->whereNull('reminder_sent')
            ->get();
        
        $email_recipient = "";
        if(count($appointments) > 0):
            foreach($appointments as $appointment):
                $appointment_date = $appointment->date;
                $appointment_time = $appointment->time;
                $start_time = explode(" - ", $appointment_time);
                $start_time = current($start_time);
                $check_date = date("Y-m-d H:i:s", strtotime($appointment_date." ".$start_time." -".$reminder_period));

                if($check_date <= date("Y-m-d H:i:s")):
                    $user_name = $appointment->user->fullname;
                    $user_email = $appointment->user->email_address;
                    $doctor_name = $appointment->doctor->name;
                    $doctor_email = $appointment->doctor->email_address;
                    $appointment_id = $appointment->id;
                    $appointment_number = $appointment->appointment_number;
                    $appointment_date = Carbon::parse($appointment_date)->format('d-m-Y');

                    $email_recipient .= $appointment_id."|".$appointment_number." - ".$user_email."|".$doctor_email.", ";

                    $title = "Appointment Reminder : " . $appointment_number;
                    $message = "You have an appointment on ".$appointment_date." at ".$appointment_time.".";
                    // Notification to user
                    $notify_data = [
                        'notify_type' => 'user',
                        'title' => $title,
                        'message' => $message,
                        'type' => 'appointment',
                        'type_id' => $appointment_id,
                        'user' => $appointment->user,
                    ];
                    GlobalFunction::sendNotification($notify_data);

                    // Notification to doctor
                    $notify_data = [
                        'notify_type' => 'doctor',
                        'title' => $title,
                        'message' => $message,
                        'type' => 'appointment',
                        'type_id' => $appointment_id,
                        'user' => $appointment->doctor,
                    ];
                    GlobalFunction::sendNotification($notify_data);

                    $appointment->reminder_sent = date("Y-m-d H:i:s");
                    $appointment->save();

                    // email
                    $email_data = [
                        'type' => 'patient',
                        'user_name' => $user_name,
                        'user_email' => $user_email,
                        'appointment_date' => $appointment_date,
                        'appointment_time' => $appointment_time,
                    ];
                    Mail::to($user_email)->send(new AppointmentReminder($email_data));
                    // END notification to user

                    $email_data = [
                        'type' => 'doctor',
                        'user_name' => $doctor_name,
                        'user_email' => $doctor_email,
                        'appointment_date' => $appointment_date,
                        'appointment_time' => $appointment_time,
                    ];
                    Mail::to($doctor_email)->send(new AppointmentReminder($email_data));
                    // END notification to doctor
                endif;
            endforeach;

            $email_recipient = substr($email_recipient, 0, -2);
        endif;
        
        // cronjob log
        if(isset($email_recipient) && $email_recipient):
            Cronjob::create([
                "type" => "appointment reminder",
                "recipient" => $email_recipient
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}