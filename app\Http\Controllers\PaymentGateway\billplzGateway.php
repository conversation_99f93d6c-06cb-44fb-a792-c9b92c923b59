<?php
namespace App\Http\Controllers\PaymentGateway;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\User;
use App\Models\PaymentTransactions;
use App\Traits\PaymentGatewaysTraits;
use App\Traits\WalletsTraits;
use App\Traits\EmailTraits;

class billplzGateway extends Controller
{
    use PaymentGatewaysTraits;
    use WalletsTraits;
    use EmailTraits;

    function link()
    {
        if(global_settings('billplz_sandbox') == 1):
            $payment_url = "https://www.billplz-sandbox.com/";
        else:
            $payment_url = "https://www.billplz.com/";
        endif;

        $payment_url .= "api/v3/bills/";
        return $payment_url;
    }

    function url($data)
    {
        $payment_url = $this->link();
        $billplzCollection = global_settings('billplz_collection');
        $billplzSecret = global_settings('billplz_secret');
        if(is_array($data)):
            $data = (object) $data;
        endif;

        switch($data->type):
            case 'user-topup-wallet': $description = 'Topup Wallet'; break;
        endswitch;

        $user = User::find($data->user_id);
        $payment_data = [
            'collection_id' => $billplzCollection,
            'description' => $description,
            'email' => $user->email_address,
            'name' => $user->first_name.' '.$user->last_name,
            'amount' => $data->amount * 100,
            'redirect_url' => route('payment.response', ['gateway' => 'billplz']),
            'callback_url' => route('payment.callback', ['gateway' => 'billplz']),
            "reference_1_label" => "Payment ID",
            "reference_1" => $data->id,
        ];

        $response = Http::withBody(json_encode($payment_data), 'application/json')
            ->withBasicAuth($billplzSecret, '')
            ->post($payment_url)->json();
        if($response):
            if(isset($response['error'])):
                return GlobalFunction::sendSimpleResponse(false, $response['error']['message']);
            else:
                $update_data = [ 'gateway_ref_id' => $response['id'] ];
                $payment = $this->updatePaymentTransaction($payment, $update_data);

                return GlobalFunction::sendDataResponse(true, "Url retrieved", $response['url']);
            endif;
        else:
            return GlobalFunction::sendSimpleResponse(false, "Url not found.");
        endif;
    }

    function response($request)
    {
        $billplz_data = $request->billplz;
        $billplz_id = $billplz_data['id'];
        if(!$billplz_id):
            return GlobalFunction::sendSimpleResponse(false, 'Transaction not found');
        endif;

        $payment = PaymentTransactions::where('gateway', 'billplz')
            ->where('gateway_ref_id', $billplz_id)
            ->first();
        if(!$payment):
            return GlobalFunction::sendSimpleResponse(false, 'Transaction not found'); 
        endif;

        if($payment->status != Constants::paymentStatus['pending']):
            return GlobalFunction::sendSimpleResponse(false, 'This transaction has been processed before. Current status is '.$payment->status);
        endif;
        
        $payment_id = $payment->id;
        $billplzSignature = global_settings('billplz_signature');
        $paid = $billplz_data['paid'];
        $paid_at = $billplz_data['paid_at'];
        $x_signature = $billplz_data['x_signature'];
        $signature_generate = 'billplzid'.$billplz_id.'|billplzpaid_at'.$paid_at.'|billplzpaid' . $paid;
        $check_signature = hash_hmac('sha256', $signature_generate, $billplzSignature);

        if($x_signature === $check_signature && $paid == 'true'):
            $response = $this->updatePaymentSuccess($payment, $request->all(), $tranID);
            $msg = $response['msg'];
            $data = $response['data'];
        else:
            $payment = $this->updatePaymentFailed($payment, $request->all(), $billplz_id);
            $msg = __('Payment failed.');
        endif;

        $order_data = [
            'order_no' => $order_no,
            'type' => $trans->type,
            'payment_transactions' => $trans,
            'status' => $status,
            'transID' => $billplz_id,
            'details' => $request->billplz,
            'gateway_id' => Constants::billplz
        ];
        return $this->updateOrderStatus($order_data);
    }

    function callback($request)
    {
        $billplzSecret = global_settings('billplz_secret');
        $billplzSignature = global_settings('billplz_signature');

        $id = $request->id;
        $collection_id = $request->collection_id;
        $paid = $request->paid;
        $state = $request->state;
        $amount = $request->amount;
        $paid_amount = $request->paid_amount;
        $due_at = $request->due_at;
        $email = $request->email;
        $mobile = $request->mobile;
        $name = $request->name;
        $url = $request->url;
        $paid_at = $request->paid_at;
        $x_signature = $request->x_signature;
        $signature_generate = 'amount' . $amount . '|collection_id' . $collection_id . '|due_at' . $due_at . '|email' . $email . '|id' . $id . '|mobile' . $mobile . '|name' . $name . '|paid_amount' . $paid_amount . '|paid_at' . $paid_at . '|paid' . $paid . '|state' . $state . '|url' . $url;
        $check_signature = hash_hmac('sha256', $signature_generate, $billplzSignature);

        $payment_url .= $this->link().$id;
        $response = Http::withBasicAuth($billplzSecret, '')->get($payment_url)->json();
        if($response):
            $trans = PaymentTransactions::where('gateway', 'billplz')
                ->where('gateway_ref_id', $id)
                ->first();
            $order_no = $trans->type_ref_id ?? null;
            if($order_no):
                if($x_signature === $check_signature && $paid == 'true'):
                    $status = "success";
                else:
                    $status = "failed";
                endif;

                $order_data = [
                    'order_no' => $order_no,
                    'type' => $trans->type,
                    'payment_transactions' => $trans,
                    'status' => $status,
                    'transID' => $id,
                    'details' => $request->all(),
                    'gateway_id' => Constants::billplz
                ];
                return $this->updateOrderStatus($order_data, 'callback');
            endif;
        endif;
    }
}