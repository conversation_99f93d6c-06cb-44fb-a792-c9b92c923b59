<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Users extends Model
{
    use HasFactory;
    public $table = "users";
    protected $fillable = [
        'deleted_at',
    ];

    public function appointments()
    {
        return $this->hasMany(Appointments::class, 'user_id', 'id');
    }

    public function age()
    {
        return Carbon::parse($this->attributes['dob'])->age;
    }

    public function membership_tiers()
    {
        return $this->belongsTo(MembershipTiers::class, 'membership_tier', 'id');
    }

    public function retailer()
    {
        return $this->belongsTo(Retailer::class, 'retailer_id');
    }
}
