<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesChannel extends Model
{
    use HasFactory;
    public $table = "sales_channels";

    protected $appends = ['channel_name'];


    public function adsSpendingTransactions()
    {
        return $this->hasMany(AdsSpendingTransaction::class);
    }

    public function mainChannel()
    {
        return $this->belongsTo(SalesChannel::class, 'channel_id', 'id');
    }

    public function getChannelNameAttribute()
    {
        if($this->channel_id){
            return $this->mainChannel->title .' ('. $this->title .')';
        }
        return $this->title;
    }


}
