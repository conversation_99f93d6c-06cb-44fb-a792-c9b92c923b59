<?php
namespace App\Http\Controllers\Api;

use App\Models\PushNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\GlobalFunction;
use App\Models\BookingItemChanges;

class ApiNotifications
{
    function getType()
    {
        $type = request()->route()->getPrefix();
        if(strpos($type, 'user') !== false):
            $type = 'user';
        else:
            $type = 'doctor';
        endif;

        return $type;
    }

    function listing(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $type = $this->getType();
        $per_page = $request->per_page ?? 10;
        $result = PushNotification::where('user_type', $type)
            ->where(function($q) use($request) {
                $q->where('user_id', auth()->user()->id);
            })
            ->orderBy('id', 'desc')
            ->paginate($per_page);

        $result->map(function($item) {
            if($item->extra_data):
                $item->extra_data = json_decode($item->extra_data);
            endif;

            if($item->type == 'stringing_booking_changes'):
                // check if member have responded
                $changes_id = $item->extra_data->changes_id;
                $changes = BookingItemChanges::find($changes_id);
                
                if($changes && $changes->acceptance_status != 'pending'):
                    $item->member_responded = true;
                else:
                    $item->member_responded = false;
                endif;
            endif;
            return $item;
        });

        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function detail(Request $request)
    {
        $rules = [ 'id' => 'required' ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $result = PushNotification::find($request->id);
        if($result->extra_data):
            $result->extra_data = json_decode($result->extra_data);
        endif;
        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function markAsRead(Request $request)
    {
        $rules = [ 'id' => 'required' ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $type = $this->getType();
        $id = $request->id;
        if($id == 'all'):
            PushNotification::where('user_id', auth()->user()->id)
                ->where('user_type', $type)
                ->update([ 'read_at' => date('Y-m-d H:i:s') ]);

            return GlobalFunction::sendSimpleResponse(true, __('All notifications are marked as read.'));
        else:
            $result = PushNotification::where('id', $id)
                ->update([ 'read_at' => date('Y-m-d H:i:s') ]);

            return GlobalFunction::sendSimpleResponse(true, __('The notification is marked as read.'));
        endif;
    }
    
    function totalUnread(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $type = $this->getType();
        $result = PushNotification::where('user_type', $type)
            ->where(function($q) use($request) {
                $q->where('user_id', auth()->user()->id);
            })
            ->whereNull('read_at')
            ->count();
        $data = [
            'total_unread' => $result
        ];

        return GlobalFunction::sendDataResponse(true, "", $data);
    }
}