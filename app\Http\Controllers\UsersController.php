<?php
namespace App\Http\Controllers;

use App\Models\Constants;
use App\Models\Doctors;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Models\PasswordReset;
use App\Models\User;
use App\Models\Users;
use App\Models\UserWalletRechargeLogs;
use App\Models\UserWalletStatements;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;

class UsersController extends Controller
{
    function addMoneyToUserWallet(Request $request)
    {
        $rules = [
            'user_id' => 'required',
            'amount' => 'required',
            'transaction_id' => 'required',
            'transaction_summary' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $messages = $validator->errors()->all();
            $msg = $messages[0];
            return response()->json(['status' => false, 'message' => $msg]);
        }

        $user = Users::find($request->user_id);
        if ($user == null) {
            return response()->json(['status' => false, 'message' => "User doesn't exists!"]);
        }
        $user->wallet = $user->wallet + $request->amount;
        $user->save();
        // Adding Statement entry
        GlobalFunction::addUserStatementEntry(
            $user->id,
            null,
            $request->amount,
            Constants::credit,
            Constants::deposit,
            $request->transaction_summary
        );
        // Recharge Wallet History
        $rechargeLog = new UserWalletRechargeLogs();
        $rechargeLog->user_id = $user->id;
        $rechargeLog->amount = $request->amount;
        $rechargeLog->gateway = $request->gateway;
        $rechargeLog->transaction_id = $request->transaction_id;
        $rechargeLog->transaction_summary = $request->transaction_summary;
        $rechargeLog->save();

        return GlobalFunction::sendSimpleResponse(true, 'Money added to wallet successfully!');
    }

    // WEB
    public function emailVerify($token, $sent='')
    {
        $users = Users::where('email_verification_token', $token)->first();
        if(!$users):
            $msg = __('Account not found.');
        else:
            if(!$sent):
                if($users->is_block):
                    $msg = __('This account is blocked.');
                elseif($users->email_verification_exp_date < date('Y-m-d H:i:s')):
                    $msg = __('The link is expired.<br>Please click on the button below to send the verification email again.');
                    $btn_verify = 1;
                elseif($users->email_verification_date):
                    $msg = __('This account has been verified previously.');
                endif;
            else:
                $msg = __('An email have sent to :email.<br>Please login to your email and click on the link or button to verify your account.', ['email' => $users->email_address]);
            endif;
        endif;

        if(!isset($msg)):
            $users->email_verification_date = date('Y-m-d H:i:s');
            $users->email_verification_exp_date = null;
            $users->email_verification_token = null;
            $users->save();
            
            $msg = __('Your account is verified.<br>You may login through the app now.');
        endif;

        return view('non-auth.email-verified', [
            'token' => $token,
            'msg' => $msg,
            'btn_verify' => $btn_verify ?? null
        ]);
    }

    public function passwordResetVerify($token = '')
    {
        if($token):
            $users = PasswordReset::with(['user'])->where('token', $token)->first();
            if(!$users):
                $msg = __('Account not found or the link is not available.');
            else:
                if($users->user->is_block):
                    $msg = __('This account is blocked.');
                elseif($users->expired_date < date('Y-m-d H:i:s')):
                    $msg = __('The link is expired.<br>Please request through the app again.');
                endif;
            endif;
        else:
            $msg = __('Your password has been reset.<br>You may login through the app now.');
        endif;

        return view('non-auth.reset-password', [
            'token' => $token,
            'msg' => $msg ?? null,
            'url' => route('passwordReset')
        ]);
    }

    public function passwordReset(Request $request)
    {
        $users = PasswordReset::with(['user'])->where('token', $request->token)->first();
        if(!$users):
            return GlobalFunction::sendSimpleResponse(false, 'Account not found or the link is not available.');
        endif;

        $rules = [
            'password' => 'required|same:confirm_password'
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        User::where('id', $users->user->id)
            ->update(['password' => Hash::make($request->password)]);

        PasswordReset::where('token', $request->token)->delete();

        return GlobalFunction::sendSimpleResponse(true, route('passwordResetVerify'));
    }
}