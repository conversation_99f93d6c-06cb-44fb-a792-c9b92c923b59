<?php
namespace App\Http\Controllers\PaymentGateway;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\User;
use App\Models\PaymentTransactions;
use App\Traits\PaymentGatewaysTraits;
use App\Traits\WalletsTraits;
use App\Traits\EmailTraits;
use Killallskywalker\Eghl\Facades\Eghl;

class eghlGateway extends Controller
{
    use PaymentGatewaysTraits;
    use WalletsTraits;
    use EmailTraits;

    function url($data)
    {
        $paymentData = [
            'TransactionType' => 'SALE',
            'PymtMethod'     => 'ANY',
            'PaymentID'       => $data->payment_ref_no,
            'OrderNumber'     => $data->order->order_no, // Replace with your order number
            'PaymentDesc'     => 'Payment for Order '.$data->order->order_no,
            'Amount'          => number_format($data->amount, 2, ".", ""), // 0.14 – simulate failed , 0.53 – simulate no response from gateway
            'CurrencyCode'    => 'MYR', // Replace with the correct currency code
            'CustIP'          => $_SERVER['REMOTE_ADDR'], // Replace with customer IP
            'CustEmail'       => $data->order->email_address, // Replace with customer email
            'CustName'        => $data->order->name, // Replace with customer name
            'CustPhone'       => $data->order->phone_number, // Replace with customer phone
        ];
        $paymentUrl = Eghl::processPaymentRequest($paymentData);

        return $paymentUrl;
    }

    function response($request)
    {
        $response = $request->all();
        // Example response data
        //             "TransactionType" => "SALE"
        //             "PymtMethod" => "CC"
        //             "ServiceID" => "sit"
        //             "PaymentID" => "67c67dc181573"
        //             "OrderNumber" => "ORDER123"
        //             "Amount" => "0.14"
        //             "CurrencyCode" => "MYR"
        //             "HashValue" => "2a16b715a1ea7ae2a302c48f5996d56a51ad3add13fa82956f435cc794cc2676"
        //             "HashValue2" => "81bbc8dfa384cd90af3cb9b2899a7d555b5faf82d1523da4836945927702b9c5"
        //             "TxnID" => "sit000000067c67dc181573"
        //             "Acquirer" => "HostSimHFM"
        //             "IssuingBank" => null
        //             "TxnStatus" => "1"
        //             "TxnMessage" => "Transaction Failed"
        //             "AuthCode" => null
        //             "BankRefNo" => "sit000000067c67dc181573"
        //             "RespTime" => "2025-03-04 12:13:05"
        //             "CardNoMask" => "444433XXXXXX1111"
        //             "CardHolder" => "Lucas"
        //             "CardType" => "VISA"
        //             "LocalForeign" => "FOREIGN"
        //             "DebitCredit" => "VISA CREDIT"
        //             "CardExp" => "203701"

        $transStatus = $response['TxnStatus'];
        $transID = $response['TxnID'];
        $paymentID = $response["PaymentID"];
        $payment = PaymentTransactions::where('gateway', 'eghl')
            ->where('payment_ref_no', $paymentID)
            ->first();

        if($transStatus == 2 || $payment->status != Constants::paymentStatus['pending']):
            $status = ($payment->status == Constants::paymentStatus['failed']) ? 'failed' : 'success';
            return $this->returnPaymentResponse($status, $payment);
        endif;

        //0 = success ,1 = failed , 2 = pending
        if($transStatus == 0):
            $status = 'success';
            $this->updatePaymentSuccess($payment, $request->all(), $transID);
        else:
            $status = 'failed';
            $this->updatePaymentFailed($payment, $request->all(), $transID);
        endif;

        return $this->returnPaymentResponse($status, $payment);
    }
}