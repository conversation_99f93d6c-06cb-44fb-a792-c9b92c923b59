<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\UserPointTransaction;

class ApiPoints
{
    function listing(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $type = $request->type ?? null;
        $per_page = $request->per_page ?? 10;
        $data = UserPointTransaction::where('user_id', auth()->user()->id)
            ->when($type, function ($q) use ($type) {
                if ($type == 'spent') {
                    $q->where('points_prefix', '-');
                } elseif ($type == 'earned') {
                    $q->where('points_prefix', '+');
                }
            })
            ->orderBy('updated_at', 'desc')
            ->paginate($per_page);
        
        if($data->count() > 0):
            $bymonth = [];
            foreach($data as $item):
                $month = date('F Y', strtotime($item['created_at']));

                if(!isset($bymonth[$month])):
                    $bymonth[$month] = [];
                endif;
                $bymonth[$month][] = $item;
            endforeach;

            $bymonth_list = [];
            foreach($bymonth as $key => $item):
                foreach($item as $point_key => $point_item):
                    $item[$point_key]['voucher_code'] = null;
                    $item[$point_key]['booking_ref_no'] = null;
                    
                    if($point_item['type'] == 'redeem_voucher'):
                        $item[$point_key]['voucher_code'] = $point_item->voucher->voucher_code ?? null;
                    elseif($point_item['type'] == 'booking'):
                        $item[$point_key]['booking_ref_no'] = $point_item->booking->ref_no;
                    endif;

                    if($point_item['point_expiry_balance'] <= 0):
                        $item[$point_key]['expired_date'] = null;
                    endif;
                    unset($item[$point_key]['voucher']);
                    unset($item[$point_key]['booking']);
                endforeach;

                $bymonth_list[] = [
                    'month' => $key,
                    'data' => $item,
                ];
            endforeach;

            $data->setCollection(collect($bymonth_list));
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail(Request $request)
    {
        $data = UserPointTransaction::where('user_id', auth()->user()->id)
            ->find($request->id);
        
        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}