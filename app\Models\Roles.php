<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Roles extends Model
{
    use HasFactory;
    public $table = "roles";

    public function admin()
    {
        return $this->hasMany(Admin::class, 'role_id', 'id');
    }

    public function retailer()
    {
        return $this->hasMany(Retailer::class, 'role_id', 'id');
    }
}    