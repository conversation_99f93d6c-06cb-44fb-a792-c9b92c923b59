<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Carts;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\CartReminderNotification;
use App\Traits\PushNotificationTraits;

class CartReminders extends Command
{
    use PushNotificationTraits;

    protected $signature = 'cart_reminder';
    protected $description = 'send notification reminder after cart created';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $cart_reminder = global_settings('cart_reminder');

        $carts = Carts::whereHas('user', function($query){
                $query->where('deleted_at', null);
            })
            ->whereHas('products', function($query) use($cart_reminder){
                $query->whereDate('created_at', '<=', Carbon::now()->subDays($cart_reminder));
            })
            ->get();

        $reminder_recipient = [];
        foreach($carts as $cart):
            $normal_product = $cart->products->where('cart_bundle_id', null)->pluck('product.name')->toArray();
            $bundle_product = $cart->products->where('cart_bundle_id', '!=', null)->groupBy('cart_bundle_id')->map(function($item){
                return $item->first()->cart_bundle->bundle->name;
            })->toArray();

            $products = '';
            if($normal_product):
                $products .= implode(', ', $normal_product);
            endif;
            if($bundle_product):
                if($products):
                    $products .= ', ';
                endif;
                $products .= implode(', ', $bundle_product);
            endif;

            Notification::route('whatsapp', $cart->user->phone_number)
                ->notify(new CartReminderNotification($cart->user, $products));
            $reminder_recipient[] = $cart->user->id.": ".$cart->user->phone_number;
        endforeach;

        if($reminder_recipient):
            Cronjob::create([
                "type" => "cart reminder",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}