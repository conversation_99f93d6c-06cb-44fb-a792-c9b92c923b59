<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\NewsAndAnnouncement;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class NewsAndAnnouncementController extends Controller
{
    function index()
    {
        if (!checkPermission('view_news_and_announcements')):
            return redirect(route('index'))->with('error', __('You do not have permission to view news and announcement.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('newsAndAnnouncementDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('newsAndAnnouncementExport'),
                'filename' => 'news_and_announcements'
            ]
        ];

        if (!checkPermission('delete_news_and_announcements')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_news_and_announcements')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'news_and_announcements';
        return view('news-and-announcement', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = NewsAndAnnouncement::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
            $q->where('title', 'LIKE', "%{$search}%");
        })
            // ->whereIn('brand', userBrand())
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function ($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                        <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="newsAndAnnouncementList">
                        <span></span>
                    </label>';

                $image_src = GlobalFunction::createMediaUrl($item->image);
                $image = '<a href="' . $image_src . '" target="_blank" data-toggle="tooltip" data-title="' . __('Enlarge') . '">
                            <img src="' . $image_src . '" style="max-width:120px; max-height: 120px;">
                        </a>';

                if (!checkPermission('edit_news_and_announcements')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                            <label class="switch mb-0">
                                <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                                <span class="slider round"></span>
                            </label>
                        </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_news_and_announcements')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_news_and_announcements')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $image,
                    $item->title,
                    // config('staticdata.brand')[$item->brand],
                    $item->start_date && $item->end_date ? Carbon::parse($item->start_date)->format('d-m-Y') . ' to ' . Carbon::parse($item->end_date)->format('d-m-Y') : '-',
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_news_and_announcements')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = NewsAndAnnouncement::find($request->id);
        if ($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = NewsAndAnnouncement::find($id);
        else:
            if (!$request->has('image')):
                return GlobalFunction::sendSimpleResponse(false, __('Please select image.'));
            endif;

            $msg = __('Added!');
            $query = new NewsAndAnnouncement();
        endif;

        if (isset($request->active_period)) {
            $active_period = explode(' to ', $request->active_period);
            $query->start_date = Carbon::parse($active_period[0])->format('Y-m-d');
            $query->end_date = Carbon::parse($active_period[1])->format('Y-m-d');
        }

        $query->title = $request->title;
        // $query->brand = $request->brand ?? null;
        $query->content = $request->content;
        $query->is_active = $request->status ?? 0;

        if ($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            NewsAndAnnouncement::find($request->id)->delete();
        else:
            NewsAndAnnouncement::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = NewsAndAnnouncement::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = NewsAndAnnouncement::when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('title', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function ($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'news_and_announcement_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'news_and_announcement'),
            $fileName
        );
    }
}
