<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integration\WoocommerceController;

class ApiWoocommerce extends Controller
{
    function getOrders(Request $request)
    {
        if ($request->route()->parameters['brand'] == ':brand') {
            return GlobalFunction::sendSimpleResponse(false, __('Brand not found.'));
        }

        $requestData = $request->all();
        $requestData['brand'] = $request->route()->parameters['brand'];

        return (new WoocommerceController)->getOrderList($requestData);
    }

    function createOrders(Request $request)
    {
        if ($request->route()->parameters['brand'] == ':brand') {
            return GlobalFunction::sendSimpleResponse(false, __('Brand not found.'));
        }

        $requestData = $request->all();
        $requestData['brand'] = $request->route()->parameters['brand'];

        return (new WoocommerceController)->createOrder($requestData);
    }

    function webhookUpdateOrder(Request $request)
    {
        Log::info($request->all());
    }
}
