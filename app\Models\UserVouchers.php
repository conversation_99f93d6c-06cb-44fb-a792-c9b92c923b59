<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Products;

class UserVouchers extends Model
{
    use HasFactory;
    public $table = "user_vouchers";

    protected $fillable = [
        'user_id',
        'voucher_id',
        'voucher_code',
        'voucher_value',
        'portal',
        'effective_start_date',
        'effective_end_date',
        'voucher_data',
        'used_date'
    ];

    public function voucher()
    {
        return $this->belongsTo(RedemptionVouchers::class, 'voucher_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getVoucherDetailAttribute()
    {
        return json_decode($this->voucher_data);
    }

    public function getVoucherProductsAttribute()
    {
        if($this->free_product):
            $free_product = json_decode($this->free_product);
            $product_list = [];
            $product_ids = array_values(array_unique(array_map(function($item) {
                return $item->product_id;
            }, $free_product)));
            $variation_ids = array_values(array_unique(array_map(function($item) {
                return $item->variation_id;
            }, $free_product)));

            $product_data = Products::whereIn('id', $product_ids)->get()->keyBy('id');
            if(count($variation_ids)):
                $variation_data = ProductVariations::whereIn('id', $variation_ids)->get()->keyBy('id');
            endif;

            foreach($free_product as $key => $item):
                if($item->variation_id):
                    $image = $variation_data[$item->variation_id]->image_url;
                else:
                    $image = $product_data[$item->product_id]->single_image_url;
                endif;

                $product_list[$item->id] = [
                    'image' => $image,
                    'product_name' => $product_data[$item->product_id]->name,
                    'product_sku' => $product_data[$item->product_id]->sku,
                    'variation_name' => $variation_data[$item->variation_id]->variation_name ?? null,
                    'variation_sku' => $variation_data[$item->variation_id]->sku ?? null,
                    'quantity' => $item->quantity,
                ];
            endforeach;

            return $product_list;
        endif;
    }
}
