<?php

namespace App\Http\Controllers\Api;

use App\Models\Pages;
use App\Models\Faqs;
use App\Models\Banners;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;

use App\Models\User;
use App\Models\Doctors;

class ApiPages
{
    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function view(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $type = $request->type;

        $result = Pages::where('brand', $brand)
            ->first();

        $data = null;
        $type = $request->type;
        if ($type != "privacy-policy" && $type != "terms-of-use" && $type != "about-us"):
            return GlobalFunction::sendSimpleResponse(false, __('Page not found.'));
        endif;

        if ($type == "privacy-policy"):
            $data = $result->privacy;
        elseif ($type == "terms-of-use"):
            $data = $result->termsofuse;
        elseif ($type == "about-us"):
            $data = $result->aboutus;
        endif;

        if ($data):
            $data = [
                'content' => $data,
            ];
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function faqs(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $type = $request->type;

        $data = Faqs::where('is_active', 1)
            ->where('brand', $brand)
            ->when($type, function ($q) use ($type) {
                return $q->where('type', $type);
            })
            ->get();
        if ($data->count() > 0):
            $data->makeHidden([
                'category_id',
            ]);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function banners(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $type = $request->type;

        $data = Banners::where('is_active', 1)
            ->where('brand', $brand)
            ->when($type, function ($q) use ($type) {
                return $q->where('type', $type);
            })
            ->orderBy('sort_order', 'asc')
            ->get();

        if ($data->count() > 0):
            $data->map(function ($item) {
                if ($item->image):
                    $item->image = GlobalFunction::createMediaUrl($item->image);
                endif;
                return $item;
            });
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function mobileVersions()
    {
        $type = request()->path();
        if (strpos($type, 'user/') !== false):
            $data = [
                'ios_version' => global_settings('ios_version_no'),
                'android_version' => global_settings('android_version_no'),
            ];
        else:
            $data = [
                'ios_version' => global_settings('staff_ios_version_no'),
                'android_version' => global_settings('staff_android_version_no'),
            ];
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function settings()
    {
        $data = [
            'support_email' => global_settings('support_email'),
            'support_contact' => global_settings('support_contact'),
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
