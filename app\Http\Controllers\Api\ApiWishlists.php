<?php
namespace App\Http\Controllers\Api;

use App\Models\Constants;
use App\Models\UserWishlist;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\UserPointTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiWishlists
{
    function listing(Request $request)
    {
        if(!auth()->user()):
            return GlobalFunction::sendSimpleResponse(false, 'You are not logged in.');
        endif;

        $per_page = $request->per_page ?? 10;

        $data = UserWishlist::where('user_id', auth()->user()->id)
            ->with('product')
            ->orderBy('created_at', 'desc')
            ->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function add(Request $request)
    {
        $validate = $this->validateProduct($request);

        if (isset($validate['error'])) {
            return GlobalFunction::sendSimpleResponse(false, $validate['error']);
        }

        $user = Auth::user();
        $user_wishlist = new UserWishlist();
        $user_wishlist->user_id = $user->id;
        $user_wishlist->product_id = $validate['product_id'];
        $user_wishlist->save();

        return GlobalFunction::sendDataResponse(true, __('Wishlist added successfully.'), $user_wishlist);
    }

    function validateProduct($data)
    {
        $product_id = $data['product_id'] ?? null;

        if (!$product_id) {
            return ['error' => __('Product is required.')];
        }

        return $data = [
            'product_id' => $product_id,
        ];
    }

    function delete($id)
    {
        $rules = [
            'product_id' => 'required',
        ];

        $request = [
            'product_id' => $id
        ];

        $validator = Validator::make($request, $rules);
        $result = GlobalFunction::showValidatorMsg($validator);

        if ($result):
            return $result;
        endif;

        $user_wishlist = UserWishlist::where('product_id',$id)->where('user_id', auth()->user()->id)->first();
        $user_wishlist->delete();

        return GlobalFunction::sendSimpleResponse(true, __('Wishlist is deleted.'));
    }
}