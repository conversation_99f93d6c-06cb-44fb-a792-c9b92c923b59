<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\UserAddressBook;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiUserAddressBook
{
    function listing(Request $request)
    {
        $per_page = $request->per_page ?? 10;
        $search = $request->search ?? null;

        $user = auth()->user();
        $address = $user->userAddressBooks()
            ->where(function ($q) use ($search) {
                $q->where('recipient', 'LIKE', "%{$search}%")
                    ->orWhere('phone_number', 'LIKE', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $address);
    }

    function detail($id)
    {
        $data = UserAddressBook::find($id);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function create(Request $request)
    {
        $validate = $this->validateAddress($request);
        if (isset($validate['error'])) {
            return GlobalFunction::sendSimpleResponse(false, $validate['error']);
        }

        $user = Auth::user();
        $address_book = new UserAddressBook();
        $address_book->user_id = $user->id;
        $address_book->label = $validate['label'];
        $address_book->recipient = $validate['recipient'];
        $address_book->phone_number = $validate['phone_number'];
        $address_book->address = $validate['address'];
        $address_book->city = $validate['city'];
        $address_book->postcode = $validate['postcode'];
        $address_book->state = $validate['state'];
        $address_book->latitude = $validate['latitude'];
        $address_book->longitude = $validate['longitude'];
        $address_book->country = $validate['country'];
        $address_book->save();

        return GlobalFunction::sendDataResponse(true, __('Address book created successfully'), $address_book);
    }

    function validateAddress($data)
    {
        $recipient = $data['recipient'] ?? null;
        $phone_number = $data['phone_number'] ?? null;
        $address = $data['address'] ?? null;
        $city = $data['city'] ?? null;
        $state = $data['state'] ?? null;
        $postcode = $data['postcode'] ?? null;
        $label = $data['label'] ?? null;
        $latitude = $data['latitude'] ?? null;
        $longitude = $data['longitude'] ?? null;
        $country = $data['country'] ?? null;

        if (!$recipient) {
            return ['error' => __('Recipient is required.')];
        }
        if (!$phone_number) {
            return ['error' => __('Phone number is required.')];
        }
        if (!$address) {
            return ['error' => __('Address is required.')];
        }
        if (!$city) {
            return ['error' => __('City is required.')];
        }
        if (!$state) {
            return ['error' => __('State is required.')];
        }
        if (!$postcode) {
            return ['error' => __('Postcode is required.')];
        }
        if (!$latitude) {
            return ['error' => __('Latitude is required.')];
        }
        if (!$longitude) {
            return ['error' => __('Longitude is required.')];
        }
        if (!$country) {
            return ['error' => __('Country is required.')];
        }

        return $data = [
            'label' => $label,
            'recipient' => $recipient,
            'phone_number' => $phone_number,
            'address' => $address,
            'city' => $city,
            'state' => $state,
            'postcode' => $postcode,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'country' => $country
        ];
    }

    function updateAddress(Request $request)
    {
        $rules = [
            'id' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);

        if ($result):
            return $result;
        endif;

        $id = $request->id;
        $address = UserAddressBook::find($id);

        if (isset($request->recipient) && $request->recipient) {
            $address->recipient = $request->recipient;
        }

        if (isset($request->phone_number) && $request->phone_number) {
            if (!GlobalFunction::validatePhoneNumber($request->phone_number)):
                return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
            endif;
            $address->phone_number = $request->phone_number;
        }

        if (isset($request->address) && $request->address) {
            $address->address = $request->address;
        }

        if (isset($request->city) && $request->city) {
            $address->city = $request->city;
        }

        if (isset($request->postcode) && $request->postcode) {
            $address->postcode = $request->postcode;
        }

        if (isset($request->state) && $request->state) {
            $address->state = $request->state;
        }

        if (isset($request->latitude) && $request->latitude) {
            $address->latitude = $request->latitude;
        }

        if (isset($request->longitude) && $request->longitude) {
            $address->longitude = $request->longitude;
        }

        if (isset($request->label) && $request->label) {
            $address->label = $request->label;
        }
        if (isset($request->country) && $request->country) {
            $address->country = $request->country;
        }
        $address->save();

        return GlobalFunction::sendDataResponse(true, __('Address book updated.'), $address);
    }

    function delete(Request $request)
    {
        $rules = [
            'id' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);

        if ($result):
            return $result;
        endif;

        $id = $request->id;
        $address = UserAddressBook::find($id);
        $address->delete();

        return GlobalFunction::sendSimpleResponse(true, __('Address book is deleted.'));
    }
}