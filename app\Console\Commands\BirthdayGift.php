<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Users;
use App\Models\Products;
use App\Models\ProductVariations;
use App\Models\BirthdayGift as BirthdayGiftModel;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use App\Http\Controllers\Admin\CartController;
use App\Notifications\BirthdayNotification;

class BirthdayGift extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'birthday_gift:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Check if it's the first day of the month
        $reminder_recipient = [];
        if (Carbon::now()->isSameDay(Carbon::now()->startOfMonth())) {
            $month = Carbon::now()->month;
            $users = Users::whereMonth('dob', $month)->whereNull('deleted_at')->get();
            $last_day_of_month = Carbon::now()->endOfMonth()->format('d/m/Y'); // not sure need the user to buy the gfit before when.

            if ($users && count($users) > 0) {
                foreach ($users as $user) {
                    $brand = $user->brand;
                    $membership_tier = $user->membership_tier;
                    $birthday_gifts = BirthdayGiftModel::whereJsonContains('month',(string)$month)
                        ->whereJsonContains('brand', $brand)
                        ->where('is_active', 1)
                        ->get();

                    $bd_gift_products = [];
                    $products_names = [];
                    // loop all available Birthday Gifts
                    foreach ($birthday_gifts as $birthday_gift) {
                        $birthday_gift_items = $birthday_gift->items()->whereJsonContains('membership_tier_ids', (string)$membership_tier)->get();

                        if (count($birthday_gift_items) > 0) {
                            foreach ($birthday_gift_items as $birthday_gift_item) {
                                $data = [];
                                $data['product_id'] = $birthday_gift_item->product_id;
                                $data['variation_id'] = $birthday_gift_item->variation_id;
                                $data['quantity'] = $birthday_gift_item->quantity;
                                $data['is_birthday'] = 1;

                                // to collect the birthday gift projects for later to add in cart once
                                $bd_gift_products[] = (object)$data;
                                
                                $product = Products::find($birthday_gift_item->product_id);
                                $product_name = $product->name;
                                if ($birthday_gift_item->variation_id) {
                                    $product_variation = ProductVariations::where('id', $birthday_gift_item->variation_id)
                                        ->where('product_id', $birthday_gift_item->product_id)
                                        ->first();

                                    $product_name .= ' - '. $product_variation->getVariationNameAttribute();
                                }

                                // collect product names for email content use
                                $products_names[] = $product_name;
                            }
                        }
                    }

                    if (count($bd_gift_products) > 0) {
                        $cart_controller = new CartController();
                        $cart_controller->add($user->id, $bd_gift_products);
                    }

                    // Send Email
                    if($user->email_address):
                        Notification::route('mail', $user->email_address)
                            ->notify(new BirthdayNotification($user, $products_names, 'gift', $last_day_of_month, ['mail']));
                    endif;

                    // Send Whatsapp
                    Notification::route('whatsapp', $user->phone_number)
                        ->notify(new BirthdayNotification($user, 'Free Product', 'gift', $last_day_of_month, ['whatsapp']));

                    $reminder_recipient[] = $user->id.": ".$user->phone_number;
                }
            }
        }

        if($reminder_recipient):
            Cronjob::create([
                "type" => "birthday gift",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}
