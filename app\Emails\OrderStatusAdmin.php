<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderStatusAdmin extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		if($this->data['status'] == 'paid'):
			$subject = __('[:site_name] New Order, :ref_no', [
				'site_name' => global_settings('site_name'),
				'ref_no' => $this->data['ref_no']
			]);
		else:
			$subject = __('[:site_name] Order, :ref_no is :status', [
				'site_name' => global_settings('site_name'),
				'ref_no' => $this->data['ref_no'],
				'status' => $this->data['status']
			]);
		endif;
		
		$build = $this->subject($subject)
            ->view('emails.order-status-admin')
            ->with('data', $this->data);
	}
}