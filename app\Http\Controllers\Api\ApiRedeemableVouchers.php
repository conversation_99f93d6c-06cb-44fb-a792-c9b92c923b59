<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\RedemptionVouchers;
use App\Models\MembershipTiers;
use App\Models\Products;
use App\Models\UserVouchers;
use App\Traits\PointsTraits;

class ApiRedeemableVouchers
{
    use PointsTraits;

    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'];
        }
    }

    function listing(Request $request)
    {
        $per_page = $request->per_page ? $request->per_page : 10;
        $user_membership = null;

        if (!is_null(auth()->user())) {
            $user_membership = auth()->user()->membership_tier;
        }

        $data = RedemptionVouchers::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('is_birthday', 0)
            ->where('is_new_user', 0)
            ->where('is_auto_issuance', 0)
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', $this->brand);
            })
            ->where('available_start_date', '<=', date('Y-m-d H:i:s'))
            ->where('available_end_date', '>=', date('Y-m-d H:i:s'))
            ->where('first_time_purchase', 0)
            ->when(isset($user_membership), function ($query) use ($user_membership) {
                $query->where('membership_tiers', 'like', '%"' . $user_membership . '"%');
            })
            ->when($request->is_featured, function ($query) use ($request) {
                $query->where('is_featured', $request->is_featured);
            })
            ->when(isset($request->created_at) && $request->created_at == 'asc', function ($query)  {
                $query->orderBy('created_at', 'asc');
            },
            function ($query) {
                $query->orderBy('created_at', 'desc');
            })
            ->paginate($per_page);

        if ($data->count() > 0):
            $data->makeHidden([
                'balance_quantity',
                'user_ids',
                'portal',
            ]);

            $data->map(function ($item) {
                if ($item->image):
                    $item->image = GlobalFunction::createMediaUrl($item->image);
                endif;

                if ($item->banner):
                    $item->banner = GlobalFunction::createMediaUrl($item->banner);
                endif;

                if ($item->type == 'discount' && $item->discount_type == 'fixed') {
                    $item->voucher_value = global_settings('currency') . " " . $item->value;
                } elseif ($item->type == 'discount' && $item->discount_type == 'percentage') {
                    $item->voucher_value = $item->value . '%';
                } else {
                    $item->voucher_value = '';
                }

                if ($item->max_quantity && $item->redeemed_quantity >= $item->max_quantity):
                    $item->fully_redeemed = true;
                else:
                    $item->fully_redeemed = false;
                endif;
                $item->effective_start_date = $item->redemption_start_date;
                $item->effective_end_date = $item->redemption_end_date;


                if ($item->membership_tiers):
                    $item->membership_tiers = getMemberTier(json_decode($item->membership_tiers));
                endif;

                if (!is_null(auth()->user())) {
                    if (auth()->user()->point_balance < $item->redeem_points):
                        $item->can_redeem = false;
                    else:
                        $item->can_redeem = true;
                    endif;
                } else {
                    $item->can_redeem = false;
                }

                unset($item->redemption_start_date);
                unset($item->redemption_end_date);

                return $item;
            });
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail(Request $request)
    {
        $user_membership = null;

        if (!is_null(auth()->user())) {
            $user_membership = auth()->user()->membership_tier;
        }

        $data = RedemptionVouchers::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('is_birthday', 0)
            ->where('is_new_user', 0)
            ->where('is_auto_issuance', 0)
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', $this->brand);
            })
            ->where('available_start_date', '<=', date('Y-m-d H:i:s'))
            ->where('available_end_date', '>=', date('Y-m-d H:i:s'))
            ->where('first_time_purchase', 0)
            ->when(isset($user_membership), function ($query) use ($user_membership) {
                $query->where('membership_tiers', 'like', '%"' . $user_membership . '"%');
            })
            ->where('id', $request->id)
            ->first();

        if (!$data):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher not found.');
        endif;

        $data->makeHidden([
            'balance_quantity',
            'user_ids',
            'portal'
        ]);

        if ($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;

        if ($data->banner):
            $data->banner = GlobalFunction::createMediaUrl($data->banner);
        endif;

        if ($data->type == 'discount' && $data->discount_type == 'fixed') {
            $data->voucher_value = global_settings('currency') . " " . $data->value;
        } elseif ($data->type == 'discount' && $data->discount_type == 'percentage') {
            $data->voucher_value = $data->value . '%';
        } else {
            $data->voucher_value = '';
        }

        if ($data->max_quantity && $data->redeemed_quantity >= $data->max_quantity):
            $data->fully_redeemed = true;
        else:
            $data->fully_redeemed = false;
        endif;
        $data->effective_start_date = $data->redemption_start_date;
        $data->effective_end_date = $data->redemption_end_date;

        if ($data->type == "free_product"):
            $product_list = [];
            foreach ($data->voucher_products as $product):
                $image = ($product->variation_id ? $product->variation->image_url : $product->product->single_image_url);
                $product_list[] = [
                    'image' => $image,
                    'product_name' => $product->product->name,
                    'variation_name' => $product->variation_name ?? null,
                    'quantity' => $product->quantity,
                ];
            endforeach;
            $data->free_product = $product_list;
        else:
            $data->free_product = null;
        endif;

        if ($data->membership_tiers):
            $data->membership_tiers = getMemberTier(json_decode($data->membership_tiers));
        endif;

        if (!is_null(auth()->user())) {
            if (auth()->user()->point_balance < $data->redeem_points):
                $data->can_redeem = false;
            else:
                $data->can_redeem = true;
            endif;
        } else {
            $data->can_redeem = false;
        }

        unset($data->redemption_start_date);
        unset($data->redemption_end_date);
        unset($data->voucher_products);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function claim(Request $request)
    {
        $id = $request->id;

        $voucher = RedemptionVouchers::whereNull('deleted_at')
            ->where('is_active', 1)
            // ->where('type', '!=', 'birthday')
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', $this->brand);
            })
            ->where('available_start_date', '<=', date('Y-m-d H:i:s'))
            ->where('available_end_date', '>=', date('Y-m-d H:i:s'))
            ->where('first_time_purchase', 0)
            ->where('id', $id)
            ->first();

        if (!$voucher):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher no longer available.');
        endif;

        if ($voucher->max_quantity && $voucher->redeemed_quantity >= $voucher->max_quantity):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher is fully redeemed.');
        endif;

        $claimed_voucher = UserVouchers::where('user_id', auth()->user()->id)->where('voucher_id', $id)->count();
        if ( $voucher->voucher_per_usage != null && $claimed_voucher >= $voucher->voucher_per_usage):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher is fully redeemed per customer.');
        endif;

        $user = auth()->user();
        if (!in_array($user->membership_tier, json_decode($voucher->membership_tiers))):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher is not available for your membership tier.');
        endif;

        if ($user->point_balance < $voucher->redeem_points):
            return GlobalFunction::sendSimpleResponse(false, 'Insufficient points to redeem this voucher.');
        endif;

        $voucher->redeemed_quantity += 1;
        $voucher->balance_quantity -= 1;
        $voucher->save();

        if ($voucher->type == 'free_product'):
            $free_product = $voucher->voucher_products->makeHidden(['created_at', 'updated_at']);
        endif;

        $user_voucher = new UserVouchers;
        $user_voucher->user_id = $user->id;
        $user_voucher->voucher_id = $voucher->id;
        $user_voucher->voucher_code = $voucher->code;
        $user_voucher->voucher_value = $voucher->value;
        $user_voucher->portal = $voucher->portal ?? null;
        $user_voucher->effective_start_date = $voucher->redemption_start_date;
        $user_voucher->effective_end_date = $voucher->redemption_end_date;
        $user_voucher->voucher_data = json_encode($voucher);
        $user_voucher->free_product = $free_product ?? null;
        $user_voucher->save();

        // point transaction
        $point_data = [
            'user_id' => $user->id,
            'type' => 'redeem_voucher',
            'type_id' => $user_voucher->id,
            'points_prefix' => "-",
            'points' => $voucher->redeem_points,
            'status' => 'ready',
            'remarks' => 'Redeem voucher (' . $voucher->name . ')',
        ];
        $this->insertPointTransaction($point_data);

        $data = [
            'redeemed_id' => $user_voucher->id,
        ];
        return GlobalFunction::sendDataResponse(true, 'Voucher redeemed successfully.', $data);
    }

    function firstTimePurchase()
    {
        $user = auth()->user();
        // check if user is eligible for first time stringing voucher

        $data = RedemptionVouchers::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('redemption_start_date', '<=', date('Y-m-d H:i:s'))
            ->where('redemption_end_date', '>=', date('Y-m-d H:i:s'))
            ->where('first_time_purchase', 1)
            ->where('membership_tiers', 'like', '%"' . $user->membership_tier . '"%')
            ->whereColumn('redeemed_quantity', '<', 'max_quantity')
            // ->where('redeem_points', '<=', $user->point_balance)
            ->orderBy('redemption_start_date', 'asc')
            ->get();

        return $data;
    }
}
