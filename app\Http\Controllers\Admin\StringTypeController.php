<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StringTypes;
use App\Models\OutletStringTypes;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use App\Models\StringColors;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;

class StringTypeController extends Controller
{
    function index()
    {
        if (!checkPermission('view_string_types')):
            return redirect(route('index'))->with('error', __('You do not have permission to view string type.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('stringTypeDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('stringTypeExport'),
                'filename' => 'string_types'
            ]
        ];

        if (!checkPermission('delete_string_types')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_string_types')):
            unset($bulk_action['export']);
        endif;

        $colors = StringColors::where('is_active', 1)
            ->orderBy('name', 'ASC')
            ->get();

        $moduleID = 'string_types';
        return view('string-types', compact('bulk_action', 'moduleID', 'colors'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'name';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = StringTypes::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
            $q->where('name', 'LIKE', "%{$search}%");
        })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->color, function ($query) use ($request) {
                $query->where('string_colors', 'LIKE', '%"' . $request->color . '"%');
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="string_typesList">
                    <span></span>
                </label>';

                if (!checkPermission('edit_string_types')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $string_colors = '';
                $string_color_names = '';
                if ($item->string_colors) {
                    //format "8" to '8'
                    $string_colors = "['" . implode("', '", json_decode($item->string_colors)) . "']";
                    $string_color_names = StringColors::whereIn('id', json_decode($item->string_colors, true))
                        ->pluck('name')
                        ->toArray();
                    $string_color_names = implode(', ', $string_color_names);
                }

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_string_types')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-name="' . $item->name . '" data-price="' . $item->price . '" data-status="' . $item->is_active . '" data-string_colors="' . $string_colors . '" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_string_types')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    number_format($item->price, 2),
                    $string_color_names,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_string_types')):
                    unset($param[5]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $rules = [
            'price' => 'numeric|regex:/^\d+(\.\d{1,2})?$/',
        ];
        $validator = Validator::make($request->all(), $rules, [
            'price.regex' => __('Price must be a number with up to 2 decimal places.')
        ]);
        if ($validator->fails()) {
            $messages = $validator->errors()->all();
            return GlobalFunction::sendSimpleResponse(false, $messages);
        }

        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = StringTypes::find($id);
        else:
            $msg = __('Added!');
            $query = new StringTypes();
        endif;

        $query->name = $request->name;
        $query->price = $request->price;
        $query->string_colors =  $request->string_colors ? json_encode($request->string_colors) : null;
        $query->is_active = $request->status ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function getStringTypeColors(Request $request)
    {
        $id = $request->id ?? null;
        $string_color_names = [];
        $query = StringTypes::find($id);
        if ($query->string_colors) {
            $string_color_names = StringColors::whereIn('id', json_decode($query->string_colors))->pluck('name', 'id')->toArray();
        }
        return $string_color_names;
    }

    function delete(Request $request)
    {
        if ($request->id):
            StringTypes::find($request->id)->delete();
            OutletStringTypes::where('string_type_id', $request->id)->delete();
        else:
            StringTypes::whereIn('id', $request->selected)->delete();
            OutletStringTypes::whereIn('string_type_id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = StringTypes::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'name';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = StringTypes::when($request->search, function ($q) use ($request) {
            $search = $request->search;
            $q->where('name', 'LIKE', "%{$search}%");
        })
            ->when($request->selected, function ($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when($request->color, function ($query) use ($request) {
                $query->where('string_colors', 'LIKE', '%"' . $request->color . '"%');
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $string_color_names = '';
                if($item->string_colors){
                    $string_color_names = StringColors::whereIn('id', json_decode($item->string_colors, true))
                    ->pluck('name')
                    ->toArray();
                    $string_color_names = implode(', ', $string_color_names);
                }
                $item->string_colors = $string_color_names;
            endforeach;
        endif;

        $fileName = 'string_types_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'string_types'),
            $fileName
        );
    }
}
