<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Models\Carts;
use App\Models\Bookings;
use App\Models\Products;
use App\Models\CartProducts;
use App\Models\UserVouchers;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\RedemptionVouchers;
use App\Http\Controllers\Api\ApiRedeemableVouchers;

class ApiRedeemedVouchers
{
    function listing(Request $request)
    {
        $per_page = $request->per_page ? $request->per_page : 10;
        $is_used = $request->is_used ?? 0;
        $is_stackable = $request->is_stackable;
        $is_available = $request->is_available;

        $data = collect();

        if ($request->type == 'order') {
            $cart = Carts::where('user_id', auth()->user()->id)->first();

            $user_vouchers = UserVouchers::where('user_id', auth()->user()->id)->whereNull('used_date')
                ->when(isset($is_stackable) && ($is_stackable == 1 || $is_stackable == 0), function ($query) use ($is_stackable) {
                    $query->whereHas('voucher', function ($query) use ($is_stackable) {
                        $query->where('is_stackable', $is_stackable);
                    });
                })
                ->when(is_array($cart->voucher_list), function ($query) use ($cart) {
                    $query->whereNotIn('id', $cart->voucher_list);
                })
                ->where('effective_start_date', '<', Carbon::now())
                ->where('effective_end_date', '>', Carbon::now());
        } else {
            $user_vouchers = UserVouchers::where('user_id', auth()->user()->id);
            if ($is_used):
                $user_vouchers = $user_vouchers->whereNotNull('used_date')->orWhere('effective_end_date', '<', now())->where('user_id', auth()->user()->id);
            else:
                $user_vouchers = $user_vouchers->whereNull('used_date');
            endif;
        }

        $user_vouchers = $user_vouchers->orderBy('effective_start_date', 'asc')
            ->when(isset($is_available) && ($is_available == 1), function ($query) {
                $query->where('effective_end_date', '>', now());
            })
            ->when($request->name, function ($query) use ($request) {
                $query->whereHas('voucher', function ($query) use ($request) {
                    $query->where('name', 'like', '%' . $request->name . '%');
                });
            })
            ->orderBy('id', 'asc')
            ->get();

        if ($user_vouchers->count() > 0):
            $user_vouchers->makeHidden([
                'updated_at',
            ]);

            $user_vouchers->map(function ($item) {
                $item->voucher_data = json_decode($item->voucher_data);
                $item->status = 'Active';

                if ($item->voucher_data->image):
                    $item->voucher_data->image = GlobalFunction::createMediaUrl($item->voucher_data->image);
                endif;

                if ($item->voucher_data->banner):
                    $item->voucher_data->banner = GlobalFunction::createMediaUrl($item->voucher_data->banner);
                endif;

                if ($item->voucher_data->list_image ?? ''):
                    $item->voucher_data->list_image = GlobalFunction::createMediaUrl($item->voucher_data->list_image);
                endif;

                if ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'fixed') {
                    $item->voucher_value = global_settings('currency') . " " . $item->voucher_data->value;
                } elseif ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'percentage') {
                    $item->voucher_value = $item->voucher_data->value . '%';
                } else {
                    $item->voucher_value = '';
                }

                $isEffective = false;
                $cannotUseReason = null;
                $start_date = $item->effective_start_date;
                $end_date = $item->effective_end_date;
                if ($item->used_date):
                    $item->status = 'used';
                    $cannotUseReason = __('Voucher has been used on :date', ['date' => Carbon::parse($item->used_date)->format('j M Y')]);
                elseif ($start_date && strtotime($start_date) > time()):
                    $item->status = 'pending';
                    $cannotUseReason = __('Voucher only can be used starting from :date.', ['date' => Carbon::parse($start_date)->format('j M Y')]);
                elseif ($end_date && strtotime($end_date) < time()):
                    $item->status = 'expired';
                    $cannotUseReason = __('Voucher is expired on :date.', ['date' => Carbon::parse($end_date)->format('j M Y')]);
                else:
                    $isEffective = true;
                endif;
                $item->is_effective = $isEffective;
                $item->cannot_use_reason = $cannotUseReason;

                unset($item->portal);
                unset($item->voucher_data->portal);
                unset($item->voucher_data->user_ids);
                unset($item->voucher_data->created_at);
                unset($item->voucher_data->updated_at);
                unset($item->voucher_data->deleted_at);
                unset($item->voucher_data->max_quantity);
                unset($item->voucher_data->redeem_points);
                unset($item->voucher_data->redeemed_quantity);
                unset($item->voucher_data->balance_quantity);
                unset($item->voucher_data->membership_tiers);
                unset($item->voucher_data->available_end_date);
                unset($item->voucher_data->available_start_date);
                unset($item->voucher_data->redemption_end_date);
                unset($item->voucher_data->redemption_start_date);
                unset($item->voucher_data->is_active);

                return $item;
            });

            $data = $data->concat($user_vouchers);
        endif;

        $data = $data->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail(Request $request)
    {
        $data = UserVouchers::where('user_id', auth()->user()->id)
            ->where('id', $request->id)
            ->first();

        if (!$data):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher not found.');
        endif;

        $data->status = 'Active';

        $data->voucher_id = $data->voucher->id;
        $data->name = $data->voucher->name;
        $data->image_url = $data->voucher->image_url;
        $data->description = $data->voucher->description;
        $data->how_to_use = $data->voucher->how_to_use;
        $data->terms_condition = $data->voucher->terms_condition;
        $data->redeem_points = $data->voucher->redeem_points;
        $data->discount_type = $data->voucher->discount_type;
        $data->value = $data->voucher->value;
        $data->max_quantity = $data->voucher->max_quantity;
        $data->voucher_per_usage = $data->voucher->voucher_per_usage;

        // $data->voucher_data = json_decode($data->voucher_data);
        // if ($data->voucher_data->image):
        //     $data->voucher_data->image = GlobalFunction::createMediaUrl($data->voucher_data->image);
        // endif;

        // if ($data->voucher_data->banner):
        //     $data->voucher_data->banner = GlobalFunction::createMediaUrl($data->voucher_data->banner);
        // endif;

        if ($data->voucher->list_image ?? ''):
            $data->list_image_url = GlobalFunction::createMediaUrl($data->voucher->list_image);
        endif;

        if ($data->voucher->type == 'discount' && $data->voucher->discount_type == 'fixed') {
            $data->voucher_value = global_settings('currency') . " " . $data->voucher->value;
        } elseif ($data->voucher->type == 'discount' && $data->voucher->discount_type == 'percentage') {
            $data->voucher_value = $data->voucher->value . '%';
        } else {
            $data->voucher_value = '';
        }

        $isEffective = false;
        $cannotUseReason = null;
        $start_date = $data->effective_start_date;
        $end_date = $data->effective_end_date;
        if ($data->used_date):
            $data->status = 'used';
            $cannotUseReason = __('Voucher has been used on :date', ['date' => Carbon::parse($data->used_date)->format('j M Y')]);
        elseif ($start_date && strtotime($start_date) > time()):
            $data->status = 'pending';
            $cannotUseReason = __('Voucher only can be used starting from :date.', ['date' => Carbon::parse($start_date)->format('j M Y')]);
        elseif ($end_date && strtotime($end_date) < time()):
            $data->status = 'expired';
            $cannotUseReason = __('Voucher is expired on :date.', ['date' => Carbon::parse($end_date)->format('j M Y')]);
        else:
            $isEffective = true;
        endif;
        $data->is_effective = $isEffective;
        $data->cannot_use_reason = $cannotUseReason;

        // if ($data->voucher_data->type == "free_product" && isset($data->voucher_products) && $data->voucher_products):
        //     $data->voucher_data->free_product = array_values($data->voucher_products);
        // else:
        //     $data->voucher_data->free_product = null;
        // endif;

        unset($data->portal);
        unset($data->voucher_data);
        // unset($data->voucher_data->portal);
        // unset($data->voucher_data->user_ids);
        // unset($data->voucher_data->created_at);
        // unset($data->voucher_data->updated_at);
        // unset($data->voucher_data->deleted_at);
        // unset($data->voucher_data->max_quantity);
        // unset($data->voucher_data->redeem_points);
        // unset($data->voucher_data->redeemed_quantity);
        // unset($data->voucher_data->balance_quantity);
        // unset($data->voucher_data->membership_tiers);
        // unset($data->voucher_data->available_end_date);
        // unset($data->voucher_data->available_start_date);
        // unset($data->voucher_data->redemption_end_date);
        // unset($data->voucher_data->redemption_start_date);
        // unset($data->voucher_data->is_active);
        // unset($data->voucher_data->voucher_products);
        unset($data->free_product);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function apply(Request $request)
    {
        $voucher_id = $request->voucher_id;
        $voucher = [$voucher_id];
        $amount = $request->amount;

        $response = [
            'voucher_id' => (int) $voucher_id,
            'sub_amount' => number_format($amount, 2)
        ];

        if (!is_numeric($amount)):
            return GlobalFunction::sendDataResponse(false, 'Please enter valid amount.', $response);
        endif;

        $user_voucher = UserVouchers::where('user_id', auth()->user()->id)
            ->with('voucher')
            ->where('id', $voucher_id)
            ->whereNull('used_date')
            ->first();

        $cart = Carts::where('user_id', auth()->user()->id)->first();

        if ($cart->coupon_id != null):
            return GlobalFunction::sendSimpleResponse(false, 'Unable to apply voucher, coupon already applied.');
        endif;

        $user_voucher->voucher_data = json_decode($user_voucher->voucher_data);

        if (!$user_voucher):
            return GlobalFunction::sendSimpleResponse(false, 'Please enter valid voucher.');
        endif;

        if ($user_voucher->voucher_data->is_stackable == 0 && $cart->voucher_list && count($cart->voucher_list) > 0) {
            return GlobalFunction::sendDataResponse(false, 'Unable to stack voucher.', $response);
        }

        $claimed_voucher = UserVouchers::where('user_id', auth()->user()->id)->where('voucher_id', $voucher_id)->whereNotNull('used_date')->count();
        if ($user_voucher->voucher_data->voucher_per_usage != null && $claimed_voucher >= $user_voucher->voucher_data->voucher_per_usage):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher is fully redeemed per customer.');
        endif;

        if ($amount >= ($user_voucher->voucher_data->min_spend ?? 0)) {

            $cart->voucher_id = $cart->voucher_list ? array_merge($cart->voucher_list, $voucher) : $voucher;
            $cart->save();

            if ($user_voucher->voucher_data->type == 'discount') {
                if ($user_voucher->voucher_data->discount_type == 'percentage') {
                    $response['discount_amount'] = number_format($amount * ($user_voucher->voucher_data->value / 100), 2);
                } else {
                    $response['discount_amount'] = number_format($user_voucher->voucher_data->value, 2);
                }
                $response['total_amount'] = number_format($amount - $response['discount_amount'], 2);
            }

            if ($user_voucher->voucher_data->type == 'free_product') {
                foreach ($user_voucher->voucher_data->voucher_products as $free_product) {
                    $free_cart_product = new CartProducts();
                    $free_cart_product->cart_id = $cart->id;
                    $free_cart_product->cart_bundle_id = null;
                    $free_cart_product->product_id = $free_product->product_id;
                    $free_cart_product->variation_id =  null;
                    $free_cart_product->is_gwp = 0;
                    $free_cart_product->is_pwp = 0;
                    $free_cart_product->is_free = 1;
                    $free_cart_product->quantity = $free_product->quantity;
                    $free_cart_product->save();
                }
            }

            return GlobalFunction::sendDataResponse(true, '', $response);
        } else {

            $spend_more = $user_voucher->voucher_data->min_spend - $amount;
            return GlobalFunction::sendDataResponse(false, 'Minimum spending not reached,please spend ' . number_format($spend_more, 2) . ' more.', $response);
        }
    }

    function removeFromCart($voucher_id)
    {
        $cart = Carts::where('user_id', auth()->user()->id)->first();
        $cart->voucher_id = array_diff($cart->voucher_list, [$voucher_id]);
        $cart->save();

        $user_voucher = UserVouchers::where('user_id', auth()->user()->id)
            ->with('voucher')
            ->where('id', $voucher_id)
            ->whereNull('used_date')
            ->first();

        $voucher_data = json_decode($user_voucher->voucher_data);
        if ($voucher_data->type == 'free_product') {
            $remove_products = array_column($voucher_data->voucher_products, 'product_id');
            CartProducts::where('cart_id', $cart->id)->whereIn('product_id', $remove_products)->where('is_free', 1)->delete();
        }

        return GlobalFunction::sendDataResponse(true, 'Removed voucher from cart', $cart);
    }

    function useNow($voucher_id)
    {
        $user_voucher = UserVouchers::where('user_id', auth()->user()->id)
            ->where('id', $voucher_id)
            ->whereNull('used_date')
            ->first();

        if (!$user_voucher) {
            return GlobalFunction::sendSimpleResponse(false, 'Voucher not found.');
        }

        $user_voucher->used_date = Carbon::now();
        $user_voucher->physical_use_date = Carbon::now();
        $user_voucher->save();

        return GlobalFunction::sendDataResponse(true, 'Voucher used successfully', $user_voucher);
    }
}
