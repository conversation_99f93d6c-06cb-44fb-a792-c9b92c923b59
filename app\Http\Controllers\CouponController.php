<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Coupon;
use App\Models\Products;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\ProductCategory;
use Maatwebsite\Excel\Facades\Excel;

class CouponController extends Controller
{
    function index()
    {
        if (!checkPermission('view_coupon')):
            return redirect(route('index'))->with('error', __('You do not have permission to view redemption coupon.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('couponDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('couponExport'),
                'filename' => 'coupons'
            ]
        ];

        if (!checkPermission('delete_coupon')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_coupon')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'coupons';
        return view('coupons', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';

        $start_filter = $end_filter = null;
        if (isset($request->available_date)) {
            $available_date = explode(" to ", $request->available_date);
            $start_filter = Carbon::parse(current($available_date))->format('Y-m-d H:i:s');
            $end_filter = Carbon::parse(end($available_date))->endOfDay()->format('Y-m-d H:i:s');
        }

        $result = Coupon::when($request->input('search.value'), function ($query) use ($request) {
            $search = $request->input('search.value');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%$search%")
                    ->orWhere('code', 'LIKE', "%$search%");
            });
        })
            ->when(isset($start_filter) && isset($end_filter), function ($query) use ($start_filter, $end_filter) {
                $query->whereBetween('start_datetime', [$start_filter, $end_filter])
                    ->whereBetween('expiry_datetime', [$start_filter, $end_filter]);
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->type) && $request->type != 'all', function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="couponList">
                    <span></span>
                </label>';


                $available_date = '-';
                if ($item->start_datetime && $item->expiry_datetime) {
                    $available_date =
                        date('d-m-Y g:ia', strtotime($item->start_datetime)) .
                        '<br>to<br>' .
                        date('d-m-Y g:ia', strtotime($item->expiry_datetime));
                }

                if (!checkPermission('edit_coupon')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $discount_type = '-';
                if ($item->type) {
                    $discount_type = config('staticdata.discount_type')[$item->type];
                }

                $discount_value = '-';
                if ($item->value != 0) {
                    $discount_value = $item->value;
                }

                $minimum_amount = '-';
                if ($item->minimum_amount != 0) {
                    $minimum_amount = $item->minimum_amount;
                }

                $limit_per_user = '-';
                if ($item->limit_per_user != 0) {
                    $limit_per_user = $item->limit_per_user;
                }

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_coupon')):
                    $action .= '<a href="' . route('couponForm', $item->id) . '" class="btn-icon btn-info mr-1" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_coupon')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    '<div class="text-center">' . $item->code . '</div>',
                    '<div class="text-center">' . $discount_type . '</div>',
                    '<div class="text-center">' . $discount_value . '</div>',
                    '<div class="text-center">' . $minimum_amount . '</div>',
                    '<div class="text-center">' . $limit_per_user . '</div>',
                    '<div class="text-center">' . $available_date . '</div>',
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_coupon') && !checkPermission('delete_coupon')):
                    unset($param[11]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function form(Request $request)
    {
        $id = $request->id ?? null;

        if (!$id):
            $title = __('Add Coupon');
            $result = new Coupon();
        else:
            $title = __('Edit Coupon');
            $result = Coupon::find($id);
        endif;

        $products = Products::where('is_active', true)->get();
        $productCatogories = ProductCategory::where('is_active', true)->get();

        $data = [
            'title' => $title,
            'id' => $id,
            'result' => $result,
            'products' => $products,
            'productCatogories' => $productCatogories
        ];
        return view('coupon-form', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;

        if ($id):
            $msg = __('Updated!');
            $query = Coupon::find($id);
        else:
            $msg = __('Added!');
            $query = new Coupon();
        endif;

        $start = $end = null;
        if (isset($request->available_date)) {
            $available = explode(' to ', $request->available_date);
            $start = Carbon::createFromFormat(config('app.display_datetime_format'), $available[0])->format('Y-m-d H:i:s');
            $end = Carbon::createFromFormat(config('app.display_datetime_format'), $available[1])->format('Y-m-d H:i:s');
        }

        $query->name = $request->name;
        $query->code = $request->code ?? null;
        $query->type = $request->type;
        $query->value = $request->value;
        $query->minimum_amount = $request->minimum_amount ?? null;
        $query->limit_per_user = $request->limit_per_user ?? null;
        $query->product_ids = isset($request->product_ids) ? json_encode($request->product_ids) : null;
        $query->category_ids = isset($request->category_ids) ? json_encode($request->category_ids) : null;
        $query->start_datetime = $start ?? null;
        $query->expiry_datetime = $end ?? null;
        $query->is_active = $request->status ?? 0;
        $query->save();

        $id = $query->id;

        return redirect(route('couponForm', $id))->with('message', $msg);
    }

    function delete(Request $request)
    {
        if ($request->id):
            $query = Coupon::find($request->id);
        else:
            $query = Coupon::whereIn('id', $request->selected);
        endif;

        $query = $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Coupon::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';

        $start_filter = $end_filter = null;
        if (isset($request->available_date)) {
            $available_date = explode(" to ", $request->available_date);
            $start_filter = Carbon::parse(current($available_date))->format('Y-m-d H:i:s');
            $end_filter = Carbon::parse(end($available_date))->endOfDay()->format('Y-m-d H:i:s');
        }

        $result = Coupon::when($request->input('search.value'), function ($query) use ($request) {
            $search = $request->input('search.value');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%$search%")
                    ->orWhere('code', 'LIKE', "%$search%");
            });
        })
            ->when(isset($start_filter) && isset($end_filter), function ($query) use ($start_filter, $end_filter) {
                $query->whereBetween('start_datetime', [$start_filter, $end_filter])
                    ->whereBetween('expiry_datetime', [$start_filter, $end_filter]);
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->type) && $request->type != 'all', function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0) {
            foreach ($result as $item) {
                if ($item->type == 'discount' && $item->discount_type == 'fixed') {
                    $item->discount = global_settings('currency') . " " . $item->value;
                } elseif ($item->type == 'discount' && $item->discount_type == 'percentage') {
                    $item->discount = $item->value . '%';
                } else {
                    $item->discount = '-';
                }

                if (isset($item->minimum_amount)) {
                    $item->minimum_amount = "RM " . $item->minimum_amount;
                } else {
                    $item->minimum_amount = '-';
                }

                if (isset($item->product_ids)) {
                    $products = Products::whereIn('id', json_decode($item->product_ids))->pluck('name');
                    $item->product_ids = implode(', ', $products->toArray());
                } else {
                    $item->product_ids = '-';
                }

                if (isset($item->category_ids)) {
                    $categories = ProductCategory::whereIn('id', json_decode($item->category_ids))->pluck('name');
                    $item->category_ids = implode(', ', $categories->toArray());
                } else {
                    $item->category_ids = '-';
                }

                $item->is_active = $item->is_active == 1 ? 'Yes' : 'No';
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            }
        }

        $fileName = 'coupons_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'coupon'),
            $fileName
        );
    }
}
