<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PushNotification;
use App\Models\GlobalFunction;

class PushNotificationController extends Controller
{
    function index()
    {
        return view('push-notifications');
    }

    function listing(Request $request)
    {

        $result = PushNotification::orderBy('id', 'DESC');
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();
       
            foreach ($result as $item):
                $notify = '<div>
                                <a href="'.route('pushNotificationDetail', $item->id).'?start='.$start.'">
                                    <div class="notify-date">'.Carbon::parse($item->created_at)->format('d-m-Y, g:ia').'</div>
                                    <div class="notify-title">'.$item->title.'</div>
                                    <div class="notify-content">';
                                        if(strlen($item->message) > 70):
                                            $notify .= substr($item->message, 0, 70).'...';
                                        else:
                                            $notify .= $item->message;
                                        endif;
                        $notify .= '</div>';

                    if(!$item->read_at):
                        $notify .= '<div class="notify-new bg-success">'.__('New').'</div>';
                    endif;

                    $notify .= '</a>
                            </div>';  

                $data[] = [
                    $notify
                ];
            endforeach;
        endif;
        
        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail($id)
    {
        $data = PushNotification::with('booking')->find($id);
        $data->read_at = Carbon::now();
        $data->save();

        return view('push-notification-detail', compact('data'));
    }
}