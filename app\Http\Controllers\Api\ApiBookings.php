<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\OutletStringTypes;
use App\Models\StringTypes;
use App\Models\StringTensions;
use App\Traits\BookingsTraits;

class ApiBookings
{
    use BookingsTraits;

    function availableSlots(Request $request)
    {
        $data = $this->getAvailableSlots($request);

        if(isset($data['msg'])):
            return GlobalFunction::sendSimpleResponse(true, $data['msg']);
        endif;
        if(isset($data['error'])):
            return GlobalFunction::sendSimpleResponse(false, $data['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function stringTypes(Request $request)
    {
        $data = [
            'outlet_id' => $request->outlet_id,
        ];
        $type = $this->getStringTypes($data);

        return GlobalFunction::sendDataResponse(true, '', $type);
    }

    function stringTensions()
    {
        $data = StringTensions::where('is_active', 1)
            ->orderBy('name', 'asc')
            ->get()
            ->makeHidden([
                'is_active', 
                'created_at', 
                'updated_at'
            ]);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function checkoutSummary(Request $request)
    {
        $data = [
            'outlet_id' => $request->outlet_id,
            'date' => $request->date,
            'time' => $request->time,
            'stringer' => $request->stringer,
            'string_type' => $request->string_type,
            'string_color' => $request->string_color,
            'tension' => $request->string_tension,
            'voucher_id' => $request->voucher_id ?? '',
            'first_time_purchase' => $request->first_time_purchase ?? 0,
            'vpoint' => $request->vpoint ?? 0,
            'validation' => $request->validation ?? false
        ];
        $summary = $this->checkout($data);

        if(isset($summary['error'])):
            return GlobalFunction::sendSimpleResponse(false, $summary['error']);
        endif;
        
        if($request->validation):
            return GlobalFunction::sendSimpleResponse(true, $summary);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $summary);
    }

    function voucherApply(Request $request)
    {
        $voucher_id = $request->voucher_id ?? null;
        $voucher_code = $request->voucher_code ?? null;
        $first_time_purchase = $request->first_time_purchase ?? 0;
        if(!$voucher_id && !$voucher_code):
            return GlobalFunction::sendSimpleResponse(false, __('Voucher code / id is required.'));
        endif;

        $voucher = $this->validateVoucher($voucher_id, $first_time_purchase, $voucher_code, 'apply');
        if(isset($voucher['error'])):
            return GlobalFunction::sendSimpleResponse(false, $voucher['error']);
        endif;

        $voucher_data = [
            'id' => $voucher['id'],
            'code' => $voucher['code'],
            'amount' => global_settings('currency').' '.number_format($voucher['amount'], 2),
        ];
        return GlobalFunction::sendDataResponse(true, '', $voucher_data);
    }

    function checkoutValidation(Request $request)
    {
        $request->request->add([
            'validation' => true,
        ]);

        return $this->checkoutSummary($request);
    }

    function paymentMethods()
    {
        $request = request();
        $type = $request->type;

        $data = [
            [
                'id' => 'cybersource',
                'name' => __('Credit & Debit Card'),
                'description' => null
            ],
        ];

        if($type != 'wallet'):
            $wallet_balance = 0;
            if(auth()->user()):
                $wallet_balance = global_settings('currency').' '.number_format(auth()->user()->wallet_balance, 2);
            endif;

            $order_payment = [
                [
                    'id' => 'ewallet',
                    'name' => __('E-Wallet'),
                    'description' => null,
                    'balance' => $wallet_balance ?? 0,
                ],
                [
                    'id' => 'pay_at_counter',
                    'name' => __('Pay At Counter'),
                    'description' => __('System will auto cancel the order if payment is not made within 2 hours.'),
                ],
            ];

            $data = array_merge($data, $order_payment);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function create(Request $request)
    {
        $request->request->add([
            'tension' => $request->string_tension,
        ]);
        $data = $this->createBooking($request);
        if(isset($data['error'])):
            return GlobalFunction::sendSimpleResponse(false, $data['error']);
        endif;

        $data = [
            'booking_id' => $data->id,
        ];
        return GlobalFunction::sendDataResponse(true, __('Stringing service is booked.'), $data);
    }

    function bookingListing($type, Request $request)
    {
        $request->request->add([
            'type' => $type,
        ]);
        $result = $this->getBookingListing($request);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function bookingDetail($type, Request $request)
    {
        $request->request->add([
            'type' => $type,
        ]);
        $result = $this->getBookingDetail($request);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function bookingUpdateStatus($type, Request $request)
    {
        $request->request->add([
            'type' => $type,
        ]);
        $result = $this->updateBookingStatus($request);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendSimpleResponse(true, __('Booking status is updated.'));
    }

    function bookingEditAcceptance($type, Request $request)
    {
        $booking_id = $request->booking_id;
        $changes_id = $request->changes_id;
        $status = $request->action;
        $remarks = $request->reject_comment ?? '';
        $result = $this->updateBookingChangesAcceptance($changes_id, $status, $remarks);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        $msg = $status == 'accept' ? __('You have accepted the changes.') : __('You have rejected the changes. Vsmash will contact you again.');
        $data = [
            'booking_id' => $booking_id,
        ];
        return GlobalFunction::sendDataResponse(true, $msg, $data);
    }
}