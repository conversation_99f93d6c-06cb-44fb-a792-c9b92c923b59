<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CheckLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $response->headers->set('Cache-Control', 'nocache, no-store, max-age=0, must-revalidate');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', 'Sun, 02 Jan 2021 00:00:00 GMT');

        if (Session::get('user_name')) {
            $user = session()->get('user');

            $logout = 0;
            if(session()->get('portal') == 'retailer'):
                $user_data = \App\Models\Retailer::where('user_id', $user->user_id)->first();
                if($user_data->is_active == 0 || $user_data->deleted_at != null):
                    $logout = 1;
                endif;
                $route = 'retailer.login';
            else:
                $user_data = \App\Models\Admin::where('user_id', $user->user_id)->first();
                if($user_data->is_active == 0 || $user_data->deleted_at != null):
                    $logout = 1;
                endif;
                $route = '/';
            endif;

            if($logout == 1):
                session()->pull('user');
                session()->pull('user_name');
                session()->pull('user_type');
                return redirect(route($route));
            endif;

            return $response;
        } else {
            if(session()->get('portal') == 'retailer'):
                $route = 'retailer.login';
            else:
                $route = '/';
            endif;

            return redirect(route($route));
        }
    }
}
