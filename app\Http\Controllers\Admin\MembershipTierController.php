<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MembershipTiers;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use App\Models\MembershipTierBenefit;
use App\Models\RedemptionVouchers;
use Maatwebsite\Excel\Facades\Excel;

class MembershipTierController extends Controller
{
    function index()
    {
        if(!checkPermission('view_membership_tiers')):
            return redirect(route('index'))->with('error', __('You do not have permission to view membership tiers.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('membershipTierDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('membershipTierExport'),
                'filename' => 'membership_tiers'
            ]
        ];

        if(!checkPermission('delete_membership_tiers')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_membership_tiers')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'membership_tiers';
        return view('membership-tiers', compact('bulk_action', 'moduleID'));
    }

    function benefitIndex($membership_tier_id)
    {
        $membership_tier= MembershipTiers::find($membership_tier_id);
        if(!checkPermission('view_membership_tiers')):
            return redirect(route('index'))->with('error', __('You do not have permission to view membership tiers.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('membershipTierBenefitDelete')
            ]
        ];

        if(!checkPermission('delete_membership_tiers')):
            unset($bulk_action['delete']);
        endif;

        $moduleID = 'membership_tier_benefits';

        $vouchers = RedemptionVouchers::active()->pluck('name', 'id')->toArray();
        return view('membership-tier-benefits', compact('bulk_action', 'moduleID','vouchers','membership_tier'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'point_from';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = MembershipTiers::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('name', 'LIKE', "%{$search}%");
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->where(function($query) {
            //     foreach (userBrand() as $brand) {
            //         $query->orWhereJsonContains('brand', $brand);
            //     }
            // })
            // ->when($request->brand != 'all', function($query) use ($request) {
            //     $query->whereJsonContains('brand', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by)
            ->when($sort_col == 'point_from', function($query) use ($sort_by) {
                $query->orderBy('point_to', $sort_by);
            });
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="membership_tiersList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_membership_tiers')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                $action .= '<a href="'.route('membershipTierBenefit', ['membership_tier_id' => $item->id]).'" class="btn-icon btn-warning mr-1" data-toggle="tooltip" data-title="'.__('Benefit').'"><i class="fa fa-hand-holding"></i></a>';
                if(checkPermission('edit_membership_tiers')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_membership_tiers')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $brand = $item->brand ? implode(', ', array_map(function($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';

                $param = [
                    $checkbox,
                    $item->name,
                    $item->point_from.' - '.$item->point_to,
                    // $brand,
                    // $item->min_purchase,
                    // $item->min_spent,
                    // $item->min_days,
                    $item->sequence,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_membership_tiers')):
                    unset($param[8]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function benefitListing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = MembershipTierBenefit::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where(function($query) use ($search) {
                    $query->where('name', 'LIKE', "%{$search}%")
                            ->orWhereHas('voucher',function($query) use ($search) {
                                $query->where('name', 'LIKE', "%{$search}%");
                            });
                });
            })
            ->where('membership_tier_id', $request->membership_tier_id)
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="membership_tiersList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_membership_tiers')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';

                if(checkPermission('edit_membership_tiers')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_membership_tiers')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->voucher->name,
                    $item->name,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_membership_tiers')):
                    unset($param[8]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = MembershipTiers::find($request->id);
        $data->brand = json_decode($data->brand);
        if($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;

        if($data->icon):
            $data->icon = GlobalFunction::createMediaUrl($data->icon);
        endif;
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function benefitDetail(Request $request)
    {
        $data = MembershipTierBenefit::find($request->id);

        if($data->icon):
            $data->icon = GlobalFunction::createMediaUrl($data->icon);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = MembershipTiers::find($id);

            if($query->users->count() > 0 && $request->status == 0):
                return GlobalFunction::sendSimpleResponse(false, __('Some membership tiers are attached with members, cannot be inactive.'));
            endif;
        else:
            $msg = __('Added!');
            $query = new MembershipTiers();
        endif;

        // point validation
        $point_from = $request->point_from;
        $point_to = $request->point_to;
        if($point_from > $point_to):
            return GlobalFunction::sendSimpleResponse(false, __('Threshold from must be less than Threshold to.'));
        endif;

        if($point_to < $point_from):
            return GlobalFunction::sendSimpleResponse(false, __('Threshold to must be greater than Threshold from.'));
        endif;

        $exist = MembershipTiers::where(function($q) use ($point_from, $point_to) {
                $q->where(function($query) use ($point_from, $point_to) {
                    $query->where('point_from', '<=', $point_to)
                          ->where('point_to', '>=', $point_from);
                });
            })
            ->when($id, function($q) use ($id) {
                $q->where('id', '!=', $id);
            })
            // ->where(function($q) use ($request) {
            //     foreach ($request->brand as $brand) {
            //         $q->orWhereJsonContains('brand', $brand);
            //     }
            // })
            ->count();
        if($exist > 0):
            return GlobalFunction::sendSimpleResponse(false, __('Threshold range already exists.'));
        endif;
        // end point validation

        $query->name = $request->name;
        $query->description = $request->description;
        $query->point_from = $point_from;
        $query->point_to = $point_to;
        // $query->brand = json_encode($request->brand);
        $query->sequence = $request->sequence;
        $query->min_purchase = $request->min_purchase;
        $query->min_spent = $request->min_spent;
        $query->min_days = $request->min_days;
        $query->expiration_month = $request->expiration_month;

        if($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        if($request->has('icon')):
            $query->icon = GlobalFunction::saveFileAndGivePath($request->icon);
        endif;
        
        $query->is_active = $request->status ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function benefitAddUpdate(Request $request)
    {
        $id = $request->id ?? null;

        if(!$request->has('icon') && !$id):
            return GlobalFunction::sendSimpleResponse(false, __('Icon is required.'));
        endif;
        if($id):
            $msg = __('Updated!');
            $query = MembershipTierBenefit::find($id);
        else:
            $msg = __('Added!');
            $query = new MembershipTierBenefit();
        endif;

        $query->name = $request->name;
        $query->membership_tier_id = $request->membership_tier_id;
        $query->voucher_id = $request->voucher_id;

        if($request->has('icon')):
            $query->icon = GlobalFunction::saveFileAndGivePath($request->icon);
        endif;

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function benefitDelete(Request $request)
    {
        if($request->id):
            $delete_id = [$request->id];
        else:
            $delete_id = $request->selected;
        endif;

        MembershipTierBenefit::whereIn('id', $delete_id)->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function delete(Request $request)
    {
        if($request->id):
            $delete_id = [$request->id];
        else:
            $delete_id = $request->selected;
        endif;

        // check if any member attached with this tier, if yes then return error
        $check = MembershipTiers::whereIn('id', $delete_id)->whereHas('users')->count();
        if($check > 0):
            return GlobalFunction::sendSimpleResponse(false, __('Some membership tiers are attached with members, cannot be delete.'));
        endif;

        MembershipTiers::whereIn('id', $delete_id)->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = MembershipTiers::find($request->id);

        if($request->value == 0 && $item->users->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, __('Some membership tiers are attached with members, cannot be inactive.'));
        endif;

        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'point_from';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = MembershipTiers::when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('name', 'LIKE', "%{$search}%");
            })
            // ->where(function($query) {
            //     foreach (userBrand() as $brand) {
            //         $query->orWhereJsonContains('brand', $brand);
            //     }
            // })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->whereJsonContains('brand', $request->brand);
            // })
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->when($sort_col == 'point_from', function($query) use ($sort_by) {
                $query->orderBy('point_to', $sort_by);
            })
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'membership_tiers_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'membership_tiers'),
            $fileName
        );
    }
}