<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderStatus extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		$subject = __('[:site_name] Order, :ref_no is :status', [
			'site_name' => global_settings('site_name'),
			'ref_no' => $this->data['ref_no'],
			'status' => $this->data['status']
		]);
		$build = $this->subject($subject)
            ->view('emails.order-status')
            ->with('data', $this->data);
	}
}