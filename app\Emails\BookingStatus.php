<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BookingStatus extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		$status = $this->data['status'];
		switch($status):
			case 'changes_request':
				$subject_text = 'requested for changes';
				break;
			case 'booking_changed':
				$subject_text = 'detail is changed';
				break;
			default:
				if(in_array($status, ['accepted', 'rejected'])):
					$subject_text = 'changes request is '.__($status);
				else:
					$subject_text = 'is '.__($status);
				endif;
				break;
		endswitch;

		$subject = __('[:site_name] Stringing, :ref_no :status', [
			'site_name' => global_settings('site_name'),
			'ref_no' => $this->data['ref_no'],
			'status' => $subject_text
		]);
		$build = $this->subject($subject)
            ->view('emails.booking-status')
            ->with('data', $this->data);
	}
}