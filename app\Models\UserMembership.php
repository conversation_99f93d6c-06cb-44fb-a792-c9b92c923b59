<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserMembership extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'membership_id',
        'remaining_maintain_purchase',
        'start_date',
        'expiry_date',
        'total_spent'
    ];

    protected $appends = ['membership_name'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function membership()
    {
        return $this->belongsTo(MembershipTiers::class);
    }

    public function criterias()
    {
        return $this->hasMany(UserMembershipCriteria::class);
    }

    public function getMembershipNameAttribute()
    {
        return $this->membership->name ?? null;
    }
}
