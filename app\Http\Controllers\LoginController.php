<?php
namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Emails\ForgotPassword;
use App\Models\Retailer;

class LoginController extends Controller
{
    function login()
    {
        Artisan::call('storage:link');

        if (Session::get('user_name')) {
            return redirect('/index');
        }
        return  view('login');
    }

    function checklogin(Request $req)
    {
        $email_address = $req->user_name;
        $password = $req->user_password;

        if(session()->get('portal') == 'retailer'):
            $data = Retailer::where('user_name', $email_address)
                ->where('is_active', 1)
                ->whereNull('deleted_at')
                ->where('user_type', '2')
                ->first();
            $user_password = $data->user_password ?? '';
            $route = 'retailer.login';
        else:
            $data = Admin::where('user_name', $email_address)
                ->where('is_active', 1)
                ->whereNull('deleted_at')
                ->where('user_type', '1')
                ->first();
            $user_password = $data->user_password ?? '';
            $route = '/';
        endif;

        if(!$data):
            Session::flash('error', 'Email address not found!');
            return redirect($route);
        endif;

        if(!Hash::check($password, $user_password)):
            Session::flash('error', 'Password is incorrect!');
            return redirect($route);
        endif;

        $req->session()->put('user', $data);
        $req->session()->put('user_name', $email_address);
        $req->session()->put('user_type', $data->user_type);

        return redirect('index');
    }

    function logout()
    {
        if(session()->get('portal') == 'retailer'):
            $route = 'retailer.login';
        else:
            $route = '/';
        endif;

        session()->pull('user');
        session()->pull('user_name');
        session()->pull('user_type');
        return redirect(route($route));
    }

    function forgotPassword()
    {
        return view('forgotPassword');
    }

    function resetLink(Request $request)
    {
        $email_address = $request->email_address;
        if(session()->get('portal') == 'retailer'):
            $user = Retailer::where('user_name', $email_address)
                ->where('is_active', 1)
                ->where('user_type', 2)
                ->first();
            $user_id = $user->user_id ?? '';
            $user_name = $user->first_name ?? '';
            $route = 'retailer.forgotPassword';
            $resetRoute = 'retailer.resetPasswordVerify';
            $email_type = 'retailer';
        else:
            $user = Admin::where('user_name', $email_address)
                ->where('is_active', 1)
                ->where('user_type', 1)
                ->whereNull('deleted_at')
                ->first();
            $user_id = $user->user_id ?? '';
            $route = 'forgotPassword';
            $resetRoute = 'resetPasswordVerify';
            $email_type = 'admin';
        endif;

        if(!$user):
            Session::flash('error', 'Email address not found!');
            return redirect(route($route));
        endif;

        $expired_duration = date("Y-m-d H:i:s", strtotime("+1 days"));
        $link_exp = date("Y-m-d H:i:s", strtotime("+".$expired_duration));
        $token = str_replace("/", "", Hash::make($link_exp.$email_address));

        PasswordReset::create([
            'email' => $email_address,
            'user_id' => $user_id,
            'token' => $token,
            'expired_date' => $link_exp
        ]);

        $link = route($resetRoute, ['token' => $token]);
        $email_data = [
            'type' => $email_type,
            'user_name' => $user_name ?? '',
            'link' => $link,
            'link_exp' => $expired_duration
        ];
        Mail::to($email_address)->send(new ForgotPassword($email_data));

        Session::flash('message', 'Reset link is sent!');
        return redirect(route($route));
    }

    function resetPasswordVerify($token='')
    {
        if(session()->get('portal') == 'retailer'):
            $route = 'retailer.resetPassword';
        else:
            $route = 'resetPassword';
        endif;

        if($token):
            if(session()->get('portal') == 'retailer'):
                $reset = PasswordReset::with(['retailer'])->where('token', $token)->first();
            else:
                $reset = PasswordReset::with(['admin'])->where('token', $token)->first();
            endif;

            if(!$reset):
                $msg = __('Account not found or the link is not available.');
            else:
                if($reset->expired_date < date('Y-m-d H:i:s')):
                    $msg = __('The link is expired.<br>Please request again.');
                endif;
            endif;
        else:
            $msg = __('Your password has been reset.<br>You may login through the app now.');
        endif;

        return view('resetPassword', [
            'token' => $token,
            'msg' => $msg ?? null,
            'url' => route($route)
        ]);
    }

    function resetPassword(Request $request)
    {
        $token = $request->token;
        if(session()->get('portal') == 'retailer'):
            $reset = PasswordReset::with(['retailer'])->where('token', $token)->first();
            $route = 'retailer.resetPasswordVerify';
            $home_route = 'retailer.login';
        else:
            $reset = PasswordReset::with(['admin'])->where('token', $token)->first();
            $route = 'resetPasswordVerify';
            $home_route = '/';
        endif;

        if(!$reset):
            Session::flash('error', 'Account not found or the link is not available.');
            return redirect()->route($route, $token);
        endif;

        if($request->password != $request->confirm_password):
            Session::flash('error', 'New password is not match with confirm password.');
            return redirect()->route($route, $token);
        endif;

        if(session()->get('portal') == 'retailer'):
            Retailer::where('user_id', $reset->retailer->user_id)
                ->update(['user_password' => Hash::make($request->password)]);
        else:
            Admin::where('user_id', $reset->admin->user_id)
                ->update(['user_password' => Hash::make($request->password)]);
        endif;

        PasswordReset::where('token', $token)->delete();

        Session::flash('message', 'Password is reseted. You may login now.');
        return redirect(route($home_route));
    }
}