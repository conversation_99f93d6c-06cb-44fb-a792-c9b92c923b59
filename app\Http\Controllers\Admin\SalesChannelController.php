<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\SalesChannel;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class SalesChannelController extends Controller
{
    function index()
    {
        if(!checkPermission('view_sales_channels')):
            return redirect(route('index'))->with('error', __('You do not have permission to view sales channel.'));
        endif;

        $bulk_action = [
            // 'delete' => [
            //     'text' => __('Delete Selected'),
            //     'url' => route('salesChannelDelete')
            // ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('salesChannelExport'),
                'filename' => 'sales_channels'
            ]
        ];

        // if(!checkPermission('delete_sales_channels')):
        //     unset($bulk_action['delete']);
        // endif;
        if(!checkPermission('export_sales_channels')):
            unset($bulk_action['export']);
        endif;

        $channels = SalesChannel::where('channel_id', 0)
            ->orderBy('title', 'ASC')
            ->get();
        $selected_channel = request()->channel ?? 0;

        $moduleID = 'sales_channels';
        return view('sales-channels', compact('bulk_action', 'channels', 'selected_channel', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'title';
        $sort_by = $request->sort_by ?? 'ASC';
        $channel_id = $request->channel ?? 0;

        $result = SalesChannel::where('channel_id', $channel_id)->when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('title', 'LIKE', "%{$search}%");
            })
            ->whereIn('brand', userBrand())
            ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="sales_channelsList">
                    <span></span>
                </label>';

                // $action = '<div class="d-flex flex-wrap justify-content-center">';
                // if(checkPermission('edit_sales_channels')):
                //     $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-name="'.$item->name.'" data-status="'.$item->is_active.'" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                // endif;
                // if(checkPermission('delete_sales_channels')):
                //     $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                // endif;
                // $action .= '</div>';

                $param = [
                    $checkbox,
                    config('staticdata.brand')[$item->brand],
                    $item->title,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    // $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_sales_channels')):
                    unset($param[4]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = SalesChannel::find($request->id);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;

        if($id):
            $msg = __('Updated!');
            $query = SalesChannel::find($id);
        else:
            $msg = __('Added!');
            $query = new SalesChannel();
        endif;

        $channel_id = $request->channel_id ?? 0;
        if($channel_id && !$id):
            // add new channel
            foreach($request->channel_name as $key => $value):
                if($key != 'new_row' && $value):
                    $query = new SalesChannel();
                    $query->channel_id = $channel_id;
                    $query->title = $value;
                    $query->brand =  SalesChannel::find($channel_id)->brand;
                    $query->save();
                endif;
            endforeach;
        else:
            $query->title = $request->title;
            $query->brand = $request->brand;
            $query->save();
        endif;

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            SalesChannel::find($request->id)->delete();
        else:
            SalesChannel::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'title';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $channel_id = $request->channel ?? 0;

        $result = SalesChannel::where('channel_id', $channel_id)
            ->when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('title', 'LIKE', "%{$search}%");
            })
            ->whereIn('brand', userBrand())
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'sales_channels_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'sales_channels'),
            $fileName
        );
    }

    function ajaxChannel($first='')
    {
        $result = SalesChannel::where('channel_id', 0)
            ->when(request()->search, function($q) {
                $search = request()->search;
                $q->where('title', 'LIKE', "%{$search}%");
            })
            ->orderBy('title', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->title.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function ajaxValue($first='')
    {
        $result = SalesChannel::where('channel_id', request()->channel_id)
            ->when(request()->search, function($q) {
                $search = request()->search;
                $q->where('title', 'LIKE', "%{$search}%");
            })
            ->orderBy('title', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->title.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }
}
