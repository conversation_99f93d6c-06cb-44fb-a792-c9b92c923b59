<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\RetailerProduct;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\GeneralExport;

class RetailerProductController extends Controller
{
    function index()
    {
        if (!checkPermission('view_retailer_product') || !is_retailer()):
            return redirect(route('index'))->with('error', __('You do not have permission to view retailer product.'));
        endif;

        $bulk_action = [];
        $moduleID = 'retailer_products';
        return view('retailer-product', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'available_stock';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = RetailerProduct::when($request->input('search.value'), function($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($query) use ($search) {
                    $query->where('product_name', 'LIKE', "%{$search}%")
                        ->orWhere('variation_name', 'LIKE', "%{$search}%");
                });
            })
            ->when($request->brand, function($query) use($request) {
                $query->where('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->where('country', $request->country);
            })
            ->when(is_retailer(), function($query) {
                $query->where('retailer_id', session('user')->user_id);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0) {
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item) {
                $item_id = $item->id;
                $currency = $item->country == 'malaysia' ? global_settings('currency') : global_settings('currency_sg');
                $price = '<div class="input-group mb-2">'.
                    '<div class="input-group-prepend">'.
                        '<span class="input-group-text">'. $currency .'</span>
                    </div>'.
                    '<input type="text" name="selling_price['.$item_id.']" class="form-control" value="'.$item->selling_price.'">
                </div>'.
                '<input type="hidden" name="item_id[]" value="'.$item_id.'">';

                $discount_type = '<select name="discount_type['.$item_id.']" class="form-control select2">'.    
                    '<option value="">'. __('Select Type') .'</option>';
                    foreach(config('staticdata.discount_type') as $key => $value):
                        $discount_type .= '<option '. ($item->discount_type == $key ? 'selected' : '') .' value="'.$key.'">'. __($value) .'</option>';
                    endforeach;
                $discount_type .= '</select>';

                $discount_value = '<input type="text" name="discount_value['.$item_id.']" class="form-control mt-1" value="'.$item->discount_value.'" />';

                $discount_period = '<input type="text" name="discount_period['.$item_id.']" class="form-control datetimerange" placeholder="'. __('Select Date') .'" value="' . ($item->discount_date ?? '') .'" />';

                $param = [
                    $item->retailer->first_name ?? '',
                    $item->product_name . ' <div class="text-muted">' . $item->variation_name.'</div>',
                    config('staticdata.brand.' . $item->brand),
                    config('staticdata.country.' . $item->country),
                    '<div class="text-center">'.number_format($item->available_stock, 0, '.', ',').'</div>',
                    $price,
                    $discount_type . $discount_value,
                    $discount_period,
                ];

                if(is_retailer()):
                    unset($param[0]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            }
        }

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function update(Request $request)
    {
        $item_id = $request->item_id ?? '';
        if(!$item_id):
            return redirect(route('retailerProduct'))->with('error', __('No product to update.'));
        endif;

        $products = RetailerProduct::whereIn('id', $item_id)->get();
        foreach($products as $item):
            $id = $item->id;
            $discount_period = convertDatePeriodToDbFormat($request->discount_period[$id] ?? '');

            $item->selling_price = $request->selling_price[$id] ?? 0;
            $item->discount_value = $request->discount_value[$id] ?? 0;
            $item->discount_type = $request->discount_type[$id] ?? 0;
            $item->discount_start = $discount_period['start_date'] ?? null;
            $item->discount_end = $discount_period['end_date'] ?? null;
            $item->save();
        endforeach;

        return redirect(route('retailerProduct'))->with('message', __('Updated!'));
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'available_stock';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = RetailerProduct::when($request->input('search.value'), function($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($query) use ($search) {
                    $query->where('product_name', 'LIKE', "%{$search}%")
                        ->orWhere('variation_name', 'LIKE', "%{$search}%");
                });
            })
            ->when($request->brand, function($query) use($request) {
                $query->where('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->where('country', $request->country);
            })
            ->when(is_retailer(), function($query) {
                $query->where('retailer_id', session('user')->user_id);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->currency = $item->country == 'malaysia' ? global_settings('currency') : global_settings('currency_sg');
                if($item->discount_start):
                    $item->discount_start = Carbon::parse($item->discount_start)->format(config('app.display_datetime_format'));
                endif;
                if($item->discount_end):
                    $item->discount_end = Carbon::parse($item->discount_end)->format(config('app.display_datetime_format'));
                endif;
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'retailer_product_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'retailer_product'),
            $fileName
        );
    }
}
