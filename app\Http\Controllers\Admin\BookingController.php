<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\DoctorController;
use Illuminate\Http\Request;
use App\Models\Bookings;
use App\Models\Doctors;
use App\Models\GlobalFunction;
use App\Models\Users;
use App\Models\Outlets;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\BookingsTraits;

class BookingController extends Controller
{
    use BookingsTraits;

    function bookings($type)
    {
        $outlet_list = Outlets::where('is_active', 1)->get();
        $user_list = (new UsersController)->usersDropdownList('All Members');
        $user_list = $user_list->getData()->data;

        $doctor_list = (new DoctorController)->doctorsDropdownList('All Stringers');
        $doctor_list = $doctor_list->getData()->data;

        $moduleID = 'bookings';
        return view('bookings', compact('type', 'user_list', 'doctor_list', 'moduleID', 'outlet_list'));
    }

    function bookingsList(Request $request)
    {
        $sort_col = $request->sort_col ?? 'bookings.created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $type = $request->type;
        $payment_status = $request->payment;
        $booking_status = $request->status;
        $changes_status = $request->changes;

        $result = Bookings::join('booking_items', 'bookings.id', '=', 'booking_items.booking_id')
            ->select('bookings.*', 'booking_items.*', 'bookings.id as id')
            ->when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('ref_no', 'LIKE', "%{$search}%");
            })
            ->when($request->date, function($q) use ($request) {
                $date_exp = explode(" to ", $request->date);
                $q->where('booking_items.booking_date', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                    ->where('booking_items.booking_date', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
            })
            ->when($request->outlet, function($q) use ($request) {
                $q->where('booking_items.outlet_id', $request->outlet);
            })
            ->when(!$payment_status && !$booking_status, function($q) use ($type) {
                $q->when(in_array($type, ['paid', 'unpaid']), function($q) use ($type) {
                    $q->where('bookings.payment_status', $type);
                })
                ->when(!in_array($type, ['paid', 'unpaid', 'all']), function($q) use ($type) {
                    $q->where('bookings.booking_status', $type);
                });
            })
            ->when($payment_status, function($q) use ($payment_status) {
                $q->where('bookings.payment_status', $payment_status);
            })
            ->when($booking_status, function($q) use ($booking_status) {
                $q->where('bookings.booking_status', $booking_status);
            })
            ->when($changes_status, function($q) use ($changes_status) {
                $q->whereHas('itemChanges', function($q) use ($changes_status) {
                    $q->where('acceptance_status', $changes_status);
                });
            })
            ->when($request->user, function($q) use ($request) {
                $q->where('bookings.user_id', $request->user);
            })
            ->when($request->doctor, function($q) use ($request) {
                $q->where('booking_items.stringer_id', $request->doctor);
            })
            ->orderBy($sort_col, $sort_by);
        if($sort_col == 'booking_items.booking_date'):
            $result = $result->orderBy('booking_items.booking_time', $sort_by);
        endif;
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();
       
            foreach ($result as $item):
                $booking_detail = '<a href="' . route('bookingsDetail', $item->id) . '">'.$item->ref_no.'</a>'.
                    '<div class="text-grey">'.Carbon::parse($item->created_at)->format('d-m-Y, g:i a').'</div>';

                $book_datetime = Carbon::parse($item->booking_date.' '.$item->booking_time)->format('Y-m-d H:i:s');
                $booking_date = '<span style="display:none">'.$book_datetime.'</span>'.Carbon::parse($item->booking_date)->format('d-m-Y').'<br><span class="text-grey">'.Carbon::parse($item->booking_time)->format('g:i a')."</span></div>";

                $user = $item->user->first_name.' '.$item->user->last_name.
                    '<div class="text-grey">'.$item->user->email_address.'</div>'.
                    '<div>'.$item->user->phone_number.'</div>';

                $booking_items = $item->bookingItems()->first();
                $doctor = $booking_items->staff->first_name.' '.$booking_items->staff->last_name.
                '<div><a href="'.route('doctorsProfile', $booking_items->stringer_id) .'"><span class="btn btn-sm btn-info font-11 mt-1">'.__('View').'</span></a></div>';

                switch($item->booking_status):
                    case 'pending': $status_class = 'badge-warning'; break;
                    case 'confirmed': $status_class = 'badge-warning'; break;
                    case 'received_at_outlet': $status_class = 'badge-success'; break;
                    case 'ready_for_collect': $status_class = 'badge-success'; break;
                    case 'completed': $status_class = 'badge-success'; break;
                    case 'cancelled': $status_class = 'badge-danger'; break;
                endswitch;

                switch($item->payment_status):
                    case 'unpaid': $payment_class = 'text-danger'; break;
                    case 'paid': $payment_class = 'text-success'; break;
                endswitch;

                $status = '<div class="text-center">
                    <span class="badge py-1 '.$status_class.'">'.__($item->booking_status).'</span>';

                if(in_array($item->booking_status, ['pending', 'confirmed']) && $item->itemChanges):
                    switch($item->itemChanges->acceptance_status):
                        case 'pending': $status_text = __('Pending Changes Acceptance'); break;
                        case 'accepted': $status_text = __('Changes Accepted'); break;
                        case 'rejected': $status_text = __('Changes Rejected'); break;
                    endswitch;

                    $status .= '<div class="font-12 mt-1">'.$status_text.'</div>';
                endif;

                    $status .= '<div class="'.$payment_class.' mt-1 fw-500">'.__($item->payment_status).'</div>
                </div>';

                $action = '<div class="d-flex flex-wrap justify-content-center">
                    <a href="' . route('bookingsDetail', $item->id) . '" class="btn-icon btn-info" data-toggle="tooltip" data-title="'.__('View').'"><i class="fa fa-search"></i></a>';
                
                $action .= '</div>';

                $param = [
                    $booking_detail,
                    $booking_date,
                    $item->outlet_name,
                    $user ?? null,
                    $doctor ?? null,
                    $status,
                    formatNumber($item->total, 2),
                    '+'.formatNumber($item->earned_point),
                    $action,
                ];

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function bookingsExport(Request $request)
    {
        $type = $request->type;
        $payment_status = $request->payment;
        $booking_status = $request->status;
        $changes_status = $request->changes;
        
        $result = Bookings::join('booking_items', 'bookings.id', '=', 'booking_items.booking_id')
            ->select('bookings.*', 'booking_items.*')
            ->when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('ref_no', 'LIKE', "%{$search}%");
            })
            ->when($request->date, function($q) use ($request) {
                $date_exp = explode(" to ", $request->date);
                $q->where('booking_items.booking_date', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                    ->where('booking_items.booking_date', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
            })
            ->when($request->outlet, function($q) use ($request) {
                $q->where('booking_items.outlet_id', $request->outlet);
            })
            ->when(!$payment_status && !$booking_status, function($q) use ($type) {
                $q->when(in_array($type, ['paid', 'unpaid']), function($q) use ($type) {
                    $q->where('bookings.payment_status', $type);
                })
                ->when(!in_array($type, ['paid', 'unpaid', 'all']), function($q) use ($type) {
                    $q->where('bookings.booking_status', $type);
                });
            })
            ->when($payment_status, function($q) use ($payment_status) {
                $q->where('bookings.payment_status', $payment_status);
            })
            ->when($booking_status, function($q) use ($booking_status) {
                $q->where('bookings.booking_status', $booking_status);
            })
            ->when($changes_status, function($q) use ($changes_status) {
                $q->whereHas('itemChanges', function($q) use ($changes_status) {
                    $q->where('acceptance_status', $changes_status);
                });
            })
            ->when($request->user, function($q) use ($request) {
                $q->where('bookings.user_id', $request->user);
            })
            ->when($request->doctor, function($q) use ($request) {
                $q->where('booking_items.stringer_id', $request->doctor);
            })
            ->orderBy('booking_items.booking_date', 'DESC')
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->user_name = $item->user->first_name.' '.$item->user->last_name;
                $item->user_email = $item->user->email_address;
                $item->user_phone = $item->user->phone_number;

                $booking_item = $item->bookingItems()->first();
                $item->stringer_name = $booking_item->staff->first_name.' '.$booking_item->staff->last_name;
                $item->booking_date = Carbon::parse($booking_item->booking_date)->format('d-m-Y');
                $item->booking_time = Carbon::parse($booking_item->booking_time)->format('g:i a');
                $item->outlet_name = $booking_item->outlet_name;
                $item->string_type = $booking_item->string_type_name;
                $item->string_tension = $booking_item->string_tension_name;
            endforeach;
        endif;

        $fileName = 'bookings_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'bookings'),
            $fileName
        );
    }

    function bookingsDetail($id)
    {
        $booking = Bookings::where('id', $id)
            ->first();

        $booking_item = $booking->bookingItems()->first();
        $booking_item->booking_date = Carbon::parse($booking_item->booking_date)->format('d-m-Y');
        $booking_item->booking_time = Carbon::parse($booking_item->booking_time)->format('g:i a');

        switch($booking->booking_status):
            case 'pending': $booking->status_class = 'text-warning'; break;
            case 'confirmed': $booking->status_class = 'text-warning'; break;
            case 'received_at_outlet': $booking->status_class = 'text-success'; break;
            case 'ready_for_collect': $booking->status_class = 'text-success'; break;
            case 'completed': $booking->status_class = 'text-success'; break;
            case 'cancelled': 
                $booking->status_class = 'text-danger'; 
                $cancel = $booking->statusLogs()->where('status', 'cancelled')->orderBy('id', 'desc')->first();
                $booking->cancel_reason = $cancel->remarks ?? null;
                break;
        endswitch;

        $booking->changes_status = $booking_item->itemChanges->acceptance_status ?? null;

        $moduleID = 'bookings';
        return view('booking-details', [
            'moduleID' => $moduleID,
            'booking' => $booking,
            'booking_item' => $booking_item,
        ]);
    }

    function bookingsUpdate(Request $request)
    {
        $id = $request->id;
        $action = $request->action;
        $remarks = $request->remarks ?? null;

        $data = [
            'id' => $id,
            'remarks' => $remarks,
        ];

        if($action == 'paid'):
            $data['payment_status'] = 'paid';
            $data['booking_status'] = 'confirmed';
        else:
            $data['booking_status'] = $action;
        endif;
        $result = $this->updateBookingStatus($data);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendSimpleResponse(true, $result['msg']);
    }

    function bookingsEdit(Request $request)
    {
        $this->editBooking($request);
        
        return GlobalFunction::sendSimpleResponse(true, 'Booking\'s changes is updated and have notify member for acceptance.');
    }
}