<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PushNotification;

class NotificationController extends Controller
{
    function insertPushNotification($data)
    {
        return PushNotification::create([
            'type' => $data['type'],
            'type_id' => $data['type_id'],
            'user_type' => $data['user_type'],
            'user_id' => $data['user_id'],
            'title' => $data['title'],
            'message' => $data['message'],
            'extra_data' => isset($data['extra_data']) ? json_encode($data['extra_data']) : null,
        ]);
    }
}