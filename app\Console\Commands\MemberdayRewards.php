<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserVouchers;
use App\Traits\VoucherTraits;
use App\Models\GlobalFunction;
use Illuminate\Console\Command;
use App\Models\RedemptionVouchers;

class MemberdayRewards extends Command
{
    use VoucherTraits;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'memberday:rewards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send memberday rewards to all users';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $memberday = global_settings('memberday_every_month') ?? 10;
        $memberday_vouchers = global_settings('memberday_vouchers') ?? [];

        if (date('t') < $memberday) {
            $memberday = date('t');
        }

        $today = date('j');

        if ($today == 18) {
            $vouchers = RedemptionVouchers::whereNull('deleted_at')
                ->where('is_active', true)
                ->whereIn('id', json_decode($memberday_vouchers))
                ->get();

            $users = User::where('is_active', true)->whereNull('deleted_at')->get();

            if (count($users) > 0 && count($vouchers) > 0) {
                foreach ($users as $user) {
                    foreach ($vouchers as $item) {
                        $voucherCode = $this->userVoucherCode($item);
        
                        UserVouchers::create([
                            'user_id' => $user->id,
                            'voucher_id' => $item->id,
                            'voucher_code' => $voucherCode,
                            'voucher_value' => $item->value,
                            'effective_start_date' => $item->available_start_date,
                            'effective_end_date' => $item->available_end_date,
                            'voucher_data' => json_encode($item)
                        ]);
                    }

                    
                }
            }
        }
    }
}
