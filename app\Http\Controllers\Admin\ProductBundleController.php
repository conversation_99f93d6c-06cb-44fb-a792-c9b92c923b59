<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ProductBundle;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\ProductBundleGift;
use App\Models\ProductBundlePurchase;
use App\Models\ProductBundleDiscounts;
use App\Models\ProductVariations;
use App\Exports\GeneralExport;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class ProductBundleController extends Controller
{
    function index()
    {
        if (!checkPermission('view_product_bundle')):
            return redirect(route('index'))->with('error', __('You do not have permission to view product bundle.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('productBundleDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('productBundleExport'),
                'filename' => 'product_bundles'
            ]
        ];

        if (!checkPermission('delete_product_bundle')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_product_bundle')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'product-bundle';
        return view('product-bundle', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = ProductBundle::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
                $q->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('sku', 'LIKE', "%{$search}%")
                        ->orWhereHas('product', function($q) use ($search) {
                            $q->where('name', 'LIKE', "%{$search}%");
                        });
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->mix) && $request->mix != 'all', function ($query) use ($request) {
                $query->where('is_mix_match', $request->mix);
            })
            ->when($request->type, function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                        <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="product-bundleList">
                        <span></span>
                    </label>';

                if (!checkPermission('edit_product_bundle')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                            <label class="switch mb-0">
                                <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                                <span class="slider round"></span>
                            </label>
                        </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_product_bundle')):
                    $action .= '<a href="' . route('productBundleForm', ['id' => $item->id]) . '" class="btn-icon btn-info mr-1" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_product_bundle')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    $item->sku,
                    config('staticdata.product.bundle_type')[$item->type] ?? '',
                    $item->product->name ?? '',
                    $item->is_mix_match == 1 ? __('Yes') : __('No'),
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_product_bundle')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function form(Request $request)
    {
        $id = $request->id ?? null;
        $type = $request->type ?? null;
        $tab = $request->tab ?? 'details';

        if(!$id):
            $title = __('Add Product Bundle');
            $result = new ProductBundle();
            $result->is_active = 1;
        else:
            $title = __('Edit Product Bundle');
            $result = ProductBundle::find($id);
            $type = $result->type;

            if($tab == 'gwp' && $result->gwp->count() > 0):
                foreach($result->gwp as $item):
                    if($item->type == 'quantity'):
                        $item->threshold = round($item->threshold, 0);
                    endif;

                    if($item->gift_product_variation_id):
                        $item->gift_product_variation_id = json_decode($item->gift_product_variation_id);
                    endif;
                endforeach;

                $result->gwp = $result->gwp->sortBy('product_bundle_discount_id')
                    ->sortBy('threshold')
                    ->sortBy('type')
                    ->values()
                    ->collect();
            elseif($tab == 'pwp' && $result->pwp->count() > 0):
                foreach($result->pwp as $item):
                    if($item->type == 'quantity'):
                        $item->threshold = round($item->threshold, 0);
                    endif;

                    if($item->product_variation_id):
                        $item->product_variation_id = json_decode($item->product_variation_id);
                    endif;
                endforeach;

                $result->pwp = $result->pwp->sortBy('product_bundle_discount_id')
                    ->sortBy('threshold')
                    ->sortBy('type')
                    ->values()
                    ->collect();
            endif;
        endif;

        $memberships = MembershipTiers::where('is_active', true)->get();

        $data = [
            'title' => $title,
            'result' => $result,
            'id' => $id,
            'type' => $type,
            'tab' => $tab,
            'memberships' => $memberships,
        ];

        return view('product-bundle-form', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $tab = $request->tab;
        $msg = ($id ? __('Updated!') : __('Added!'));

        switch($tab):
            case 'details': $id = $this->addUpdateDetails($request); break;
            case 'gwp': $this->addUpdateGwp($request); break;
            case 'pwp': $this->addUpdatePwp($request); break;
        endswitch;

        return redirect(route('productBundleForm', ['id' => $id]).'?tab='.$tab)->with('message', $msg);
    }

    function addUpdateDetails($request)
    {
        $id = $request->id ?? null;
        if ($id):
            $query = ProductBundle::find($id);
            $type = $query->type;
        else:
            $query = new ProductBundle();
            $type = $request->type;
        endif;

        $query->name = $request->name;
        $query->sku = $request->sku;
        $query->type = $type;
        $query->brand = $request->brand;
        $query->product_id = $request->product_id;
        $query->is_mix_match = $request->is_mix_match ?? 0;
        $query->is_active = $request->is_active ?? 0;
        $query->save();
        $id = $query->id;

        if ($type == 'quantity_discount'):
            $quantity = $request->quantity;
            $discount_exist = [];
            foreach($quantity as $key => $value):
                if ($key != 'new_row'):
                    $discount_id = $request->discount_id[$key] ?? '';

                    if(!$discount_id):
                        $discount = new ProductBundleDiscounts();
                    else:
                        $discount = ProductBundleDiscounts::find($discount_id);
                    endif;
                    $discount->product_bundle_id = $id;
                    $discount->quantity = $value;
                    $discount->price = $request->price[$key];
                    $discount->price_sg = $request->price_sg[$key];
                    $discount->max_set = $request->max_set[$key] ?? null;
                    $discount->membership_tier_ids = json_encode($request->memberships[$key]);
                    $discount->save();

                    $discount_exist[] = $discount->id;
                endif;
            endforeach;

            if($discount_exist):
                ProductBundleDiscounts::where('product_bundle_id', $id)
                    ->whereNotIn('id', $discount_exist)
                    ->delete();
            endif;
        endif;

        return $id;
    }

    function addUpdateGwp($request)
    {
        $id = $request->id;

        $type = $request->type;
        $gwp_exist = [];
        foreach($type as $key => $type_text):
            if ($key != 'new_row'):
                $gwp_id = $request->gwp_id[$key] ?? '';
                $discount_package = $request->package[$key] ?? '';
                $variation_id = $request->product_variation_id[$key] ?? '';

                if($type_text == 'quantity'):
                    $threshold_my = $request->threshold[$key];
                    $threshold_sg = $request->threshold[$key];
                else:
                    $threshold_my = $request->threshold_my[$key];
                    $threshold_sg = $request->threshold_sg[$key];
                endif;

                if(!$gwp_id):
                    $query = new ProductBundleGift();
                else:
                    $query = ProductBundleGift::find($gwp_id);
                endif;
                $query->product_bundle_id = $id;
                $query->product_bundle_discount_id = ($discount_package ? json_encode($discount_package) : null);
                $query->type = $type_text;
                $query->threshold = $threshold_my;
                $query->threshold_sg = $threshold_sg;
                $query->gift_product_id = $request->product_id[$key];
                $query->gift_product_variation_id = ($variation_id ? json_encode($variation_id) : null);
                $query->quantity = $request->quantity[$key];
                $query->membership_tier_ids = json_encode($request->memberships[$key]);
                $query->save();

                $gwp_exist[] = $query->id;
            endif;
        endforeach;

        if($gwp_exist):
            ProductBundleGift::where('product_bundle_id', $id)
                ->whereNotIn('id', $gwp_exist)
                ->delete();
        else:
            ProductBundleGift::where('product_bundle_id', $id)->delete();
        endif;
    }

    function addUpdatePwp($request)
    {
        $id = $request->id;

        $type = $request->type;
        $pwp_exist = [];
        foreach($type as $key => $type_text):
            if ($key != 'new_row'):
                $pwp_id = $request->pwp_id[$key] ?? '';
                $discount_package = $request->package[$key] ?? '';
                $variation_id = $request->product_variation_id[$key] ?? '';

                if($type_text == 'quantity'):
                    $threshold_my = $request->threshold[$key];
                    $threshold_sg = $request->threshold[$key];
                else:
                    $threshold_my = $request->threshold_my[$key];
                    $threshold_sg = $request->threshold_sg[$key];
                endif;

                if(!$pwp_id):
                    $query = new ProductBundlePurchase();
                else:
                    $query = ProductBundlePurchase::find($pwp_id);
                endif;
                $query->product_bundle_id = $id;
                $query->product_bundle_discount_id = ($discount_package ? json_encode($discount_package) : null);
                $query->type = $type_text;
                $query->threshold = $threshold_my;
                $query->threshold_sg = $threshold_sg;
                $query->product_id = $request->product_id[$key];
                $query->product_variation_id = ($variation_id ? json_encode($variation_id) : null);
                $query->max_quantity = $request->max_quantity[$key];
                $query->price = $request->price[$key];
                $query->price_sg = $request->price_sg[$key];
                $query->membership_tier_ids = json_encode($request->memberships[$key]);
                $query->save();

                $pwp_exist[] = $query->id;
            endif;
        endforeach;

        if($pwp_exist):
            ProductBundlePurchase::where('product_bundle_id', $id)
                ->whereNotIn('id', $pwp_exist)
                ->delete();
        else:
            ProductBundlePurchase::where('product_bundle_id', $id)->delete();
        endif;
    }

    function delete(Request $request)
    {
        if ($request->id):
            ProductBundle::find($request->id)->delete();
        else:
            ProductBundle::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = ProductBundle::find($request->id);
        
        if($request->value == 1):
            $exist_data = new Request([
                'bundle_type' => $item->type,
                'product_id' => $item->product_id,
                'id' => $item->id
            ]);
            $exist = $this->checkProductExist($exist_data);
            if($exist):
                return GlobalFunction::sendSimpleResponse(false, 'One product can have one type of bundle only.');
            endif;
        endif;

        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = ProductBundle::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
                $q->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('sku', 'LIKE', "%{$search}%")
                        ->orWhereHas('product', function($q) use ($search) {
                            $q->where('name', 'LIKE', "%{$search}%");
                        });
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->mix) && $request->mix != 'all', function ($query) use ($request) {
                $query->where('is_mix_match', $request->mix);
            })
            ->when($request->type, function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'product_bundles_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'product_bundles'),
            $fileName
        );
    }

    function checkProductExist(Request $request) // each product can have one type of bundle only
    {
        $bundle_type = $request->bundle_type;
        $product_id = $request->product_id;
        $id = $request->id ?? null;
        $result = ProductBundle::where('product_id', $product_id)
            ->when($id, function($q) use ($id) {
                $q->where('id', '!=', $id);
            })
            ->where('type', $bundle_type)
            ->where('is_active', 1)
            ->count();

        return $result;
    }

    function getVariationData($variation_id)
    {
        if($variation_id):
            $variation_id = json_decode($variation_id);

            $variation = ProductVariations::whereIn('id', $variation_id)->get();
            $variation = $variation->map(function($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->variation_name,
                    'sku' => $item->sku,
                    'image' => $item->image_url,
                ];
            })->toArray();

            return $variation;
        endif;
    }
}
