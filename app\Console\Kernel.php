<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
     /**
      * Define the application's command schedule.
      *
      * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
      * @return void
      */
     protected function schedule(Schedule $schedule)
     {
          // $schedule->command('appointment_auto_cancelled:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->everyMinute();

          // $schedule->command('booking_reminder:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->everyMinute();

          /*$schedule->command('custom_notifications:cron')
               ->timezone('Asia/Kuala_Lumpur')
               ->everyMinute();


          $schedule->command('medicine_delivery_auto_cancelled:cron')
               ->timezone('Asia/Kuala_Lumpur')
               ->everyMinute();
               */

          // $schedule->command('point_expired:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->everyMinute();

          // $schedule->command('point_expiry_reminder')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('08:00');

          // $schedule->command('voucher_expiry_reminder')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('08:00');

          // $schedule->command('order_feedback_reminder')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('08:00');

          // $schedule->command('cart_reminder')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('08:00');

          /*$schedule->command('point_redemptions_expired:cron')
               ->timezone('Asia/Kuala_Lumpur')
               ->everyMinute();*/

          $schedule->command('birthday:voucher')
               ->timezone('Asia/Kuala_Lumpur')
               ->dailyAt('00:00');

          // $schedule->command('birthday_gift:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->monthlyOn(1, '00:00');

          // $schedule->command('membership_tier_expired:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('00:00');

          // $schedule->command('woocommerce:create')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('00:00');

          // $schedule->command('clear_log:cron')
          //      ->timezone('Asia/Kuala_Lumpur')
          //      ->dailyAt('00:00');

          $schedule->command('auto_issuance:voucher')
               ->timezone('Asia/Kuala_Lumpur')
               ->dailyAt('00:00');

          $schedule->command('memberday:rewards')
               ->timezone('Asia/Kuala_Lumpur')
               ->dailyAt('09:00');
     }

     /**
      * Register the commands for the application.
      *
      * @return void
      */
     protected function commands()
     {
          $this->load(__DIR__ . '/Commands');

          require base_path('routes/console.php');
     }
}
