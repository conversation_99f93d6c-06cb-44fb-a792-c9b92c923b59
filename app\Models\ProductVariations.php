<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Admin\ProductsController;

class ProductVariations extends Model
{
    use HasFactory;
    public $table = "product_variations";
    public $timestamps = true;

    protected $fillable = [
        'product_id',
        'attribute_id',
        'sku',
        'quantity',
        'retailer_price_my',
        'retailer_price_sg',
        'sequence',
        'is_active',
    ];

    public function product()
    {
        return $this->belongsTo(Products::class, 'product_id', 'id');
    }

    public function getVariationNameAttribute()
    {
        return (new ProductsController)->getVariationName($this->attribute_id);
    }

    public function getImageUrlAttribute()
    {
        if($this->image):
            return GlobalFunction::createMediaUrl($this->image);
        else:
            return $this->product->single_image_url;
        endif;
    }
}