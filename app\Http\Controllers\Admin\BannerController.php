<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Banners;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class BannerController extends Controller
{
    function index()
    {
        if(!checkPermission('view_banners')):
            return redirect(route('index'))->with('error', __('You do not have permission to view banner.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('bannerDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('bannerExport'),
                'filename' => 'banner'
            ]
        ];

        if(!checkPermission('delete_banners')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_banners')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'banners';
        return view('banners', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'sort_order';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = Banners::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('name', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->when(isset($request->type) && $request->type != 'all', function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="bannersList">
                    <span></span>
                </label>';

                $image_src = GlobalFunction::createMediaUrl($item->image);
                $image = '<a href="'.$image_src.'" target="_blank" data-toggle="tooltip" data-title="'.__('Enlarge').'">
                        <img src="'.$image_src.'" style="max-width:120px; max-height: 120px;">
                    </a>';

                if(!checkPermission('edit_banners')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_banners')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_banners')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $image,
                    // config('staticdata.brand')[$item->brand] ?? $item->brand,
                    config('staticdata.banner_type')[$item->type] ?? $item->type,
                    $item->name,
                    $item->sort_order,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_banners')):
                    unset($param[6]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = Banners::find($request->id);
        if($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {

        $item = Banners::find($request->id);

        // $banner_enabled = Banners::active()->count();
        // $banner_enabled = Banners::active()->where('brand', $item->brand ?? $request->brand)->where('type', $request->type ?? $item->type)->count();

        // if($banner_enabled >= 5 && $request->status){
        //     return GlobalFunction::sendSimpleResponse(false, 'Maximum 5 banners per brand and type is allow enabled.');
        // }

        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = Banners::find($id);
        else:
            if(!$request->has('image')):
                return GlobalFunction::sendSimpleResponse(false, __('Please select image.'));
            endif;

            $msg = __('Added!');
            $query = new Banners();
        endif;

        $query->name = $request->name;
        // $query->brand = $request->brand;
        $query->type = $request->type;
        $query->url = $request->url;
        $query->sort_order = $request->sort_order;
        $query->is_vsmash = $request->is_vsmash ?? 0;
        $query->is_active = $request->status ?? 0;

        if($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            Banners::find($request->id)->delete();
        else:
            Banners::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Banners::find($request->id);

        $banner_enabled = Banners::active()->count();

        if($banner_enabled >= 5 && $request->value){
            return GlobalFunction::sendSimpleResponse(false, 'Maximum 5 banners per brand and type is allow enabled.');
        }
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'sort_order';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = Banners::when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('name', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'banners_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'banners'),
            $fileName
        );
    }
}