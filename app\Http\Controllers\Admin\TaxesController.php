<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Taxes;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class TaxesController extends Controller
{
    function index()
    {
        if(!checkPermission('view_tax')):
            return redirect(route('index'))->with('error', __('You do not have permission to view taxes.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('taxesDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('taxesExport'),
                'filename' => 'taxes'
            ]
        ];

        if(!checkPermission('delete_tax')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_tax')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'taxes';
        return view('taxes', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Taxes::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="taxesList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_tax')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_tax')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-name="' . $item->name . '" data-rate="' . $item->rate . '" data-status="' . $item->is_active . '" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_tax')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    $item->rate,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_tax') && !checkPermission('delete_tax')):
                    unset($param[5]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = Taxes::find($id);
        else:
            $msg = __('Added!');
            $query = new Taxes();
        endif;

        $query->name = $request->name;
        $query->rate = $request->rate;
        $query->is_active = $request->status ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = Taxes::find($request->id);
        else:
            $query = Taxes::whereIn('id', $request->selected);
        endif;

        // check if any product attached
        if($query->customer_tax->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This tax is attached with product(s).');
        endif;

        if($query->retailer_tax->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This tax is attached with product(s).');
        endif;
        // END check if any product attached

        $query = $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Taxes::find($request->id);

        // check if any product attached
        if($item->customer_tax->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This tax is attached with product(s).');
        endif;

        if($item->retailer_tax->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This tax is attached with retailer(s).');
        endif;
        // END check if any product attached
        
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Taxes::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;
        
        $fileName = 'taxes_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'taxes'),
            $fileName
        );
    }
}