<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\Doctors;
use App\Models\Outlets;
use App\Models\DoctorAppointmentSlots;
use App\Models\DoctorHolidays;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use App\Traits\EmailTraits;
use Maatwebsite\Excel\Facades\Excel;

class DoctorController extends Controller
{
    use EmailTraits;
    
    function doctors()
    {
        if(!checkPermission('view_doctor')):
            return redirect(route('index'))->with('error', __('You do not have permission to view staff.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('doctorsDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('doctorsExport'),
                'filename' => 'staffs'
            ]
        ];

        if(!checkPermission('delete_doctor')):
            unset($bulk_action['delete']);
        endif;

        if(!checkPermission('export_doctor')):
            unset($bulk_action['export']);
        endif;

        $outlets = Outlets::where('is_active', 1)
            ->orderBy('name', 'ASC')
            ->get();

        $moduleID = 'staffs';
        return view('doctors', compact('bulk_action', 'outlets', 'moduleID'));
    }

    function doctorsList(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Doctors::whereNull('deleted_at')
            ->when($request->input('search.value'), function($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('email_address', 'LIKE', "%{$search}%")
                        ->orWhere('mobile_number', 'LIKE', "%{$search}%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->outlet, function($query) use ($request) {
                $query->where('outlets', 'LIKE', '%"'.$request->outlet.'"%');
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();
       
            foreach ($result as $item):
                $id = $item->id;
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$id.'" class="check-selected form-check-input" data-datatable="doctorsList">
                    <span></span>
                </label>';

                $outlets = '';
                if($item->outlets):
                    $outlets = Outlets::whereIn('id', json_decode($item->outlets, true))
                        ->pluck('name')
                        ->toArray();
                    $outlets = implode('<br>', $outlets);
                endif;

                if(!checkPermission('edit_doctor')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_doctor')):
                    $action .= '<a href="'.route('doctorsProfile', $id).'" class="btn-icon btn-info mr-1" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_doctor')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->email_address,
                    $item->mobile_number,
                    $item->first_name,
                    $item->last_name,
                    $outlets,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action,
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_doctor') && !checkPermission('delete_doctor')):
                    unset($param[7]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function doctorsAddUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $email = $request->email_address;
        $phone = $request->mobile_number;

        // check for email unique
        $checkExist = Doctors::whereNull('deleted_at')
            ->where(function($q) use($phone, $email) {
                $q->where('mobile_number', $phone);
            })
            ->when($id > 0, function($q) use($id) {
                $q->where('id', '!=', $id);
            })
            ->first();
        if($checkExist):
            $error_msg = __('Email address is already taken.');
            return GlobalFunction::sendSimpleResponse(false, $error_msg);
        endif;
        // END check for email unique

        if(!GlobalFunction::validatePhoneNumber($phone)):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
        endif;

        if($request->password && $request->password != $request->confirm_password):
            return GlobalFunction::sendSimpleResponse(false, __('Password and confirm password does not match.'));
        endif;

        if($id):
            $msg = __('Updated!');
            $doctor = Doctors::find($id);
        else:
            $msg = __('Added!');
            $doctor = new Doctors();
        endif;

        $doctor->email_address = $email;
        $doctor->first_name = $request->first_name;
        $doctor->last_name = $request->last_name;
        $doctor->mobile_number = $phone;
        $doctor->outlets = $request->outlet_id ? json_encode($request->outlet_id) : null;
        $doctor->additional_charges = $request->additional_charges ?? 0;
        $doctor->is_active = $request->status ?? 0;

        if($request->has('image')):
            $doctor->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        if(!$id):
            $password = $request->password;
            $doctor->password = Hash::make($password);
            $doctor->device_token = null;
            $doctor->login_type = 'email';
            $doctor->login_times = 0;

            // email user for password
            $email_data = [
                'type' => 'staff',
                'email_type' => 'account_created',
                'name' => $request->first_name.' '.$request->last_name,
                'email' => $email,
                'password' => $password,
            ];
            $this->sendEmail($email_data);
            // END email user for password
        else:
            if($request->password):
                $doctor->password = Hash::make($request->password);
            endif;
        endif;

        $doctor->save();
        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function doctorsDelete(Request $request)
    {
        if($request->id):
            $query = Doctors::find($request->id);
        else:
            $query = Doctors::whereIn('id', $request->selected);
        endif;

        $query = $query->update(['deleted_at' => Carbon::now()]);

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function doctorsStatusUpdate(Request $request)
    {
        $item = Doctors::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function doctorsExport(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'id';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'DESC';
        $result = Doctors::whereNull('deleted_at')
            ->when($request->search, function($query) use ($request) {
                $search = $request->search;
                $query->where(function($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('email_address', 'LIKE', "%{$search}%")
                        ->orWhere('mobile_number', 'LIKE', "%{$search}%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->outlet, function($query) use ($request) {
                $query->where('outlets', 'LIKE', '%"'.$request->outlet.'"%');
            })
            ->when($request->selected, function($query) use ($request) {
                $query->whereIn('id', $request->selected);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                if($item->outlets):
                    $outlets = Outlets::whereIn('id', json_decode($item->outlets, true))
                        ->pluck('name')
                        ->toArray();
                    $item->outlets = implode('<br>', $outlets);
                endif;
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'staffs_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'staffs'),
            $fileName
        );
    }

    function doctorsDropdownList($first='')
    {
        $result = Doctors::whereNull('deleted_at')
            ->when(request()->search, function($query) {
                $search = request()->search;
                $query->where(function($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('mobile_number', 'LIKE', "%{$search}%")
                        ->orWhere('email_address', 'LIKE', "%{$search}%");
                });
            })
            ->orderBy('first_name', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->first_name.' '.$item->last_name.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function doctorsDetail($doctorId)
    {
        if(!checkPermission('edit_doctor')):
            return redirect(route('index'))->with('error', __('You do not have permission to view this staff.'));
        endif;
        $doctor = Doctors::find($doctorId);
        if($doctor->outlets):
            $outlet = json_decode($doctor->outlets, true);
            $doctor->outlets = $outlet;

            $outlet = Outlets::whereIn('id', $outlet)
                ->pluck('name')
                ->toArray();
            $doctor->outlets_display = implode(',', $outlet);
        endif;

        $outlets = Outlets::where('is_active', 1)
            ->orderBy('name', 'ASC')
            ->get();

        // tab list
        $tab_list = [];
        $tab_list['slots'] = [
            'text' => __('Booking Slots'),
            'divID' => '#slotsTab',
            'active' => true,
        ];

        $tab_list['holidays'] = [
            'text' => __('Holidays'),
            'divID' => '#holidaysTab',
            'active' => false,
        ];
        // END tab list

        // bulk action
        $bulk_action_holidays = [
            'delete' => [
                'text' => __('Delete'),
                'url' => route('holidaysDelete')
            ],
        ];

        $moduleID = 'doctors';
        $moduleSlotID = 'slots';
        $moduleHolidayID = 'holidays';
        return view('doctor-detail', [
            'doctorId' => $doctorId,
            'moduleID' => $moduleID,
            'moduleSlotID' => $moduleSlotID,
            'moduleHolidayID' => $moduleHolidayID,
            'outlets' => $outlets,
            'doctor' => $doctor,
            'tab_list' => $tab_list,
            'slot_data' => [
                'day' => dayList(),
                'time_range' => timeRange(),
                'unit' => durationUnit(),
            ],
            'holidays' => [
                'bulk_action' => $bulk_action_holidays,
            ],
        ]);
    }

    // appointment slots
    function appointmentSlotsAdd(Request $request)
    {
        $doctor_id = $request->doctor_id;
        $day = $request->day;
        $start_time = $request->start_time;
        $end_time = $request->end_time;
        $duration = $request->duration;
        $duration_unit = $request->duration_unit;
        $interval = $request->interval ?? 0;
        $interval_unit = $request->interval_unit ?? "minutes";

        // validation
        $errors = [];
        if(!$day):
            $errors[] = "Please select at least a day.";
        endif;

        if($start_time == "midnight"): $start_time == "23:59"; endif;
        if($end_time == "midnight"): $end_time == "23:59"; endif;
        if($end_time <= $start_time):
            $errors[] = "End time must be later than start time.";
        endif;

        if(!$duration || $duration <= 0):
            $errors[] = "Please key in the duration.";
        endif;

        if($errors):
            array_unshift($errors, __('Slot not able to add, following is the errors:'));
            return GlobalFunction::sendSimpleResponse(false, $errors); 
        endif;
        // END validation

        $start_time = strtotime($start_time);
        if($end_time == "midnight"):
            $end_time = strtotime("23:59")+60;
        else:
            $end_time = strtotime($end_time);
        endif;
        $duration_calc = $duration * 60;
        if($duration_unit == "hours"):
            $duration_calc = $duration_calc * 60;
        endif;

        if($interval > 0):
            $interval_calc = $interval * 60;
            if($interval_unit == "hours"):
                $interval_calc = $interval_calc * 60;
            endif;
        endif;

        // calculate time range
        $time_range = [];
        for($i = $start_time; $i <= $end_time; $i += $duration_calc):
            $end_calc = $i + $duration_calc;
            if($end_calc <= $end_time):
                $time_range[] = date("H:i", $i)." - ".date("H:i", $end_calc);

                if($interval > 0):
                    $i += $interval_calc;
                endif;
            endif;
        endfor;
        // END calculate time range

        $slot_day = dayList();
        $errors = [];
        $created = 0;
        foreach($day as $day_id):
            foreach($time_range as $time):
                $time_exp = explode(" - ", $time);
                $time_start = current($time_exp);
                $time_end = end($time_exp);

                $check = DoctorAppointmentSlots::where('doctor_id', $doctor_id)
                    ->where('weekday', $day_id)
                    ->where(function($q) use($time_start, $time_end)
                    {
                        $q->where(function($q) use($time_start, $time_end)
                            {
                                $q->where('time_start', '<', $time_start)
                                    ->where('time_end', '>', $time_start);
                            })
                            ->orWhere(function($q) use($time_start, $time_end)
                            {
                                $q->where('time_start', '<', $time_end)
                                    ->where('time_end', '>', $time_end);
                            })
                            ->orWhere(function($q) use($time_start, $time_end)
                            {
                                $q->where('time_start', $time_start)
                                    ->where('time_end', $time_end);
                            });
                    })
                    ->first();

                if(!$check):
                    DoctorAppointmentSlots::create([
                        'doctor_id' => $doctor_id,
                        'time_start' => $time_start,
                        'time_end' => $time_end,
                        'weekday' => $day_id,
                    ]);
                    $created++;
                else:
                    $errors[] = $slot_day[$day_id].": ".date("g:i a", strtotime($time_start))." - ".date("g:i a", strtotime($time_end));
                endif;
            endforeach;
        endforeach;

        if($errors || $created == 0):
            if($errors):
                $msg = [];
                if($created > 0):
                    $msg[] = __('Slots is added except following slot is not able to add because the time is exist:');
                else:
                    $msg[] = __('Following slot is not able to add because the time is exist:');
                endif;
            else:
                $msg[] = __('No slot is added.');
            endif;
            $errors = array_merge($msg, $errors);

            return GlobalFunction::sendSimpleResponse(false, $errors); 
        else:
            return GlobalFunction::sendSimpleResponse(true, __('Slots is added.')); 
        endif;
    }

    function appointmentSlotsView(Request $request)
    {
        $doctor_id = $request->doctor_id;
        $day = $request->day;

        $slot = DoctorAppointmentSlots::where('doctor_id', $doctor_id)
            ->where('weekday', $day)
            ->orderby('time_start')
            ->get();

        $title = dayList()[$day];
        $html = addslashes(view('appointmentsSlot', compact('slot', 'title', 'day', 'doctor_id')));

        return GlobalFunction::sendSimpleResponse(true, stripslashes($html)); 
    }

    function appointmentSlotsDelete(Request $request)
    {
        if($request->slot_id):
            $slot = DoctorAppointmentSlots::find($request->slot_id);
            if(!$slot):
                return GlobalFunction::sendSimpleResponse(false, 'Slot does not exists.');
            endif;
            $slot->delete();
        else:
            DoctorAppointmentSlots::where('doctor_id', $request->doctor_id)
                ->where('weekday', $request->day)
                ->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, __('The slots is deleted.')); 
    }
    // END appointment slots

    // holidays
    function holidaysAdd(Request $request)
    {
        $doctor_id = $request->doctor_id;
        $start_date = strtotime($request->start_date);
        $end_date = strtotime($request->end_date);
        $start_time = $request->start_time ?? null;
        $end_time = $request->end_time ?? null;

        // validation
        $errors = [];
        if($end_date < $start_date):
            $errors[] = "End date must be later than start date.";
        endif;

        if($start_time || $end_time):
            if($start_time == "midnight"): $start_time == "23:59"; endif;
            if($end_time == "midnight"): $end_time == "23:59"; endif;
            if($end_time <= $start_time):
                $errors[] = "End time must be later than start time.";
            endif;
        endif;

        if($errors):
            array_unshift($errors, __('Holiday not able to add, following is the errors:'));
            return GlobalFunction::sendSimpleResponse(false, $errors); 
        endif;
        // END validation

        $errors = [];
        $created = 0;
        $date_range = range($start_date, $end_date, 24*60*60);
        foreach($date_range as $item):
            $date = date("Y-m-d", $item);

            $check = DoctorHolidays::where('doctor_id', $doctor_id)
                ->where('date', $date);
            
            if($start_time):
                $check = $check->where(function($q) use($start_time, $end_time)
                {
                    $q->where(function($q) use($start_time, $end_time)
                        {
                            $q->where('time_start', '<', $start_time)
                                ->where('time_end', '>', $start_time);
                        })
                        ->orWhere(function($q) use($start_time, $end_time)
                        {
                            $q->where('time_start', '<', $end_time)
                                ->where('time_end', '>', $end_time);
                        })
                        ->orWhere(function($q) use($start_time, $end_time)
                        {
                            $q->where('time_start', $start_time)
                                ->where('time_end', $end_time);
                        })
                        ->orWhere(function($q) use($start_time, $end_time)
                        {
                            $q->whereNull('time_start')
                                ->whereNull('time_end');
                        });
                });
            endif;

            $check = $check->first();

            if(!$check):
                DoctorHolidays::create([
                    'doctor_id' => $doctor_id,
                    'date' => $date,
                    'time_start' => $start_time,
                    'time_end' => $end_time,
                ]);
                $created++;
            else:
                $time_text = "";
                if($start_time):
                    $time_text = ": ".date("g:i a", strtotime($start_time))." - ".date("g:i a", strtotime($end_time));
                endif;

                $errors[] = date("d-m-Y", strtotime($date)).$time_text;
            endif;
        endforeach;

        if($errors || $created == 0):
            if($errors):
                $msg = [];
                if($created > 0):
                    $msg[] = __('Holidays is added except following date/time is not able to add because is exist:');
                else:
                    $msg[] = __('Following date/time is not able to add because is exist:');
                endif;
            else:
                $msg[] = __('No holiday is added.');
            endif;
            $errors = array_merge($msg, $errors);

            return GlobalFunction::sendSimpleResponse(false, $errors); 
        else:
            return GlobalFunction::sendSimpleResponse(true, __('Holidays is added.')); 
        endif;
    }

    function holidaysView(Request $request)
    {
        $result = DoctorHolidays::where('doctor_id', $request->doctor_id)
            ->when($request->date, function($query) use ($request) {
                $date_exp = explode(" to ", $request->date);
                $query->where('date', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                    ->where('date', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
            })
            ->orderBy('date', 'ASC')
            ->orderBy('time_start', 'ASC');
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="holidaysList">
                    <span></span>
                </label>';

                if($item->time_start):
                    $time = date('g:i a', strtotime($item->time_start))." - ".date('g:i a', strtotime($item->time_end));
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">
                    <a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>
                </div>';

                $data[] = [
                    $checkbox,
                    date('d-m-Y', strtotime($item->date)),
                    $time ?? __('Whole Day'),
                    $action,
                ];
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function holidaysDelete(Request $request)
    {
        if($request->id || $request->holiday_id):
            $id = $request->id ?? $request->holiday_id;
            $holiday = DoctorHolidays::find($id)->delete();
        else:
            DoctorHolidays::whereIn('id', $request->selected)->delete();
        endif;

        return Globalfunction::sendSimpleResponse(true, 'Deleted!');
    }

    function holidaysExport(Request $request)
    {
        $result = DoctorHolidays::where('doctor_id', $request->doctor_id)
            ->when($request->date, function($query) use ($request) {
                $date_exp = explode(" to ", $request->date);
                $query->where('date', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                    ->where('date', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
            })
            ->orderBy('date', 'ASC')
            ->orderBy('time_start', 'ASC')
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->date)->format('d-m-Y');
                $item->time = __('Whole Day');
                if($item->time_start):
                    $item->time = date('g:i a', strtotime($item->time_start))." - ".date('g:i a', strtotime($item->time_end));
                endif;
            endforeach;
        endif;
        
        $fileName = 'doctor_holidays_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, "doctor_holidays"),
            $fileName
        );
    }
    // END holidays
}