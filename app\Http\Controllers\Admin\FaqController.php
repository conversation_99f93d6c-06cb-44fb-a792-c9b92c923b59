<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Faqs;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class FaqController extends Controller
{
    function index()
    {
        if(!checkPermission('view_faq')):
            return redirect(route('index'))->with('error', __('You do not have permission to view faq.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('faqsDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('faqsExport'),
                'filename' => 'faqs'
            ]
        ];

        if(!checkPermission('delete_faq')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_faq')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'faqs';
        return view('faqs', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Faqs::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('question', 'LIKE', "%{$search}%")
                    ->orWhere('answer', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="faqsList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_faq')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_faq')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Edit').'" data-name="' . $item->question . '" data-answer="' . $item->answer . '" data-status="'.$item->is_active.'"><i class="fa fa-edit"></i></a>';
                endif;

                if(checkPermission('delete_faq')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    // config('staticdata.brand')[$item->brand] ?? $item->brand,
                    $item->question,
                    Str::limit($item->answer, 50),
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_faq') && !checkPermission('delete_faq')):
                    unset($param[4]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = Faqs::find($id);
        else:
            $msg = __('Added!');
            $query = new Faqs();
        endif;

        // $query->brand = $request->brand;
        $query->question = $request->question;
        $query->answer = $request->answer;
        $query->is_active = $request->status ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            Faqs::find($request->id)->delete();
        else:
            Faqs::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Faqs::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'DESC';
        $result = Faqs::when($request->search, function($q) use ($request) {
            $search = $request->search;
            $q->where('question', 'LIKE', "%{$search}%")
                    ->orWhere('answer', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('is_active', $request->brand);
            // })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'faqs_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'faqs'),
            $fileName
        );
    }
}