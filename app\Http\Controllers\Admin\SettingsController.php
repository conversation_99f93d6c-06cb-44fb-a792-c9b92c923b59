<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;
use App\Models\Constants;
use App\Models\Doctors;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Models\RedemptionVouchers;
use App\Models\Retailer;

class SettingsController extends Controller
{
    function settings(Request $request)
    {
        $tab = $request->tab ?? '';

        // permissions
        $tab_permissions = [];
        $tab_list = [];
        $active_tab = false;
        if(checkPermission('general_setting')):
            $tab_permissions[] = 'general';
            if(!$tab || $tab == 'general'): 
                $active_tab = true;
                $tab = 'general';
            endif;

            $tab_list['general'] = [
                'text' => __('General'),
                'divID' => '#generalTab',
                'active' => $active_tab,
            ];
        endif;
        if(checkPermission('point_setting')):
            $tab_permissions[] = 'point';
            if(!$tab || $tab == 'point'): 
                $active_point = true;
                $tab = 'point';
            endif;
            
            $tab_list['point'] = [
                'text' => __('Membership Config'),
                'divID' => '#pointTab',
                'active' => $active_point ?? false,
            ];
        endif;
        if(checkPermission('3rd_party_setting')):
            $tab_permissions[] = 'key';
            if(!$tab || $tab == 'third_party'): 
                $active_key = true;
                $tab = 'third_party';
            endif;

            $tab_list['key'] = [
                'text' => __('3rd Party'),
                'divID' => '#keyTab',
                'active' => $active_key ?? false,
            ];
        endif;
        // if(checkPermission('reminder_setting')):
        //     $tab_permissions[] = 'reminder';
        //     if(!$tab || $tab == 'reminder'): 
        //         $active_reminder = true;
        //         $tab = 'reminder';
        //     endif;

        //     $tab_list['reminder'] = [
        //         'text' => __('Reminder'),
        //         'divID' => '#reminderTab',
        //         'active' => $active_reminder ?? false,
        //     ];
        // endif;
        /*if(checkPermission('mobile_version_setting')):
            $tab_permissions[] = 'mobile';
            if(!$tab || $tab == 'mobile'): 
                $active_mobile = true;
                $tab = 'mobile';
            endif;

            $tab_list['mobile'] = [
                'text' => __('Mobile Version'),
                'divID' => '#mobileTab',
                'active' => $active_mobile ?? false,
            ];
        endif;*/
        // END permissions

        if(!$tab_permissions):
            return redirect(route('index'))->with('error', __('You do not have permission to access this page.'));
        endif;
    
        $result = GlobalSettings::all();
        $settings = [];
        foreach($result as $item):
            $settings[$item->name] = $item->value;
        endforeach;
        $settings = (object) $settings;

        if($settings->admin_logo):
            $settings->admin_logo = GlobalFunction::createMediaUrl($settings->admin_logo);
        endif;

        $vouchers = RedemptionVouchers::where('is_active', true)->where('is_birthday', false)->get();
        $b_vouchers = RedemptionVouchers::where('is_active', true)->where('is_birthday', true)->get();

        $global_function = GlobalFunction::class;
        return view('settings', [
            'data' => $settings,
            'global_function' => $global_function,
            'tab_permissions' => $tab_permissions,
            'tab_list' => $tab_list,
            'vouchers' => $vouchers,
            'b_vouchers' => $b_vouchers
        ]);
    }

    function settingsUpdate(Request $request)
    {
        $tab = $request->tab ?? 'general';
        if($tab == 'general'):
            $update_data = [
                'site_name',
                'admin_logo',
                // 'otp_expired_duration',
                'currency',
                'currency_sg',
                'support_email',
                'support_contact',
                'notification_email',
                // 'min_hour_before_booking',
                // 'booking_reminder_duration',
                'receive_order_contact',
                'tax'
            ];
        elseif($tab == 'point'):
            $update_data = [
                'bookings_points_rm_ratio',
                'bookings_points_rm_expired_enable',
                'bookings_points_rm_expired_duration',
                'bookings_points_expired_reminder',
                // 'bookings_points_rm_redeem_ratio',
                'bookings_points_sg_ratio',
                'bookings_points_sg_expired_enable',
                'bookings_points_sg_expired_duration',
                // 'bookings_points_sg_redeem_ratio',
                'points_referral_type',
                'points_referral_value',
                'memberday_every_month',
                'memberday_multiplier',
                'memberday_vouchers',
                'birthday_voucher_validity',
                'birthday_vouchers'
            ];
        elseif($tab == 'third_party'):
            $update_data = [
                'google_map_api_key',
                'lalamove_tracking_link',
                'onesignal_api_id',
                'onesignal_api_key',
                'staff_onesignal_api_id',
                'staff_onesignal_api_key',
                'smtp_host',
                'smtp_port',
                'smtp_encryption',
                'smtp_username',
                'smtp_password',
                'smtp_from_name',
                'smtp_from_email',
            ];
        elseif($tab == 'mobile'):
            $update_data = [
                'android_version_no',
                'ios_version_no',
                'staff_android_version_no',
                'staff_ios_version_no',
            ];
        elseif($tab == 'reminder'):
            $update_data = [
                'cart_reminder',
                'feedback_reminder',
                'voucher_expired_reminder'
            ];
        endif;

        $image_var = ['admin_logo'];
        if(isset($update_data)):
            foreach($update_data as $var):
                $update_data = '';
                if(isset($request[$var]) && in_array($var, $image_var)):
                    $image = GlobalSettings::where('name', $var)->first();
                    if($image):
                        GlobalFunction::deleteFile($image->value);
                    endif;

                    $update_data = GlobalFunction::saveFileAndGivePath($request[$var]);
                elseif(!in_array($var, $image_var)):
                    if(is_array($request[$var])):
                        $update_data = json_encode($request[$var]);
                    else:
                        if(in_array($var, ['memberday_vouchers'])){
                            $update_data = $request[$var] ?? [];
                        }else{
                            $update_data = $request[$var] ?? 0;
                        }
                    endif;
                endif;

                if($update_data || (!in_array($var, $image_var))):
                    GlobalSettings::updateOrCreate(
                        ['name' => $var],
                        ['value' => $update_data]
                    );
                endif;
            endforeach;
        endif;

        return redirect(route('settings').'?tab='.$tab)->with('message', "Updated!");
    }

    function paymentList(Request $request)
    {
        $result = GlobalSettings::all();

        $settings = [];
        foreach($result as $item):
            $settings[$item->name] = $item->value;
        endforeach;
        $settings = (object) $settings;

        return view('payment-list', compact('settings'));
    }

    function paymentUpdate(Request $request)
    {
        $update_data = [
            'payment_gateway',
            'billplz_sandbox',
            'billplz_collection',
            'billplz_secret',
            'billplz_signature',
            // 'stripe_secret',
            // 'stripe_publishable_key',
            // 'stripe_currency_code',
            // 'razer_sandbox',
            // 'razer_id',
            // 'razer_key',
            // 'razer_secret',
            // 'razer_currency',
            // 'razer_max_amount',
        ];

        foreach($update_data as $var):
            $update_data = $request[$var] ?? 0;
            GlobalSettings::where('name', $var)->update(['value' => $update_data]);
        endforeach;

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function changePassword(Request $request)
    {
        $admin = session()->get('user');
        $user_type = $admin->user_type;

        return view('change-password', compact('admin','user_type'));
    }

    function changePasswordUpdate(Request $request)
    {
        $user_id = session()->get('user')->user_id;

        if(session()->get('portal') == 'retailer'):
            $query = Retailer::where('user_id', $user_id)->first();
        else:
            $query = Admin::where('user_id', $user_id)->first();
        endif;

        $password = $query->user_password;

        if(Hash::check($request->old_password, $password)):
            $new_password = Hash::make($request->new_password);
            $query->user_password = $new_password;
            $query->save();

            return GlobalFunction::sendSimpleResponse(true, 'Password changed!');
        else:
            return GlobalFunction::sendSimpleResponse(false, 'Incorrect old password!');
        endif;
    }

    function changePersonalInfo(Request $request)
    {
        $user_id = session()->get('user')->user_id;

        if(session()->get('portal') == 'retailer'):
            $query = Retailer::where('user_id', $user_id)->first();
        else:
            $query = Admin::where('user_id', $user_id)->first();
        endif;
        $query->update($request->all());
        session()->put('user', $query);

        return GlobalFunction::sendDataResponse(true, 'Changed!', $query);
    }
}