<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\UserPointTransaction;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\ExpiryReminderNotification;
use App\Traits\PushNotificationTraits;

class PointExpiryReminders extends Command
{
    use PushNotificationTraits;

    protected $signature = 'point_expiry_reminder';
    protected $description = 'send notification reminder before point expired';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $point_expired_reminder = global_settings('bookings_points_expired_reminder');

        $transactions = UserPointTransaction::whereNotNull('expired_date')
            ->whereDate('expired_date', '=', Carbon::now()->addDays($point_expired_reminder))
            ->where('status', 'ready')
            ->where('is_expired', 0)
            ->where('points_prefix', '+')
            ->get();

        $reminder_recipient = [];
        foreach($transactions as $transaction):
            $expired_date = Carbon::parse($transaction->expired_date)->format('d M Y');
            $user = $transaction->user;

            Notification::route('whatsapp', $user->phone_number)
                ->notify(new ExpiryReminderNotification($transaction->user, $transaction->point_expiry_balance,'points',  $expired_date));

            $reminder_recipient[] = $user->id.": ".$user->phone_number .' - '.$transaction->id;
        endforeach;

        if($reminder_recipient):
            Cronjob::create([
                "type" => "point expiry reminder",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}