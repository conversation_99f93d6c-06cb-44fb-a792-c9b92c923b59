<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\Doctors;
use App\Models\Roles;
use App\Models\RolePermissions;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class RolesAndPrivilegesController extends Controller
{
    function index()
    {
        if (!checkPermission('view_role')):
            return redirect(route('index'))->with('error', __('You do not have permission to view roles and privileges.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('rolesPrivilegesDelete')
            ],
        ];
        if (!checkPermission('delete_role')):
            unset($bulk_action['delete']);
        endif;

        $permissions = RolePermissions::when(session()->get('user')->role_id > 1, function ($q) {
            $q->where('is_show', 1);
        })
            ->get()
            ->sortBy('group_code')
            ->groupBy('group_code')
            ->map(function ($permission, $group_code) {
                $group_sequence = match (true) {
                    str_contains($group_code, 'report') => 2,
                    str_contains($group_code, 'ecommerce') => 3,
                    str_contains($group_code, 'setup') => 4,
                    str_contains($group_code, 'pages') => 5,
                    default => 1,
                };
                return ['permissions' => $permission, 'sequence' => $group_sequence];
            })
            ->sortBy('sequence')
            ->map(fn($group) => $group['permissions']->pluck('permission_code', 'id'))
            ->toArray();

        $isTopAdmin = session()->get('user')->role_id == 1;

        $moduleID = 'roles';
        return view('roles-privileges', compact('bulk_action', 'permissions', 'moduleID', 'isTopAdmin'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'disable_delete';
        $sort_by = $request->sort_by ?? 'ASC';
        $result = Roles::where('id', '!=', 1)
            ->when($request->input('search.value'), function ($q) use ($request) {
                $q->where('name', 'LIKE', "%" . $request->input('search.value') . "%");
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="rolesList"' . ($item->disable_delete == '1' ? 'disabled' : '') . '>
                    <span></span>
                </label>';

                if ($item->id == 2):
                    $total_users = $item->retailer()->whereNull('deleted_at')->count();
                    $user_link = route('retailers');
                else:
                    $total_users = $item->admin()->whereNull('deleted_at')->count();
                    $user_link = route('admins') . '?role=' . $item->id;
                endif;
                if ($total_users > 0):
                    $total_users = '<a href="' . $user_link . '">' . $total_users . '</a>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_role')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" data-url="' . route("rolesPrivilegesDetail", $item->id) . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;

                $action .= '<a href="" class="btn-icon btn-warning btn-modal-detail mr-1" data-url="' . route("rolesPrivilegesView", $item->id) . '" data-toggle="tooltip" data-title="' . __('View Privileges') . '"><i class="fa fa-user-shield"></i></a>';

                if ($item->disable_delete == 0 && checkPermission('delete_role')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;

                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    '<div class="text-center">' . $total_users . '</div>',
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = Roles::find($id);
        else:
            $msg = __('Added!');
            $query = new Roles();
        endif;

        $query->name = $request->name;
        if (isset($request->permission_ids)):
            $query->permissions = $request->permission_ids ? json_encode($request->permission_ids) : null;
        endif;
        $query->save();

        if ($request->admin_ids):
            Admin::whereIn('user_id', $request->admin_ids)->update(['role_id' => $query->id]);
        endif;

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if ($request->id):
            $query = Roles::find($request->id);
            $admin = Admin::where('role_id', $request->id);
        else:
            $query = Roles::whereIn('id', $request->selected);
            $admin = Admin::whereIn('role_id', $request->selected);
        endif;

        $admin->update(['role_id' => 0]);
        $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function detail($id, Request $request)
    {
        $data = Roles::find($id);

        $admins = $data->admin()->whereNull('deleted_at')->get();
        if ($admins):
            $selected_admin = '';
            foreach ($admins as $item):
                $selected_admin .= '<option value="' . $item->user_id . '" selected>' . $item->first_name . ' ' . $item->last_name . ' (' . $item->user_name . ')</option>';
            endforeach;
        endif;
        $data->selected_admin = $selected_admin ?? '';

        // permissions
        $selected_permissions = $data->permissions;
        if ($selected_permissions && $selected_permissions != 'all'):
            $selected_permissions = json_decode($selected_permissions);
        endif;
        $data->selected_permissions = $selected_permissions;
        // END permissions

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function viewPrivileges($id)
    {
        $query = Roles::find($id);
        $selected_permissions = $query->permissions;

        $html = '';
        if ($selected_permissions == 'all'):
            $html = '<div class="selected-data">' . __('All Privileges') . '</div>';
        elseif ($selected_permissions && $selected_permissions != 'all'):
            $selected_permissions = json_decode($selected_permissions);

            $permissions = RolePermissions::whereIn('id', $selected_permissions)
                ->get()
                ->sortBy('group_code')
                ->groupBy('group_code')
                ->map(function ($permission, $group_code) {
                    $group_sequence = match (true) {
                        str_contains($group_code, 'report') => 2,
                        str_contains($group_code, 'ecommerce') => 3,
                        str_contains($group_code, 'setup') => 4,
                        str_contains($group_code, 'pages') => 5,
                        default => 1,
                    };
                    return ['permissions' => $permission, 'sequence' => $group_sequence];
                })
                ->sortBy('sequence')
                ->map(fn($group) => $group['permissions']->pluck('permission_code', 'id'))
                ->toArray();

            foreach ($permissions as $group_code => $permission):
                list($group_module, $group_tag) = explode('_', $group_code, 2);

                $html .= '<div class="permissions-box">
                        <label class="mb-2 text-dark-gray">' . __(ucwords($group_module)) . ': ' . __(ucwords(str_replace('_', ' ', $group_tag))) . '</label>
                        <div>';

                foreach ($permission as $permission_id => $permission_code):
                    $permission_module = str_replace('_' . $group_tag, '', $permission_code);
                    $permission_module = str_replace('_', ' ', $permission_module);

                    $html .= '<div class="selected-data">' . __(ucwords($permission_module)) . '</div>';
                endforeach;

                $html .= '</div>
                    </div>';
            endforeach;
        endif;

        $data = [
            'title' => 'Privileges',
            'data' => $html
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'name';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = Roles::where('id', '!=', 1)
            ->when($request->search, function ($q) use ($request) {
                $q->where('name', 'LIKE', "%" . $request->search . "%");
            })
            ->when($request->selected, function ($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $admins = '';
                $item->admins = $admins;

                $permissions_list = '';
                if ($item->permissions):
                    if ($item->permissions != 'all'):
                        $permissions = json_decode($item->permissions);
                        foreach ($permissions as $permission_id):
                            $permissionData = RolePermissions::find($permission_id);
                            $group_name = $permissionData->group_name;
                            $group_name = str_replace('&', 'and', $group_name);
                            $permissions_list .= $group_name . ' : ' . $permissionData->permission_name . '<br>';
                        endforeach;
                    else:
                        $permissions_list = 'all';
                    endif;
                endif;

                $item->permissions_list = $permissions_list;
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'roles_and_priviledges_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'roles_and_priviledges'),
            $fileName
        );
    }

    function dropdownList($first = '')
    {
        $result = Roles::where('id', '!=', '1')
            ->orderBy('name', 'ASC')
            ->get();

        $html = "";
        if ($result->count() > 0):
            if ($first):
                $html .= '<option value="">' . $first . '</option>';
            endif;
            foreach ($result as $item):
                $html .= '<option value="' . $item->id . '">' . $item->name . '</option>';
            endforeach;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function featureEnabler()
    {
        $title = 'Feature Enabler';

        $permissions = RolePermissions::when(session()->get('user')->role_id > 1, function ($q) {
            $q->where('is_show', 1);
        })
            ->get()
            ->sortBy('group_code')
            ->groupBy('group_code')
            ->map(function ($permission, $group_code) {
                $group_sequence = match (true) {
                    str_contains($group_code, 'report') => 2,
                    str_contains($group_code, 'ecommerce') => 3,
                    str_contains($group_code, 'setup') => 4,
                    str_contains($group_code, 'pages') => 5,
                    default => 1,
                };

                $show = $permission->mapWithKeys(fn($item) => [$item->permission_code => $item->is_show]);

                $all = $show->every(function($value) {
                    return $value === 1;
                }) ?: false;

                return ['show' => $show, 'sequence' => $group_sequence, 'all' => $all];
            })
            ->sortBy('sequence')
            ->toArray();

        $data = [
            'title' => $title,
            'permissions' => $permissions
        ];

        return view('feature-enabler-form', $data);
    }

    function featureEnablerUpdate(Request $request)
    {
        if (isset($request->permission_codes)) {
            RolePermissions::whereIn('permission_code', $request->permission_codes)
                ->update(['is_show' => true]);

            // Update the RolePermissions where permission_code is NOT in the request
            RolePermissions::whereNotIn('permission_code', $request->permission_codes)
                ->update(['is_show' => false]);
        }

        return redirect(route('rolesPrivilegesFeatureEnabler'))->with('message', 'Updated!');
    }
}
