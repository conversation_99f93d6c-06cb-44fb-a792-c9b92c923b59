<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\UserVouchers;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\RedemptionVouchers;
use App\Http\Controllers\Controller;
use App\Traits\VoucherTraits;
use Maatwebsite\Excel\Facades\Excel;

class RedeemedVoucherController extends Controller
{
    use VoucherTraits;

    function index()
    {
        $filter_user = (new UsersController)->usersDropdownList('All Members');
        $filter_user = $filter_user->getData()->data;

        $filter_status = [
            'Valid' => __('Valid'),
            'Used' => __('Used'),
            'Expired' => __('Expired')
        ];

        $moduleID = 'redeemed-voucher';

        $users = User::where('is_active', '1')->whereNull('deleted_at')->get();

        return view('redeemed-vouchers', compact('moduleID', 'filter_user', 'filter_status', 'users'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';

        $result = UserVouchers::with(['user', 'voucher'])
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->when($request->input('search.value'), function ($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('voucher_code', 'LIKE', "%{$search}%")->orWhere(function ($q) use ($search) {
                    $q->whereHas('voucher', function ($q) use ($search) {
                        $q->where('name', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when($request->status, function ($q) use ($request) {
                $q->when($request->status == 'Valid', function ($q) {
                    $q->where('effective_end_date', '>', Carbon::now())->whereNull('used_date');
                })->when($request->status == 'Used', function ($q) {
                    $q->whereNotNull('used_date');
                })->when($request->status == 'Expired', function ($q) {
                    $q->where('effective_end_date', '<', Carbon::now())->whereNull('used_date');
                });
            })
            ->when($request->date, function ($q) use ($request) {
                $date = explode(" to ", $request->date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $count => $item):

                $user = "";
                if (!$request->userPage):
                    $user = "<div>" . $item->user->name . '<div class="text-grey">' . $item->user->email_address . "</div></div>";
                endif;


                $date = "<div class='text-center'>" . Carbon::parse($item->created_at)->format('d-m-Y') . '<div class="text-grey">' . Carbon::parse($item->created_at)->format('g:i a') . "</div></div>";

                $action = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->used_date ? ' checked' : '') . '>
                            <span class="slider round"></span>
                        </label>
                    </div>';

                if ($item->used_date) {
                    $status = "<div class='badge bg-success text-white'>" . __('Used') . "</div>";
                } else if ($item->effective_end_date > Carbon::now() && $item->used_date == null) {
                    $status = "<div class='badge bg-info text-white'>" . __('Valid') . "</div>";
                } else {
                    $status = "<div class='badge bg-danger text-white'>" . __('Expired') . "</div>";
                }

                $delete = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('delete_redemption_voucher')):
                    $delete .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $delete .= '</div>';

                $param = [
                    $date,
                    $item->voucher->name,
                    $user,
                    Carbon::parse($item->effective_start_date)->format('d-m-Y') . ' to ' . Carbon::parse($item->effective_end_date)->format('d-m-Y'),
                    $status,
                    $action,
                    $delete
                ];

                if ($request->userPage):
                    unset($param[0]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data,
        ];

        echo json_encode($json_data);
        exit();
    }

    function usedUpdate(Request $request)
    {
        $item = UserVouchers::find($request->id);

        $item->used_date = $request->value ? Carbon::now() : null;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = UserVouchers::with(['user', 'voucher'])
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->when($request->input('search.value'), function ($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('voucher_code', 'LIKE', "%{$search}%")->orWhere(function ($q) use ($search) {
                    $q->whereHas('voucher', function ($q) use ($search) {
                        $q->where('name', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when($request->status, function ($q) use ($request) {
                $q->when($request->status == 'Valid', function ($q) {
                    $q->where('effective_end_date', '>', Carbon::now())->whereNull('used_date');
                })->when($request->status == 'Used', function ($q) {
                    $q->whereNotNull('used_date');
                })->when($request->status == 'Expired', function ($q) {
                    $q->where('effective_end_date', '<', Carbon::now())->whereNull('used_date');
                });
            })
            ->when($request->date, function ($q) use ($request) {
                $date = explode(" to ", $request->date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                if ($item->used_date) {
                    $item->status =  __('Used');
                } else if ($item->effective_end_date > Carbon::now() && $item->used_date == null) {
                    $item->status =  __('Valid');
                } else {
                    $item->status =  __('Expired');
                }
                $item->date = Carbon::parse($item->created_at)->format('d-m-Y g:i a');
                $item->effective_start_date = Carbon::parse($item->effective_start_date)->format('d-m-Y');
                $item->effective_end_date = Carbon::parse($item->effective_end_date)->format('d-m-Y');
            endforeach;
        endif;

        $fileName = 'Member_redeemed_vouchers_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'redeemed_vouchers'),
            $fileName
        );
    }

    function store(Request $request)
    {
        $msg = __('Added!');
        $query = new UserVouchers();

        $voucher = RedemptionVouchers::find($request->voucher_id);

        $voucher_code = $this->userVoucherCode($voucher);

        $query->user_id = $request->user_id;
        $query->voucher_id = $request->voucher_id;
        $query->voucher_code = $voucher_code;
        $query->voucher_value = $voucher->value;
        $query->effective_start_date = $voucher->available_start_date;
        $query->effective_end_date = $voucher->available_end_date;
        $query->voucher_data = json_encode($voucher);
        $query->save();
        $voucher->increment('redeemed_quantity');

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function getVoucherBasedOnTier(Request $request)
    {
        $html = '';
        $html .= '<option value="">Select Voucher</option>';

        $user = User::find($request->user_id);

        if ($user) {
            $vouchers = RedemptionVouchers::where('membership_tiers', 'LIKE', '%' . $user->membership_tier . '%')->get();

            if ($vouchers->count() > 0) {
                foreach ($vouchers as $item) {
                    $html .= '<option value="' . $item->id . '">' . $item->name . '</option>';
                }
            }
        }

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function delete(Request $request)
    {
        $voucherLog = UserVouchers::find($request->id);

        $voucherLog->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }
}
