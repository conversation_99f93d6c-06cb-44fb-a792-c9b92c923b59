<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\Outlets;
use App\Models\User;
use App\Http\Controllers\Api\ApiBookings;
use App\Traits\BookingsTraits;

class WebStringingController extends Controller
{
    use BookingsTraits;

    function form(Request $request)
    {
        $outlet_id = $request->outlet_id;
        $outlet = Outlets::where('id', $outlet_id)->first();

        return view('web.stringing-form', [
            'outlet' => $outlet,
        ]);
    }

    function summary(Request $request)
    {
        // check if email is used
        $user = User::where('email_address', $request->email)
            ->whereNull('deleted_at')
            ->first();
        if($user):
            return redirect()->route('stringing.form', [
                'outlet_id' => $request->outlet_id,
            ])->with('error', 'Email address is already taken.');
        endif;

        $request->merge([
            'tension' => $request->string_tension,
        ]);
        $summary = $this->checkout($request);

        if(isset($summary['error'])):
            return redirect()->route('stringing.form', [
                'outlet_id' => $request->outlet_id,
            ])->with('error', $summary['error']);
        endif;

        $summary = (object) $summary;
        return view('web.stringing-summary', [
            'summary' => $summary,
            'request' => $request,
        ]);
    }

    function create(Request $request)
    {
        $request->merge([
            'web_version' => true,
            'tension' => $request->string_tension,
        ]);
        $booking = $this->createBooking($request);

        if(isset($booking['error'])):
            return redirect()->route('stringing.form', [
                'outlet_id' => $request->outlet_id,
            ])->with('error', $booking['error']);
        endif;

        $data = [
            'booking' => $booking,
            'payment_status' => 'paid',
            'booking_status' => 'received_at_outlet',
        ];
        $this->updateBookingStatus($data);

        return redirect()->route('stringing.created', [
            'ref' => $booking['ref_no'],
        ]);
    }

    function created($ref_no)
    {
        $data = [
            'ref_no' => $ref_no,
            'type' => 'user'
        ];
        $booking = $this->getBookingDetail($data);

        return view('web.stringing-booked', [
            'booking' => $booking,
        ]);
    }
}