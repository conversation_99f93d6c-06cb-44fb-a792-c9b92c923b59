<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Taxes extends Model
{
    use HasFactory;
    public $table = "taxes";

    public function customer_tax()
    {
        return $this->hasMany(ProductPrices::class, 'tax_id', 'id');
    }

    public function retailer_tax()
    {
        return $this->hasMany(Products::class, 'retailer_tax_id', 'id');
    }
}
