<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\UserVouchers;
use Illuminate\Console\Command;
use App\Models\RedemptionVouchers;
use App\Models\CronjobLogs as Cronjob;
use App\Notifications\BirthdayNotification;
use Illuminate\Support\Facades\Notification;

class BirthdayVoucher extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'birthday:voucher';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Give birthday voucher';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $b_voucher_ids = global_settings('birthday_vouchers') ?? [];
        $b_voucher_validity = global_settings('birthday_voucher_validity') ?? 30;

        $vouchers = RedemptionVouchers::whereIn('id', json_decode($b_voucher_ids, true))
            ->where('is_active', 1)
            ->where('is_birthday', 1)
            ->whereNull('deleted_at')
            ->get();

        $reminder_recipient = [];
        foreach ($vouchers as $voucher):
            $users = User::where('is_active', 1)
                ->whereRaw('MONTH(dob) = MONTH(CURDATE()) AND DAY(dob) = DAY(CURDATE())')
                ->whereIn('membership_tier', json_decode($voucher->membership_tiers))
                ->get();

            foreach ($users as $user):
                // check if this voucher have exist in user voucher, 
                // if exist then skip
                $user_voucher = UserVouchers::where('user_id', $user->id)
                    ->where('voucher_id', $voucher->id)
                    ->first();

                if ($user_voucher) {
                    continue;
                }

                //check the voucher limit
                // if ($voucher->max_quantity && $voucher->redeemed_quantity >= $voucher->max_quantity):
                //     return 0;
                // endif;

                $voucher->redeemed_quantity += 1;
                // $voucher->balance_quantity -= 1;
                $voucher->save();

                $user_voucher = new UserVouchers;
                $user_voucher->user_id = $user->id;
                $user_voucher->voucher_id = $voucher->id;
                $user_voucher->voucher_code = $voucher->code;
                $user_voucher->voucher_value = $voucher->value;
                $user_voucher->portal = $voucher->portal;
                $user_voucher->effective_start_date = Carbon::now()->startOfDay();
                $user_voucher->effective_end_date = Carbon::now()->addDays($b_voucher_validity)->endOfDay();
                $user_voucher->voucher_data = json_encode($voucher);
                $user_voucher->save();

                Notification::route('whatsapp', $user->phone_number)
                    ->notify(new BirthdayNotification($user, $voucher->name, 'voucher', date('d/m/Y', strtotime($user_voucher->effective_end_date)), ['whatsapp']));

                $reminder_recipient[] = $user->id . ": " . $user->phone_number . " - " . $user_voucher->id;
            endforeach;
        endforeach;

        if ($reminder_recipient):
            Cronjob::create([
                "type" => "birthday voucher",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}
