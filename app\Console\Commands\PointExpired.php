<?php
namespace App\Console\Commands;

use App\Models\Constants;
use Illuminate\Console\Command;
use App\Models\UserPointTransaction;
use App\Models\CronjobLogs as Cronjob;
use App\Notifications\ExpiredNotification;
use Illuminate\Support\Facades\Notification;

class PointExpired extends Command
{
    protected $signature = 'point_expired:cron';
    protected $description = 'update point to expired if expired date is meet';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $transactions = UserPointTransaction::whereNotNull('expired_date')
            ->where('expired_date', '<', date("Y-m-d H:i:s"))
            ->where('status', 'ready')
            ->where('is_expired', 0)
            ->where('points_prefix', '+')
            ->get();

        $recipient = [];
        if(count($transactions) > 0):
            foreach($transactions as $item):
                $user = $item->user;
                $point_balance = $user->point_balance - $item->points;
                if($point_balance < 0):
                    $point_balance = 0;
                endif;

                $user->point_balance = $point_balance;
                $user->save();
                
                $item->is_expired = 1;
                $item->status = "expired";
                $item->available_points = $point_balance;
                $item->save();

                $points = $item->points . ' points';
                Notification::route('whatsapp', $user->phone_number)
                    ->notify(new ExpiredNotification($user, $points, 'points'));

                $recipient[] = $user->id.": ".$user->phone_number.' - '.$item->id;
            endforeach;
        endif;

        // cronjob log
        if($recipient):
            Cronjob::create([
                "type" => "point expired",
                "recipient" => implode(', ', $recipient),
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}