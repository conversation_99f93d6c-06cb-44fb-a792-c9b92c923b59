<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserWalletTransactions extends Model
{
    use HasFactory;
    public $table = "user_wallet_transactions";

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function payment_transaction()
    {
        return $this->hasOne(PaymentTransactions::class, 'id', 'transaction_id');
    }

    public function booking()
    {
        return $this->belongsTo(Bookings::class, 'transaction_id', 'id');
    }

    public function paymentTransaction()
    {
        return $this->belongsTo(PaymentTransactions::class, 'transaction_id', 'id');
    }
}
