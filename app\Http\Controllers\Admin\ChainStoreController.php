<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\ChainStore;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class ChainStoreController extends Controller
{
    function index()
    {
        if(!checkPermission('view_chain_stores')):
            return redirect(route('index'))->with('error', __('You do not have permission to view chain stores.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('chainStoreDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('chainStoreExport'),
                'filename' => 'chain_stores'
            ]
        ];

        if(!checkPermission('delete_chain_stores')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_chain_stores')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'chain_stores';
        return view('chain-stores', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = ChainStore::when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where('name', 'LIKE', "%{$search}%");
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="chainStoreList">
                    <span></span>
                </label>';

                $image_src = GlobalFunction::createMediaUrl($item->image);
                $image = '<a href="'.$image_src.'" target="_blank" data-toggle="tooltip" data-title="'.__('Enlarge').'">
                        <img src="'.$image_src.'" style="max-width:120px; max-height: 120px;">
                    </a>';

                if(!checkPermission('edit_chain_stores')):
                    $featured = '<div class="text-center">'.($item->is_featured == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $featured = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_featured == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_chain_stores')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_chain_stores')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $image,
                    $item->name,
                    $featured,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_chain_stores')):
                    unset($param[6]);
                endif;
                
                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = ChainStore::find($id);
        else:
            if (!$request->has('image')):
                return GlobalFunction::sendSimpleResponse(false, __('Please select image.'));
            endif;

            $msg = __('Added!');
            $query = new ChainStore();
        endif;

        $query->name = $request->name;
        $query->description = $request->description ?? null;
        $query->is_featured = $request->is_featured ?? 0;

        if ($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function detail(Request $request)
    {
        $data = ChainStore::find($request->id);
        if ($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;
        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function delete(Request $request)
    {
        if($request->id):
            ChainStore::find($request->id)->delete();
        else:
            ChainStore::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function featureUpdate(Request $request)
    {
        $item = ChainStore::find($request->id);
        $item->is_featured = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';
        $result = ChainStore::when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('name', 'LIKE', "%{$search}%");
            })
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'chain_stores_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'chain_stores'),
            $fileName
        );
    }

    function dropdownList($first='')
    {
        $result = ChainStore::orderBy('name', 'ASC')->get();

        $html = "";
        if($result->count() > 0):
            if($first):
                $html .= '<option value="">'.$first.'</option>';
            endif;
            foreach($result as $item):
                $html .= '<option value="'.$item->id.'">'.$item->name.'</option>';
            endforeach;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }
}