<?php

namespace App\Http\Controllers\Integration;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Traits\OrderTraits;
use Illuminate\Http\Request;
use Automattic\WooCommerce\Client;
use App\Http\Controllers\Controller;

class WoocommerceController extends Controller
{
    use OrderTraits;

    public function brandCommerce($brand)
    {
        switch ($brand) {
            case 'derma':
                $key = env('DERMA_WOOCOMMERCE_KEY');
                $secret = env('DERMA_WOOCOMMERCE_SECRET');
                $url = env('DERMA_WOOCOMMERCE_URL');

                break;

            case 'heartio':
                $key = env('HEARTIO_WOOCOMMERCE_KEY');
                $secret = env('HEARTIO_WOOCOMMERCE_SECRET');
                $url = env('HEARTIO_WOOCOMMERCE_URL');
                break;

            case 'stonecare':
                $key = env('STONECARE_WOOCOMMERCE_KEY');
                $secret = env('STONECARE_WOOCOMMERCE_SECRET');
                $url = env('STONECARE_WOOCOMMERCE_URL');
                break;

            default:
                // nattome
                $key = env('NATTOME_WOOCOMMERCE_KEY');
                $secret = env('NATTOME_WOOCOMMERCE_SECRET');
                $url = env('NATTOME_WOOCOMMERCE_URL');
                break;
        }

        return new Client($url, $key, $secret, null);
    }

    public function getOrderList($data = [])
    {
        $woocommerce = $this->brandCommerce($data['brand']);

        return $woocommerce->get('orders', $data);
    }

    public function createOrder($data = [])
    {
        $wcOrders = $this->getOrderList($data);

        try {
            foreach ($wcOrders as $wcOrder) {
                // create order user
                $phone_no = $wcOrder->shipping->phone;
                if ($phone_no == null) {
                    $phone_no = $wcOrder->billing->phone;
                }

                $user = User::firstOrCreate(
                    [
                        'phone_number' => '+6' . $phone_no,
                        'brand' => $data['brand'],
                    ],
                    [
                        'name' => $wcOrder->shipping->first_name . ' ' . $wcOrder->shipping->last_name,
                        'brand' => $data['brand'],
                        'email_address' => $wcOrder->billing->email,
                        'country' => $wcOrder->currency == 'MYR' ? 'malaysia' : 'singapore',
                    ]
                );

                switch($data['brand']):
                    case 'derma': $sales_channel_id = 10; break;
                    case 'heartio': $sales_channel_id = 11; break;
                    default: $sales_channel_id = 0; break;
                endswitch;

                // create Order only completed order
                $order = new Order;
                $order->user_id = $user->id;
                $order->user_type = 'customer';
                $order->brand = $data['brand'];
                $order->country = $wcOrder->currency == 'MYR' ? 'malaysia' : 'singapore';
                $order->name = $user->name;
                $order->phone_number = $wcOrder->shipping->phone;
                $order->email_address = $wcOrder->billing->email;
                $order->user_address_book_id = 0;
                $order->address_phone = $wcOrder->shipping->phone;
                $order->address = $wcOrder->shipping->address_1 . ' ' . $wcOrder->shipping->address_2 ?? '';
                $order->city = $wcOrder->shipping->city;
                $order->postcode = $wcOrder->shipping->postcode;
                $order->state = $wcOrder->shipping->state;
                $order->subtotal = convertStringToNumber($wcOrder->total);
                $order->shipping_fee = convertStringToNumber($wcOrder->shipping_total);
                $order->voucher = convertStringToNumber($wcOrder->discount_total);
                $order->total_tax = convertStringToNumber($wcOrder->total_tax);
                $order->total = convertStringToNumber($wcOrder->total);
                $order->point_earned = $this->calculatePoints($wcOrder->total, $wcOrder->currency); // calculate upon create order
                $order->payment_method = $wcOrder->payment_method;
                $order->status = $wcOrder->status;
                $order->order_no = $wcOrder->order_key;
                $order->sales_channel_id = $sales_channel_id;
                $order->sales_channel = $sales_channel_id ? (SalesChannel::find($sales_channel_id)->name ?? null) : null;
                $order->platform_from = 'woocommerce';
                $order->platform_from_id = $wcOrder->id;
                $order->platform_created_at = (new \DateTime($wcOrder->date_created))->format('Y-m-d H:i:s');

                $order->save();

                if (count($wcOrder->line_items) > 0) {
                    foreach ($wcOrder->line_items as $wcOrderItem) {
                        $order_item = new OrderItem();
                        $order_item->order_id = $order->id;
                        $order_item->product_name = $wcOrderItem->name;
                        $order_item->product_sku = $wcOrderItem->sku;
                        $order_item->is_gwp = false;
                        $order_item->is_pwp = false;
                        $order_item->quantity = $wcOrderItem->quantity;
                        $order_item->price_per_quantity = $wcOrderItem->price;
                        $order_item->total = $wcOrderItem->price;
                        $order_item->total_tax = $wcOrderItem->total_tax;
                        $order_item->save();
                    }
                }
            }
        } catch (\Throwable $th) {
            return $th;
        }
    }
}
