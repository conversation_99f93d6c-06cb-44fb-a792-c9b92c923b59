<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Products;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use Maatwebsite\Excel\Facades\Excel;

class ProductSubcategoryController extends Controller
{
    public function index()
    {
        if (!checkPermission('view_product_subcategory')):
            return redirect(route('index'))->with('error', __('You do not have permission to view price group.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('productSubcategoryDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('productSubcategoryExport'),
                'filename' => 'product_subcategories'
            ]
        ];

        if (!checkPermission('delete_product_subcategory')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_product_subcategory')):
            unset($bulk_action['export']);
        endif;

        $productCategories = ProductCategory::where('is_active', true)->get();

        $moduleID = 'product-subcategory';
        return view('product-subcategory', compact('bulk_action', 'moduleID', 'productCategories'));
    }

    public function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = ProductSubcategory::when($request->input('search.value'), function ($query) use ($request) {
            $search = $request->input('search.value');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%$search%");
            });
        })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->product_category) && $request->product_category != 'all', function ($query) use ($request) {
                $query->where('product_category_id', $request->product_category);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="price-groupsList">
                    <span></span>
                </label>';

                if (!checkPermission('edit_product_subcategory')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_product_subcategory')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-name="' . $item->name . '" data-product_weight="' . $item->product_weight . '" data-product_category_id="' . $item->product_category_id . '" data-status="' . $item->is_active . '" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_product_subcategory')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    $item->productCategory ? $item->productCategory->name : '-',
                    $item->product_weight ? __('Yes') : __('No'),
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_product_subcategory') && !checkPermission('delete_product_subcategory')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;

        if($id):
            $msg = __('Updated!');
            $query = ProductSubcategory::find($id);
        else:
            $msg = __('Added!');
            $query = new ProductSubcategory();
        endif;

        $query->name = $request->name;
        $query->product_category_id = $request->product_category_id;
        $query->is_active = $request->status ?? 0;
        $query->product_weight = $request->product_weight ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = ProductSubcategory::with('products')->find($request->id);
            Products::where('product_subcategory_id', $query->id)->update(['product_subcategory_id' => null]);
        else:
            $query = ProductSubcategory::with('products')->whereIn('id', $request->selected);
            Products::whereIn('product_subcategory_id', $request->selected)->update(['product_subcategory_id' => null]);
        endif;

        $query = $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = ProductSubcategory::find($request->id);

        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = ProductSubcategory::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->product_category) && $request->product_category != 'all', function ($query) use ($request) {
                $query->where('product_category_id', $request->product_category);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;
        
        $fileName = 'product_subcategory_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'product_subcategory'),
            $fileName
        );
    }
}
