<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RedemptionVouchers extends Model
{
    use HasFactory,SoftDeletes;
    public $table = "redemption_vouchers";

    protected $fillable = [
        'deleted_at',
    ];

    public $appends =[
        'image_url','banner_url','list_image_url'
    ];

    public function voucher_products()
    {
        return $this->hasMany(RedemptionVoucherProducts::class, 'redemption_voucher_id');
    }

    public function getAvailableDateAttribute()
    {
        if($this->available_start_date):
            return Carbon::parse($this->available_start_date)->format('d-m-Y, g:ia').' to '.Carbon::parse($this->available_end_date)->format('d-m-Y, g:ia');
        endif;
    }

    public function getRedemptionDateAttribute()
    {
        if($this->redemption_start_date):
            return Carbon::parse($this->redemption_start_date)->format('d-m-Y, g:ia').' to '.Carbon::parse($this->redemption_end_date)->format('d-m-Y, g:ia');
        endif;
    }

    public function getFullyRedeemedAttribute()
    {
        $available = $this->max_quantity ?? 0;
        if($available && $this->redeemed_quantity >= $available):
            return true;
        endif;
    }

    public function getUsedVoucherTotalAttribute()
    {
        return UserVouchers::where('voucher_id', $this->id)
            ->whereNotNull('used_date')
            ->count();
    }

    public function getBannerUrlAttribute()
    {
        if($this->banner):
            return GlobalFunction::createMediaUrl($this->banner);
        endif;
    }

    public function getImageUrlAttribute()
    {
        if($this->image):
            return GlobalFunction::createMediaUrl($this->image);
        endif;
    }

    public function getListImageUrlAttribute()
    {
        if($this->list_image):
            return GlobalFunction::createMediaUrl($this->list_image);
        endif;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }
}
