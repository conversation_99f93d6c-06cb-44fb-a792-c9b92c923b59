<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderApproval extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		$subject = __('[:site_name] Approval Needed for Order, :ref_no', [
			'site_name' => global_settings('site_name'),
			'ref_no' => $this->data['ref_no']
		]);
		
		$build = $this->subject($subject)
            ->view('emails.order-approval')
            ->with('data', $this->data);
	}
}