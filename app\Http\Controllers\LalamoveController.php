<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\ApiLogs;
use App\Models\Outlets;
use Illuminate\Http\Request;
use App\Services\LalamoveService;
use Illuminate\Support\Facades\Log;
use App\Models\LalamoveStatusHistory;
use App\Http\Controllers\Api\ApiOrders;
use App\Models\User;
use App\Models\UserAddressBook;

class LalamoveController extends Controller
{
    protected $lalamove;

    public function __construct(LalamoveService $lalamove)
    {
        $this->lalamove = $lalamove;
    }

    public function getQuote(Request $request)
    {

        $quotationData = [
            "serviceType" => "MOTORCYCLE",
            "language" => "en_MY",
            "stops" => [
                [
                    "coordinates" => [
                        "lat" => "3.142583656428478",
                        "lng" => "101.69811358036164"
                    ],
                    "address" => "MyBolehBoleh"
                ],
                [
                    "coordinates" => [
                        "lat" => "3.04659879560369",
                        "lng" => "101.61842286860826"
                    ],
                    "address" => "IOI Puchong"
                ]

            ],
            "item" => [
                "quantity" => "1",
                "weight" => "0.5",
                "categories" => ["FOOD_DELIVERY"],
                "handlingInstructions" => ["KEEP_UPRIGHT"]
            ],
            "isRouteOptimized" => true
        ];

        $validated = ['data' => $quotationData];

        return response()->json($this->lalamove->getQuotation($validated));
    }

    public function placeOrder(Request $request)
    {
        $validated = $request->validate([
            'quotationId' => 'required|string',
            'sender.name' => 'required|string',
            'sender.phone' => 'required|string',
            'sender.stopId' => 'required|string',
            'recipients.name' => 'required|string',
            'recipients.phone' => 'required|string',
            'recipients.stopId' => 'required|string',
            'specialRequests' => 'nullable|array',
            'isRouteOptimized' => 'nullable|boolean',
        ]);

        return response()->json($this->lalamove->placeOrder($validated));
    }

    public function getOrderStatus($orderId)
    {
        return response()->json($this->lalamove->getOrderStatus($orderId));
    }

    public function cancelOrder($orderId)
    {
        return response()->json($this->lalamove->cancelOrder($orderId));
    }

    public function getDriverLocation($orderId, $driverId)
    {
        return response()->json($this->lalamove->getDriverLocation($orderId, $driverId));
    }

    public function getDriverDetails($orderId, $driverId)
    {
        return response()->json($this->lalamove->getDriverDetails($orderId, $driverId));
    }

    public function handleWebhook(Request $request)
    {
        $body = $request->getContent();

        Log::channel('lalamove')->info('Lalamove Webhook : ' . json_encode($request->all()));

        $data = json_decode($body, true);

        // Process webhook event
        $this->processWebhookEvent($data);

        return response()->json(['status' => 'success']);
    }

    protected function processWebhookEvent(array $data)
    {
        $eventType = $data['eventType'] ?? null;

        switch ($eventType) {
            case 'ORDER_STATUS_CHANGED':
                $orderId = $data['data']['order']['orderId'] ?? null;

                $order = Order::where('tracking_number', $orderId)->first();

                if (!$order) {
                    return;
                }

                $order->update([
                    'lalamove_status' => $data['data']['order']['status'],
                    'share_link' => $data['data']['order']['shareLink']
                ]);

                $lalamove_status_history = new LalamoveStatusHistory();
                $lalamove_status_history->order_id = $order->id;
                $lalamove_status_history->status = $data['data']['order']['status'];
                $lalamove_status_history->response_data = json_encode($data);
                $lalamove_status_history->save();

                if (in_array($data['data']['order']['status'], ['REJECTED','EXPIRED'])) {
                    $this->lalamoveReorder($order->quotation_id,$order);
                }

                if($data['data']['order']['status'] == 'PICKED_UP'){
                    $order->update(['status' => 'shipped']);
                }elseif($data['data']['order']['status'] == 'COMPLETED'){
                    $order->update(['status' => 'completed']);
                }
                break;
            case 'DRIVER_LOCATION_UPDATE':
                // Update driver location
                break;
            case 'ORDER_CANCELLED':
                // Handle order cancellation
                break;
            default:
                // Log unknown event type
                break;
        }
    }
}