<?php

namespace App\Models;

use App\Models\Products;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductSubcategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'product_category_id',
        'product_weight',
        'is_active'
    ];

    public function productCategory()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id', 'id');
    }

    public function products()
    {
        return $this->hasMany(Products::class, 'product_subcategory_id', 'id');
    }
}
