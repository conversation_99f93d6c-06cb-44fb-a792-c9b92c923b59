<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CronjobLogs as Cronjob;
use App\Models\BookingCheckSlots;

class BookingDeleteCheckingData extends Command
{
    protected $signature = 'booking_delete_checking_data:cron';
    protected $description = 'delete from database booking_check_slots after 5 minutes';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $bookings = BookingCheckSlots::where('created_at', '<', date("Y-m-d H:i:s", strtotime("-5 minutes")))
            ->get();

        $deleted = "";
        if(count($bookings) > 0):
            foreach($bookings as $item):
                $deleted .= json_encode($item).", ";
                $item->delete();
            endforeach;

            $deleted = substr($deleted, 0, -2);
        endif;

        // cronjob log
        if(isset($deleted) && $deleted):
            Cronjob::create([
                "type" => "booking delete check data",
                "recipient" => $deleted
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}