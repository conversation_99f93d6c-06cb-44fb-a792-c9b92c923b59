<?php

namespace App\Http\Controllers\API;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;

class ApiGoogle extends Controller
{
    private $client;

    public function __construct()
    {
        $this->client = Http::baseUrl('https://places.googleapis.com/v1/');
    }

    public function placeSearch($path, Request $request)
    {
        try {
            $response = $this->client
                ->withHeaders([
                    'X-Goog-Api-Key' => env('GOOGLE_MAP_API_KEY'),
                    'X-Goog-FieldMask' => $request->header('X-Goog-FieldMask') ?? '*',
                    'Content-Type' => 'application/json',
                ])
                ->post($path, $request->toArray());

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Google Places API Error: ' . $response->status() . ' - ' . $response->body());
            return null;
        } catch (\Exception $e) {
            Log::error('Google Places API Exception: ' . $e->getMessage());
            return null;
        }
    }

    public function geocodingApi($path, Request $request)
    {
        $apiUrl = 'https://maps.googleapis.com/maps/api/';

        $url = $apiUrl . $path;

        $client = new Client();
        $response = $client->request('GET', $url, [
            'query' => $request->query()
        ]);
        $statusCode = $response->getStatusCode();

        if ($statusCode == 200) {
            return response()->json(json_decode($response->getBody()));
        }

        return response()->json(['status' => false, 'data' => json_decode($response->getBody())]);
    }

    public function placeDetail($placeId, Request $request)
    {
        try {
            $response = $this->client
                ->withHeaders([
                    'X-Goog-Api-Key' => env('GOOGLE_MAP_API_KEY'),
                    'X-Goog-FieldMask' => $request->header('X-Goog-FieldMask') ?? '*',
                    'Content-Type' => 'application/json',
                ])
                ->get('places/' . $placeId);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Google Place Detail Error: ' . $response->status() . ' - ' . $response->body());
        } catch (\Exception $e) {

            Log::error('Google Place Detail Exception: ' . $e->getMessage());
            return null;
        }
    }
}
