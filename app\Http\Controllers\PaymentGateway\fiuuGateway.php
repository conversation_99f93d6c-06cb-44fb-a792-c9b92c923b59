<?php
namespace App\Http\Controllers\PaymentGateway;

use App\Models\User;
use App\Models\Order;
use App\Models\Constants;
use App\Traits\EmailTraits;
use Illuminate\Http\Request;
use App\Services\FiuuService;
use App\Traits\WalletsTraits;
use App\Models\GlobalFunction;
use RazerMerchantServices\Payment;
use App\Models\PaymentTransactions;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Traits\PaymentGatewaysTraits;

class fiuuGateway  extends Controller
{
    use PaymentGatewaysTraits;
    use WalletsTraits;
    use EmailTraits;

    function url($data)
    {
        $rms = new Payment(env('RMS_MERCHANT_ID'), env('RMS_VERIFY_KEY'), env('RMS_SECRET_KEY'), env('RMS_ENVIRONMENT'));
        $paymentUrl = $rms->getPaymentUrl($data->order->order_no, number_format($data->amount, 2, ".", ""), $data->order->name, $data->order->email_address, $data->order->phone_number,$data->order->order_no);

        return $paymentUrl;
    }

    function response($request)
    {
        return $this->handleFiuuPayment($request, 'Fiuu return');
    }

    function callback($request)
    {
        return $this->handleFiuuPayment($request, 'Fiuu callback');
    }

    function notification($request)
    {
        return $this->handleFiuuPayment($request, 'Fiuu notification');
    }

    private function handleFiuuPayment($request, string $log)
    {
        $rms = new Payment(env('RMS_MERCHANT_ID'), env('RMS_VERIFY_KEY'), env('RMS_SECRET_KEY'), env('RMS_ENVIRONMENT'));
        $key = md5($request->tranID . $request->orderid . $request->status . $request->domain . $request->amount . $request->currency);
        $isPaymentValid = $rms->verifySignature($request->paydate, $request->domain, $key, $request->appcode, $request->skey);
        $order = Order::with('payment')->where('order_no', $request->orderid)->first();

        if (!$order) {
            Log::channel('payment-gateway')->warning("$log: Order not found", ['request' => $request->all()]);
            return response()->json(['message' => 'Order not found'], 404);
        }

        $response = $request->all();
        $transStatus = $response['status'];
        $payment = $order->payment;
        $transID = $response['tranID'];

        Log::channel('payment-gateway')->info("$log: " . json_encode($request->all()));

        //if status success continue
        if ($payment && $payment->status == Constants::paymentStatus['paid']) {
            Log::channel('payment-gateway')->info("$log: Payment already processed, skipping", [
                'order_no' => $order->order_no,
                'payment_status' => $payment->status
            ]);

            return $this->returnPaymentResponse(
                $payment->status == Constants::paymentStatus['failed'] ? 'failed' : 'success',
                $payment
            );
        }

        if ($isPaymentValid) {
            //00 = success ,11 = failed , 22 = pending
            if($transStatus == 22 || $payment->status != Constants::paymentStatus['pending']){
                // Recover from failed -> success
                if ($payment->status == Constants::paymentStatus['failed'] && $transStatus == 00) {
                    Log::channel('payment-gateway')->info("$log: Payment previously failed, received success — updating full record", [
                        'order_no' => $order->order_no,
                        'previous_status' => $payment->status,
                        'incoming_status' => $transStatus,
                    ]);

                    $this->updatePaymentSuccess($payment, $response, $transID);
                    $status = 'success';

                } else {
                    $status = ($payment->status == Constants::paymentStatus['failed']) ? 'failed' : 'success';
                }

                return $this->returnPaymentResponse($status, $payment);
            }

            if($transStatus == 00){
                $status = 'success';
                $this->updatePaymentSuccess($payment, $request->all(), $transID);
            }else{
                $status = 'failed';
                $this->updatePaymentFailed($payment, $request->all(), $transID);
            }

        }else{
            $status = 'failed';
            $this->updatePaymentFailed($payment, $request->all(), $transID);
        }

        return $this->returnPaymentResponse($status, $payment);
    }
}