<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Admin\DoctorController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\AddedPatients;
use App\Models\Appointments;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\PatientRelationship;
use App\Models\User;
use App\Models\MembershipTiers;
use App\Models\UserMembership;
use App\Exports\GeneralExport;
use App\Traits\EmailTraits;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Retailer;

class UsersController extends Controller
{
    use EmailTraits;
    
    function users()
    {
        if(!checkPermission('view_user')):
            return redirect(route('index'))->with('error', __('You do not have permission to view user.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('usersDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('usersExport'),
                'filename' => 'users'
            ]
        ];

        if(!checkPermission('delete_user')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_user')):
            unset($bulk_action['export']);
        endif;

        $membership_tier = MembershipTiers::where('is_active', '1')->get();
        $admin = session('user');
        $moduleID = 'users';
        return view('users', compact('bulk_action', 'membership_tier', 'moduleID', 'admin'));
    }

    function usersList(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = User::whereNull('deleted_at')
            ->when($request->input('search.value'), function($q) use ($request) {
                $search = $request->input('search.value');
                $q->where(function($q) use ($search) {
                    $q->where('email_address', 'LIKE', "%{$search}%")
                        ->orWhere('name', 'LIKE', "%{$search}%")
                        // ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('phone_number', 'LIKE', "%{$search}%");
                });
            })
            // ->whereIn('brand', userBrand())
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->membership_tier, function($query) use ($request) {
                $query->where('membership_tier', $request->membership_tier);
            })
            // ->when($request->brand && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->when($request->country && $request->country != 'all', function($query) use ($request) {
                $query->where('country', $request->country);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $id = $item->id;
                $totalAppointments = "<div class='text-center'>".$item->appointments->count()."</div>";

                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="usersList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_user')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:                
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $retailer_name = $item->retailer ? 'data-retailer-name="'.$item->retailer->first_name. ' ' . $item->retailer->last_name .'"' : '';
                $retailer_email = $item->retailer ? 'data-retailer-email="'.$item->retailer->user_name. '"' : '';
                $item->dob = $item->dob ? Carbon::parse($item->dob)->format('d-m-Y') : '';
                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_user')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="'.$id.'" data-toggle="tooltip" data-title="'.__('Edit').'" data-firstname="' . $item->first_name . '" data-lastname="' . $item->last_name . '" data-dob="'.$item->dob.'" data-status="'.$item->is_active.'" data-phone="'.$item->phone_number.'" data-email="'.$item->email_address.'" data-membership="'.$item->membership_tier.'" data-brand="'.$item->brand.'" data-country="'.$item->country.'" data-name="' . $item->name . '" data-retailer-id="' . $item->retailer_id . '"'. $retailer_name . ' ' .$retailer_email .'><i class="fa fa-edit"></i></a>';
                endif;

                if(checkPermission('delete_user')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;

                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->phone_number,
                    $item->name,
                    // $item->last_name,
                    $item->email_address,
                    // config('staticdata.brand.' . $item->brand),
                    // config('staticdata.country.' . $item->country),
                    // '456',
                    $item->membership_tiers->name,
                    // formatNumber($item->wallet_balance, 2),
                    formatNumber($item->point_balance),
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    '<div class="text-center">'.$action.'</div>',
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                    $param = array_values($param);
                endif;

                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function usersAddUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $email = $request->email_address;
        $phone = $request->phone_number;

        // check for email unique
        $checkExist = User::whereNull('deleted_at')
            ->where('email_address', $email)
            ->when($id > 0, function($q) use($id)
            {
                $q->where('id', '!=', $id);
            })
            ->first();
        if($checkExist):
            $error_msg = __('Email address is already taken.');
            return GlobalFunction::sendSimpleResponse(false, $error_msg);
        endif;
        // END check for email unique

        if(!GlobalFunction::validatePhoneNumber($phone)):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
        endif;

        if($request->password && $request->password != $request->confirm_password):
            return GlobalFunction::sendSimpleResponse(false, __('Password and confirm password does not match.'));
        endif;

        // $check = User::where('brand', $request->brand)->where('phone_number', $phone)->whereNull('deleted_at');
        $check = User::where('phone_number', $phone)->whereNull('deleted_at')->where('id', '!=', $id)->first();
        // if($id):
        //     $check = $check->where('id', '!=', $id);
        // endif;
        // $check = $check->count();
        if($check):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number is already taken.'));
        endif;

        if($id):
            $msg = __('Updated!');
            $user = User::find($id);
        else:
            $msg = __('Added!');
            $user = new User();
        endif;

        $user->email_address = $email;
        $user->phone_number = $phone;
        $user->membership_tier = $request->membership_tier;
        $user->name = $request->name;
        // $user->last_name = $request->last_name;
        $user->is_active = $request->status ?? 0;
        // $user->country = $request->country;
        // $user->brand = $request->brand;
        // $user->retailer_id = isset($request->retailer_id) ? $request->retailer_id : null;

        if($request->dob):
            $user->dob = Carbon::parse($request->dob)->format('Y-m-d');
            
            // calculate age based on dob
            // $from = new \DateTime($user->dob);
            // $to   = new \DateTime('today');
            // $user->age = $from->diff($to)->y;
        endif;

        if($request->has('image')):
            $user->profile_image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        if(!$id):
            $password = GlobalFunction::generateRandomString(8);
            $user->password = Hash::make($password);
            $user->device_type = 1;
            $user->device_token = null;
            $user->login_type = 'email';
            $user->is_verified = 1;

            // email user for password
            $email_data = [
                'type' => 'member',
                'email_type' => 'account_created',
                'name' => $request->name,
                'email' => $email,
                'password' => $password,
            ];
            $this->sendEmail($email_data);
            // END email user for password
        else:
            if($request->password && $request->password == $request->confirm_password):
                $user->password = Hash::make($request->password);
            endif;
        endif;

        $user->save();
        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function usersDelete(Request $request)
    {
        if($request->id):
            User::find($request->id)->update(['deleted_at' => Carbon::now()]);
        else:
            User::whereIn('id', $request->selected)->update(['deleted_at' => Carbon::now()]);
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function usersStatusUpdate(Request $request)
    {
        $item = User::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function usersExport(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'id';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'DESC';
        $result = User::whereNull('deleted_at')
            ->when($request->search, function($q) use ($request) {
                $search = $request->search;
                $q->where('email_address', 'LIKE', "%{$search}%")
                    ->orWhere('name', 'LIKE', "%{$search}%")
                    // ->orWhere('last_name', 'LIKE', "%{$search}%")
                    ->orWhere('phone_number', 'LIKE', "%{$search}%");
            })
            // ->whereIn('brand', userBrand())
            ->when($request->selected, function($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when($request->membership_tier, function($query) use ($request) {
                $query->where('membership_tier', $request->membership_tier);
            })
            // ->when($request->brand && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            // ->when($request->country && $request->country != 'all', function($query) use ($request) {
            //     $query->where('country', $request->country);
            // })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                if($item->dob):
                    $item->dob = Carbon::parse($item->dob)->format('d-m-Y');
                endif;

                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $item->brand = config('staticdata.brand.'.$item->brand);
                $item->country = config('staticdata.country.'.$item->country);
            endforeach;
        endif;
        
        $fileName = 'users_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, "users"),
            $fileName
        );
    }

    function usersDropdownList($first='')
    {
        $search_type = request()->type ?? '';

        $result = User::when(request()->search, function($q) use ($search_type) {
                $search = request()->search;

                if($search_type == 'phone'):
                    $q->where('phone_number', 'LIKE', "%{$search}%");
                else:
                    $q->where(function($q) use ($search) {
                        $q->where('email_address', 'LIKE', "%{$search}%")
                            ->orWhere('phone_number', 'LIKE', "%{$search}%")
                            ->orWhere('name', 'LIKE', "%{$search}%");
                    });
                endif;
            })
            // ->when(request()->brandList, function($q) {
            //     $q->whereIn('brand', json_decode(request()->brandList));
            // })
            // ->when(request()->brand, function($q) {
            //     $q->where('brand', request()->brand);
            // })
            // ->when(request()->countryList, function($q) {
            //     $q->whereIn('country', json_decode(request()->countryList));
            // })
            // ->when(request()->country, function($q) {
            //     $q->where('country', request()->country);
            // })
            ->whereNull('deleted_at')
            ->orderBy('name', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            if (isset(request()->json)) {
                $user = [];
                foreach ($result as $item) {
                    // $user_detail = $item->name . ' (' . $item->email_address . ')';
                    // $user_detail = $item->name . ' (' . $item->phone_number . ') - ' . config('staticdata.brand.'.$item->brand);
                    $user_detail = $item->name . ' (' . $item->phone_number . ') ' . '(' . $item->email_address . ')';

                    $user[] = [
                        'id' => $item->id,
                        'text' => $user_detail,
                    ];
                }

                return response()->json([
                    'results' => $user,
                ]);
            }
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->name;
                    // if($item->email_address):
                    //     $html .= ' ('.$item->email_address.')';
                    // endif;
                    $html .= ' ('.$item->phone_number.')';
                    $html .= ' - '.config('staticdata.brand.'.$item->brand);
                    $html .= '</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function usersDetail()
    {
        $result = User::where('id', request()->id)->first();

        return response()->json([
            'results' => $result,
        ]);
    }
}