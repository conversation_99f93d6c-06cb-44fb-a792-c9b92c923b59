<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Products;
use App\Models\BirthdayGift;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\BirthdayGiftItem;
use App\Models\ProductVariations;
use App\Http\Controllers\Controller;

class BirthdayGiftController extends Controller
{
    function index()
    {
        if (!checkPermission('view_birthday_gifts')):
            return redirect(route('index'))->with('error', __('You do not have permission to view birthday gifts.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('birthdayGiftDelete')
            ],
        ];

        if(!checkPermission('delete_birthday_gifts')):
            unset($bulk_action['delete']);
        endif;
        // if(!checkPermission('export_birthday_gifts')):
        //     unset($bulk_action['export']);
        // endif;

        // $point_convert = GlobalSettings::where('name', 'bookings_points_rm_ratio')->first()->value;
        $memberships = MembershipTiers::where('is_active', true)->get();

        $moduleID = 'birthday_gifts';
        return view('birthday-gift', compact('bulk_action', 'moduleID', 'memberships'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = BirthdayGift::when($request->search, function($q) use ($request) {
            // $q->where('ref_no', 'LIKE', "%{$search}%");
        })
            ->when(isset($request->is_active) && $request->is_active != 'all', function($query) use ($request) {
                $query->where('is_active', $request->is_active);
            })
            ->when(isset($request->month) && $request->month != 'all', function ($query) use ($request) {
                $query->whereJsonContains('month', $request->month);
            })
            ->when(isset($request->brand) && $request->brand != 'all', function ($query) use ($request) {
                $query->whereJsonContains('brand', $request->brand);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];

        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="birthday_giftsList">
                    <span></span>
                </label>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_birthday_gifts')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_birthday_gifts')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $month_name = '';
                $months = json_decode($item->month);
                foreach ($months as $key => $month) {
                    if ($key+1 != count($months)) {
                        $month_name .= config('staticdata.months.'.$month) . ', ';
                    } else {
                        $month_name .= config('staticdata.months.'.$month);
                    }
                }

                $brand_name = '';
                $brands = json_decode($item->brand);
                foreach ($brands as $key => $brand) {
                    if ($key+1 != count($brands)) {
                        $brand_name .= config('staticdata.brand.'.$brand) . ', ';
                    } else {
                        $brand_name .= config('staticdata.brand.'.$brand);
                    }
                }

                if (!checkPermission('edit_birthday_gifts')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                            <label class="switch mb-0">
                                <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                                <span class="slider round"></span>
                            </label>
                        </div>';
                endif;

                $total_gifts = count($item->items);

                $param = [
                    $checkbox,
                    $month_name,
                    $brand_name,
                    $total_gifts,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_purchase_receipts')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $data = BirthdayGift::find($request->id);
        if ($data->items) {
            foreach ($data->items as $item) {
                $item->product_name = Products::find($item->product_id)->name;
                $item->variation_name = ProductVariations::find($item->variation_id) ? ProductVariations::find($item->variation_id)->getVariationNameAttribute() : '';
            }
        }

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $msg = ($id ? __('Updated!') : __('Added!'));

        if (!$request->product_id) {
            return GlobalFunction::sendSimpleResponse(false, __('Add at least one product'));
        } else {
            foreach ($request->product_id as $key => $product_id) {
                if ($request->product_variation_id && array_key_exists($key, $request->product_variation_id) && is_null($request->product_variation_id[$key])) {
                    return GlobalFunction::sendSimpleResponse(false, __('Please select variation'));
                }

                if (!$request->quantity[$key]) {
                    return GlobalFunction::sendSimpleResponse(false, __('Please enter quantity'));
                }

                if (!$request->memberships || !isset($request->memberships[$key])) {
                    return GlobalFunction::sendSimpleResponse(false, __('Please select memberships'));
                }
            }
        }

        if ($id):
            $query = BirthdayGift::find($id);
        else:
            $query = new BirthdayGift();
        endif;

        $query->month = json_encode($request->month);
        $query->brand = json_encode($request->brand);
        $query->is_active = $request->is_active ?? 0;

        $query->save();
        $id = $query->id;

        if ($query->items) {
            BirthdayGiftItem::where('id')->delete();
        }
    
        $item_exists = [];
        $product_ids = $request->product_id;

        foreach ($product_ids as $key => $product_id) {
            if ($key != 'new_row') {
                $birthday_gift_item_id = $request->birthday_gift_item_id[$key] ?? '';
                $variation_id = $request->product_variation_id[$key] ?? '';
                $quantity = $request->quantity[$key] ?? '';
                $membership_tier_ids = $request->memberships[$key] ?? '';

                if(!$birthday_gift_item_id):
                    $query = new BirthdayGiftItem();
                else:
                    $query = BirthdayGiftItem::find($birthday_gift_item_id);
                endif;

                $query->birthday_gift_id = $id;
                $query->product_id = $product_id;
                $query->variation_id = (int)$variation_id;
                $query->quantity = (int)$quantity;
                $query->membership_tier_ids = json_encode($membership_tier_ids);
                $query->save();

                $item_exists[] = $query->id;
            }
        }

        if($item_exists):
            BirthdayGiftItem::where('birthday_gift_id', $id)
                ->whereNotIn('id', $item_exists)
                ->delete();
        else:
            BirthdayGiftItem::where('birthday_gift_id', $id)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if ($request->id):
            BirthdayGift::find($request->id)->delete();
        else:
            BirthdayGift::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        //
    }
}
