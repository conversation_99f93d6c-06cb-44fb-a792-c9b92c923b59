<?php
namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Constants;
use App\Models\DoctorNotifications;
use App\Models\DoctorReviews;
use App\Models\Doctors;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Models\PasswordReset;
use App\Models\Users;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Admin\DoctorController as AdminDoctorController;

class DoctorController extends Controller
{
    function fetchDoctorReviews(Request $request)
    {
        $rules = [
            'start' => 'required',
            'count' => 'required',
            'doctor_id' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $messages = $validator->errors()->all();
            $msg = $messages[0];
            return response()->json(['status' => false, 'message' => $msg]);
        }
        $doctor = Doctors::where('id', $request->doctor_id)->first();
        if ($doctor == null) {
            return GlobalFunction::sendSimpleResponse(false, 'Doctor does not exists!');
        }
        $result =  DoctorReviews::with(['user'])
            ->Where('doctor_id', $request->doctor_id)
            ->whereHas('user')
            ->whereHas('doctor')
            ->orderBy('id', 'DESC')
            ->offset($request->start)
            ->limit($request->count)
            ->get();

        return GlobalFunction::sendDataResponse(true, 'data fetched successfully', $result);
    }

    function fetchDoctorNotifications(Request $request)
    {
        $rules = [
            'start' => 'required',
            'count' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $messages = $validator->errors()->all();
            $msg = $messages[0];
            return response()->json(['status' => false, 'message' => $msg]);
        }

        $doctorNotifications = DoctorNotifications::offset($request->start)
            ->limit($request->count)
            ->orderBy('id', 'DESC')
            ->get();

        return response()->json(['status' => true, 'message' => 'Data fetched successfully !', 'data' => $doctorNotifications]);
    }

    // WEB
    public function emailVerify($token, $sent='')
    {
        $doctor = Doctors::where('email_verification_token', $token)->first();
        if(!$doctor):
            $msg = __('Account not found.');
        else:
            if(!$sent):
                if($doctor->status == 2):
                    $msg = __('This account is blocked.');
                elseif($doctor->email_verification_exp_date < date('Y-m-d H:i:s')):
                    $msg = __('The link is expired.<br>Please click on the button below to send the verification email again.');
                    $btn_verify = 1;
                elseif($doctor->email_verification_date):
                    $msg = __('This account has been verified previously.');
                endif;
            else:
                $msg = __('An email have sent to :email.<br>Please login to your email and click on the link or button to verify your account.', ['email' => $doctor->email_address]);
            endif;
        endif;

        if(!isset($msg)):
            $doctor->email_verification_date = date('Y-m-d H:i:s');
            $doctor->email_verification_exp_date = null;
            $doctor->email_verification_token = null;
            $doctor->save();
            
            $msg = __('Your account is verified.<br>You may login through the app now.');
        endif;

        return view('non-auth.email-verified', [
            'token' => $token,
            'msg' => $msg,
            'btn_verify' => $btn_verify ?? null
        ]);
    }

    public function passwordResetVerify($token = '')
    {
        if($token):
            $users = PasswordReset::with(['user'])->where('token', $token)->first();
            if(!$users):
                $msg = __('Account not found or the link is not available.');
            else:
                if($users->user->is_block):
                    $msg = __('This account is blocked.');
                elseif($users->expired_date < date('Y-m-d H:i:s')):
                    $msg = __('The link is expired.<br>Please request through the app again.');
                endif;
            endif;
        else:
            $msg = __('Your password has been reset.<br>You may login through the app now.');
        endif;

        return view('non-auth.reset-password', [
            'token' => $token,
            'msg' => $msg ?? null,
            'url' => route('doctor.passwordReset')
        ]);
    }

    public function passwordReset(Request $request)
    {
        $doctors = PasswordReset::with(['doctor'])->where('token', $request->token)->first();
        if(!$doctors):
            return GlobalFunction::sendSimpleResponse(false, 'Account not found or the link is not available.');
        endif;

        $rules = [
            'password' => 'required|same:confirm_password'
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        Doctors::where('id', $doctors->doctor->id)
            ->update(['password' => Hash::make($request->password)]);

        PasswordReset::where('token', $request->token)->delete();

        return GlobalFunction::sendSimpleResponse(true, route('doctor.passwordResetVerify'));
    }
}