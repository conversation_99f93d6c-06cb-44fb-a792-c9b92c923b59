<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\BookingRateReviews;
use App\Traits\BookingsTraits;

class ApiRatings
{
    use BookingsTraits;

    function rateReview(Request $request)
    {
        $data = [
            'booking_id' => $request->booking_id,
            'rating' => $request->rating,
            'review' => $request->review,
        ];
        $result = $this->createRateReview($data);

        if(isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendSimpleResponse(true, __('Thank you for your review.'));
    }

    function reviewListing($type, Request $request)
    {
        $request->request->add([
            'type' => $type,
        ]);
        $data = $this->getReviewListing($request);

        if(isset($data['error'])):
            return GlobalFunction::sendSimpleResponse(false, $data['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function reviewDetail($type, Request $request)
    {
        $request->request->add([
            'type' => $type,
        ]);
        $data = $this->getReviewDetail($request);

        if(isset($data['error'])):
            return GlobalFunction::sendSimpleResponse(false, $data['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function reviewRanking($by_outlet, Request $request)
    {
        $range = $request->range;
        $outlet_id = $request->outlet_id ?? null;
        $limit = $request->limit ?? 5;

        if(!$range):
            return GlobalFunction::sendSimpleResponse(false, __('Please provide is for week or month.'));
        endif;

        if($range == 'week'):
            $start_date = date('Y-m-d', strtotime('monday this week'));
            $end_date = date('Y-m-d', strtotime('sunday this week'));
        elseif($range == 'month'):
            $start_date = date('Y-m-01');
            $end_date = date('Y-m-t');
        endif;

        if($by_outlet == 'by-outlet' && !$outlet_id):
            $outlets = BookingRateReviews::with('outlet')
                ->whereBetween('created_at', [$start_date, $end_date])
                ->distinct()
                ->get(['outlet_id']);

            $data = [];
            foreach($outlets as $outlet_item):
                $rating = BookingRateReviews::selectRaw('staff_id, AVG(rate) as rate')
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->where('outlet_id', $outlet_item->outlet_id)
                    ->groupBy('staff_id')
                    ->orderBy('rate', 'desc')
                    ->limit($limit)
                    ->get();

                foreach($rating as $item):
                    $item->rate = number_format($item->rate, 1);
                    $item->staff_name = $item->staff->first_name . ' ' . $item->staff->last_name;

                    unset($item->staff);
                endforeach;

                $data[] = [
                    'outlet_id' => $outlet_item->outlet_id,
                    'outlet_name' => $outlet_item->outlet->name,
                    'rating' => $rating,
                ];
            endforeach;

            if($data == []):
                $data = null;
            endif;
        else:
            $data = BookingRateReviews::selectRaw('staff_id, AVG(rate) as rate')
                ->whereBetween('created_at', [$start_date, $end_date]);
            
            if($outlet_id):
                $data = $data->where('outlet_id', $outlet_id);
            endif;

            $data = $data->groupBy('staff_id')
                ->orderBy('rate', 'desc')
                ->limit($limit)
                ->get();

            foreach($data as $item):
                $item->rate = number_format($item->rate, 1);
                $item->staff_name = $item->staff->first_name . ' ' . $item->staff->last_name;

                unset($item->staff);
            endforeach;
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}