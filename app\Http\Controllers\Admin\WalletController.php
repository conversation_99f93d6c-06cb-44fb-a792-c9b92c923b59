<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Users;
use App\Models\Constants;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\UserWalletStatements;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\DoctorWalletStatements;
use App\Models\UserWalletRechargeLogs;
use App\Models\UserWalletTransactions;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\DoctorController;

class WalletController extends Controller
{
    function wallet($type)
    {
        $filter_user = (new UsersController)->usersDropdownList('All Members');
        $filter_user = $filter_user->getData()->data;

        $user_list = (new UsersController)->usersDropdownList('Select Member');
        $user_list = $user_list->getData()->data;

        $filter_type = [
            'Top-Up' => __('Top-Up'),
            'Stringing Booking' => __('Stringing Booking'),
            'Wallet Adjustment' => __('Wallet Adjustment')
        ];

        $filter_amount_type = [
            '+' => __('+'),
            '-' => __('-'),
        ];

        $moduleID = 'wallets';
        return view('wallet', compact('moduleID', 'filter_user', 'filter_type', 'user_list', 'filter_amount_type'));
    }

    function walletList(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');
        $date = $request->date;
        $type = $request->type;
        $amount_type = $request->amounttype;
        $user = $request->user;

        $result = UserWalletTransactions::with(['user', 'booking'])
            ->when($search, function ($q) use ($search) {
                $q->where('ref_no', 'like', '%' . $search . '%');
            })
            ->when($date, function ($q) use ($date) {
                $date = explode(" to ", $date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->when($type, function ($q) use ($type) {
                $q->where('type', $type);
            })
            ->when($amount_type, function ($q) use ($amount_type) {
                $q->where('amount_type', $amount_type);
            })
            ->when($user, function ($q) use ($user) {
                $q->where('user_id', $user);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            $summary = UserWalletTransactions::with(['user', 'booking'])
                ->when($search, function ($q) use ($search) {
                    $q->where('ref_no', 'like', '%' . $search . '%');
                })
                ->when($date, function ($q) use ($date) {
                    $date = explode(" to ", $date);
                    $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                        ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
                })
                ->when($type, function ($q) use ($type) {
                    $q->where('type', $type);
                })
                ->when($user, function ($q) use ($user) {
                    $q->where('user_id', $user);
                })
                ->orderBy($sort_col, $sort_by)
                ->get();
            
            $add_amounts = $summary->where('amount_type', '+')->sum('amount');
            $deduct_amounts = $summary->where('amount_type', '-')->sum('amount');
            $amounts_balance = $add_amounts - $deduct_amounts;

            foreach ($result as $count => $item):
                $type = $item->type;
                $type_detail = $type;

                if($type == 'Stringing Booking'){
                    $type_detail .= '<div class="text-grey">'.$item->booking->ref_no.'</div>';
                }

                $user = "<div>" . $item->user->first_name . ' ' . $item->user->last_name . '<br>' . $item->user->email_address . "</div>";;

                $date = "<div class='text-center'>" . Carbon::parse($item->created_at)->format('d-m-Y') . '<br>' . Carbon::parse($item->created_at)->format('g:i a') . "</div>";

                if ($count == 0):
                    $date .= '<input type="hidden" name="amountBalance" value="' . formatNumber($amounts_balance, 2) . '">';
                    $date .= '<input type="hidden" name="addAmounts" value="' . formatNumber($add_amounts, 2) . '">';
                    $date .= '<input type="hidden" name="deductAmounts" value="' . formatNumber($deduct_amounts, 2) . '">';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">
                    <a href="' . route('walletDetail', $item->id) . '" class="btn-icon btn-info" data-toggle="tooltip" data-title="' . __('View') . '"><i class="fa fa-search"></i></a>
                </div>';

                $amount = "<div class='text-right'>" . $item->amount_type . ' ' . number_format($item->amount, 2) . "</div>";

                $param = [
                    $date,
                    $item->ref_no,
                    $user,
                    $type_detail,
                    $amount,
                    $action
                ];

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function walletExport(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');
        $date = $request->date;
        $type = $request->type;
        $amount_type = $request->amounttype;
        $user = $request->user;

        $result = UserWalletTransactions::with(['user', 'booking'])
            ->when($search, function ($q) use ($search) {
                $q->where('ref_no', 'like', '%' . $search . '%');
            })
            ->when($date, function ($q) use ($date) {
                $date = explode(" to ", $date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->when($type, function ($q) use ($type) {
                $q->where('type', $type);
            })
            ->when($amount_type, function ($q) use ($amount_type) {
                $q->where('amount_type', $amount_type);
            })
            ->when($user, function ($q) use ($user) {
                $q->where('user_id', $user);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                switch ($item->type):
                    case ('Stringing Booking'):
                        $item->type_ref = $item->booking->ref_no;
                        break;
                endswitch;

                $item->date = Carbon::parse($item->created_at)->format('d-m-Y g:i a');
            endforeach;
        endif;

        $fileName = 'user_wallet_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'wallet'),
            $fileName
        );
    }

    function walletUpdate(Request $request)
    {
        $amount_type = $request->amount_type;
        $amount = $request->amount;

        $user = User::find($request->user);

        if ($amount_type == '+'):
            $user->increment('wallet_balance', $amount);
        else:
            $user->decrement('wallet_balance', $amount);
        endif;

        $wallet_transaction = new UserWalletTransactions();
        $wallet_transaction->user_id = $request->user;
        $wallet_transaction->type = 'Wallet Adjustment';
        $wallet_transaction->remarks = $request->remarks;
        $wallet_transaction->amount_type = $amount_type;
        $wallet_transaction->amount = $request->amount;
        $wallet_transaction->save();

        $ref_no = 'W' . str_pad($wallet_transaction->id, 8, '0', STR_PAD_LEFT);
        $wallet_transaction->ref_no = $ref_no;
        $wallet_transaction->save();

        return GlobalFunction::sendSimpleResponse(true, 'Wallet updated!');
    }

    function getUserWallet(Request $request)
    {
        $wallet_balance = 0;

        if ($request->id) {
            $wallet_balance = User::find($request->id)->wallet_balance;
        }
        $data = ["wallet_balance" => $wallet_balance];
        return $data;
    }


    function walletDetail($id)
    {
        if (!checkPermission('view_wallet_transactions')):
            return redirect(route('index'))->with('error', __('You do not have permission to view wallet transactions.'));
        endif;

        $wallet_transaction = UserWalletTransactions::with(['user', 'paymentTransaction', 'booking'])->find($id);

        return view('wallet-transaction-details', compact('wallet_transaction'));
    }

    function walletTopup(Request $request)
    {
        $user = Users::find($request->user_id);
        $user->wallet = $user->wallet + $request->amount;
        $user->save();

        // Adding Statement entry
        GlobalFunction::addUserStatementEntry(
            $user->id,
            null,
            $request->amount,
            Constants::credit,
            Constants::deposit,
            $request->transaction_summary
        );

        // Recharge Wallet History
        $rechargeLog = new UserWalletRechargeLogs();
        $rechargeLog->user_id = $user->id;
        $rechargeLog->amount = $request->amount;
        $rechargeLog->gateway = $request->gateway;
        $rechargeLog->transaction_id = $request->transaction_id;
        $rechargeLog->transaction_summary = $request->transaction_summary;
        $rechargeLog->save();

        return GlobalFunction::sendSimpleResponse(true, 'Topped up!');
    }
}
