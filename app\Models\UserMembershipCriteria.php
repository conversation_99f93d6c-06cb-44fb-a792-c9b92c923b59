<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserMembershipCriteria extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_membership_id',
        'criteria',
        'goal',
        'count',
        'cleared'
    ];

    public function userMembership()
    {
        return $this->belongsTo(UserMembership::class, 'user_membership_id', 'id');
    }
}
