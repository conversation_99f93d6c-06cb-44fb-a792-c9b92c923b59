
<?php $__env->startSection('content'); ?>
    <span class="d-none" id="current-menu" data-menu="menu-redemption-vouchers"></span>
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="flex-grow-1">
                <?php echo e(__('Redemption Vouchers')); ?>

                <div class="text-muted"><small><?php echo e(__('Search by name')); ?></small></div>
            </h4>

            <div>
                <?php if(checkPermission('create_redemption_voucher')): ?>
                    <a href="<?php echo e(route('redemptionVouchersForm')); ?>" class="ml-md-auto my-1 btn btn-primary text-white"><?php echo e(__('Add Voucher')); ?></a>
                <?php endif; ?>
                <?php if(checkPermission('export_redemption_voucher')): ?>
                    <a href="" data-url="<?php echo e(route('redemptionVouchersExport')); ?>" id="<?php echo e($moduleID); ?>Export" class="my-1 ml-1 btn btn-secondary text-white"><?php echo e(__('Export')); ?></a>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body table-id" id="<?php echo e($moduleID); ?>List" data-module="<?php echo e($moduleID); ?>">
            <div class="form-row text-left justify-content-end mb-2">
                <?php if(checkFeatureControl('filter', 'brand')): ?>
                <div class="col-md-3">
                    <select name="fbrand" class="form-control select2 filter-form" data-width="100%">
                        <option value=""><?php echo e(__('All Brand')); ?></option>
                        <?php $__currentLoopData = userBrandList(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>"><?php echo e($item); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <div class="col-md-3 mb-2">
                    <input type="text" name="fpoint_from" placeholder="<?php echo e(__('Point From')); ?>" class="form-control filter-form">
                </div>
                <div class="col-md-3 mb-2">
                    <input type="text" name="fpoint_to" placeholder="<?php echo e(__('Point To')); ?>" class="form-control filter-form">
                </div>
            </div>
            <div class="form-row text-left justify-content-end mb-2">
                <div class="col-md-3 mb-2">
                    <input type="text" name="favailable_date" value="" placeholder="<?php echo e(__('Voucher\'s Available Date')); ?>" class="form-control daterange filter-form">
                </div>
                <div class="col-md-3 mb-2">
                    <input type="text" name="fredemption_date" value="" placeholder="<?php echo e(__('Redemption Date')); ?>" class="form-control daterange filter-form">
                </div>
                <div class="col-md-3">
                    <select name="fbirthday" class="form-control select2 filter-form" data-width="100%">
                        <option value="all"><?php echo e(__('All')); ?></option>
                        <option value="1"><?php echo e(__('Birthday')); ?></option>
                        <option value="0"><?php echo e(__('Not Birthday')); ?></option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="fstatus" class="form-control select2 filter-form" data-width="100%">
                        <option value="all"><?php echo e(__('All Statuses')); ?></option>
                        <option value="1"><?php echo e(__('Enabled')); ?></option>
                        <option value="0"><?php echo e(__('Disabled')); ?></option>
                    </select>
                </div>
            </div>

            <?php if($bulk_action): ?>
                <?php echo $__env->make('include.selected-box', [ 
                    'bulk_action' => $bulk_action,
                    'datatable_list' => $moduleID.'List'
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
            <div class="table-responsive">
                <table class="table table-striped word-wrap" data-url="<?php echo e(route('redemptionVouchersList')); ?>" data-delete="<?php echo e(route('redemptionVouchersDelete')); ?>" data-status="<?php echo e(route('redemptionVouchersStatusUpdate')); ?>" data-featured="<?php echo e(route('redemptionVouchersFeaturedUpdate')); ?>">
                    <thead>
                        <tr>
                            <?php if($bulk_action): ?>
                            <th style="min-width:40px">
                                <label class="custom-checkbox">
                                    <input type="checkbox" value="1" class="form-check-input checkall" data-datatable="<?php echo e($moduleID); ?>List">
                                    <span></span>
                                </label>
                            </th>
                            <?php endif; ?>
                            <th style="min-width:150px" data-column="name" class="sortable"><?php echo e(__('Name')); ?></th>
                            <th style="min-width:150px" data-column="redeem_points" class="sortable text-center"><?php echo e(__('Discount Type')); ?></th>
                            <th style="min-width:150px" data-column="redeem_points" class="sortable text-center"><?php echo e(__('Discount')); ?></th>
                            <!-- <th style="min-width:150px" data-column="brand" class="sortable"><?php echo e(__('Brand')); ?></th>
                            <th style="min-width:150px" data-column="type" class="sortable"><?php echo e(__('Type')); ?></th> -->
                            <th style="min-width:150px" data-column="redeem_points" class="sortable text-center"><?php echo e(__('Redeem Points')); ?></th>
                            <th style="min-width:120px" class="text-center"><?php echo e(__('Max. Vouchers')); ?></th>
                            <th style="min-width:130px" data-column="redeemed_quantity" class="sortable text-center"><?php echo e(__('Redeemed Vouchers')); ?></th>
                            <!-- <th style="min-width:120px" class="text-center"><?php echo e(__('Used Vouchers')); ?></th> -->
                            <th style="min-width:170px" data-column="available_start_date" class="sortable text-center"><?php echo e(__('Voucher\'s Available Date')); ?></th>
                            <th style="min-width:170px" data-column="redemption_start_date" class="sortable text-center"><?php echo e(__('Redemption Date')); ?></th>
                            <th style="min-width:100px" data-column="first_time_purchase" class="sortable text-center"><?php echo e(__('Birthday Applicable')); ?></th>
                            <th style="min-width:100px" data-column="is_featured" class="text-center sortable"><?php echo e(__('Featured')); ?></th>
                            <th style="min-width:100px" data-column="is_active" class="text-center sortable"><?php echo e(__('Enabled')); ?></th>
                            <th style="min-width:120px" data-column="created_at" class="sortable"><?php echo e(__('Created On')); ?></th>
                            <?php if(checkPermission('edit_redemption_voucher') || checkPermission('delete_redemption_voucher')): ?>
                            <th style="min-width:100px" class="text-center"><?php echo e(__('Action')); ?></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <input type="hidden" name="sort_col" id="sort_col" value="<?php echo e(request()->sort_col); ?>">
            <input type="hidden" name="sort_by" id="sort_by" value="<?php echo e(request()->sort_by); ?>">
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('include.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/redemption-vouchers.blade.php ENDPATH**/ ?>