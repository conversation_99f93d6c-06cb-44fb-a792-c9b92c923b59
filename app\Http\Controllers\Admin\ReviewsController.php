<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Outlets;
use App\Models\Constants;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\BookingRateReviews;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Admin\DoctorController;

class ReviewsController extends Controller
{
    function index()
    {
        if (!checkPermission('view_review')):
            return redirect(route('index'))->with('error', __('You do not have permission to view review.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete'),
                'url' => route('reviewsDelete')
            ]
        ];

        $doctor_list = (new DoctorController)->doctorsDropdownList('All Stringers');
        $doctor_list = $doctor_list->getData()->data;

        $user_list = (new UsersController)->usersDropdownList('All Members');
        $user_list = $user_list->getData()->data;

        $outlet_list = Outlets::where('is_active', 1)->get();

        $moduleID = 'reviews';

        $rating_list = [
            '1' => 1,
            '2' => 2,
            '3' => 3,
            '4' => 4,
            '5' => 5
        ];

        $bulk_action = [
            'delete' => [
                'text' => __('Delete'),
                'url' => route('reviewsDelete')
            ]
        ];

        if(!checkPermission('delete_review')):
            unset($bulk_action['delete']);
        endif;

        return view('reviews', compact('doctor_list', 'user_list', 'rating_list', 'moduleID', 'outlet_list','bulk_action'));
    }

    function listing(Request $request)
    {
        $result = BookingRateReviews::orderBy('id', 'DESC');
        if ($request->input('search.value')):
            $search = $request->input('search.value');
            $result = $result->whereHas('booking', function ($q) use ($search) {
                $q->where('ref_no', 'LIKE', "%{$search}%");
            });
        endif;
        if ($request->date):
            $date_exp = explode(" to ", $request->date);
            $result = $result->whereDate('created_at', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                ->whereDate('created_at', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
        endif;
        if ($request->doctor):
            $result = $result->where('staff_id', $request->doctor);
        endif;

        if ($request->outlet):
            $result = $result->where('outlet_id', $request->outlet);
        endif;

        if ($request->user):
            $result = $result->where('user_id', $request->user);
        endif;

        if ($request->rating):
            $result = $result->where('rate', $request->rating);
        endif;
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $count => $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="' . (!$request->doctorPage ? "reviewsList" : "reviews-box") . '">
                    <span></span>
                </label>';

                $ratingBar = '';
                for ($i = 0; $i < 5; $i++):
                    if ($item->rate > $i):
                        $ratingBar .= '<i class="fas fa-star rate-active"></i>';
                    else:
                        $ratingBar .= '<i class="fas fa-star rate-disabled"></i>';
                    endif;
                endfor;

                $ratingBar .= '<br> <a href="" class="btn-icon btn-info edit" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Review') . '"><i class="fa fa-comment"></i></a>';

                $date = "<div class='text-center'>" . Carbon::parse($item->created_at)->format('d-m-Y') . '<br>' . Carbon::parse($item->created_at)->format('g:i a') . "</div>";
                $booking = "<div >" . $item->booking->ref_no . '<br>' . Carbon::parse($item->booking_date)->format('d-m-Y') . "</div>";
                $user = "<div>" . $item->user->first_name . ' ' . $item->user->last_name . '<br>' . $item->user->email_address . "</div>";;
                $staff = $item->staff->first_name . ' ' . $item->staff->last_name;

                if (checkPermission('view_doctor')):
                    $staff .='<div><a href="' . route('doctorsProfile', $item->staff_id) . '"><span class="btn btn-sm btn-info font-11 mt-1">' . __('View') . '</span></a></div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">
                    <a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>
                </div>';


                $param = [
                    $checkbox,
                    $date,
                    $booking,
                    $item->outlet->name,
                    $user,
                    $staff,
                    $ratingBar,
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('delete_review')):
                    unset($param[8]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function reviewUpdate(Request $request)
    {
        $item = BookingRateReviews::find($request->id);
        $item->review = $request->review;
        $item->rate = $request->rate;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function detail(Request $request)
    {
        $data = BookingRateReviews::find($request->id);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function delete(Request $request)
    {
        if ($request->id):
            BookingRateReviews::find($request->id)->delete();
        else:
            BookingRateReviews::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }
}
