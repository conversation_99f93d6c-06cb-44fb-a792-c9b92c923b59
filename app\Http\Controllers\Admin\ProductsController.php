<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Admin;
use App\Models\Taxes;
use App\Models\Users;
use App\Models\Products;
use App\Models\Attributes;
use App\Models\PriceGroups;
use Illuminate\Http\Request;
use App\Models\ProductPrices;
use App\Traits\ProductTraits;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\ProductCategory;
use App\Models\RetailerProduct;
use App\Models\ProductAttributes;
use App\Models\ProductVariations;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Admin\MediaController;
use App\Models\ProductSubcategory;

class ProductsController extends Controller
{
    use ProductTraits;

    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function index()
    {
        if(!checkPermission('view_product')):
            return redirect(route('index'))->with('error', __('You do not have permission to view product.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('productsDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('productsExport'),
                'filename' => 'products'
            ]
        ];

        if(!checkPermission('delete_product')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_product')):
            unset($bulk_action['export']);
        endif;

        $price_group = PriceGroups::where('type', 'product')
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $productCategories = ProductCategory::where('is_active', true)->get();

        $moduleID = 'products';
        return view('products', compact('bulk_action', 'moduleID', 'price_group', 'productCategories'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Products::when($request->input('search.value'), function ($query) use ($request) {
            $search = $request->input('search.value');
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%$search%")
                    ->orWhere('sku', 'LIKE', "%$search%");
            });
        })
            // ->when($this->brandSelector, function($query) {
            //     $query->whereIn('brand',userBrand());
            // })
            // ->when($this->brandSelector && isset($request->brand), function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            // ->when($request->country, function($query) use ($request) {
            //     $query->whereJsonContains('country', $request->country);
            // })
            // ->when($request->pricegroup, function($query) use ($request) {
            //     $query->where('price_group', $request->pricegroup);
            // })
            // ->when(isset($request->variant) && $request->variant != 'all', function($query) use ($request) {
            //     $query->where('is_variant', $request->variant);
            // })
            // ->when(isset($request->saleable) && $request->saleable != 'all', function($query) use ($request) {
            //     $query->where('is_saleable', $request->saleable);
            // })
            ->when(isset($request->productcategory) && $request->productcategory != 'all', function($query) use ($request) {
                $query->where('product_category_id', $request->productcategory);
            })
            ->when(isset($request->productsubcategory) && $request->productsubcategory != 'all', function($query) use ($request) {
                $query->where('product_subcategory_id', $request->productsubcategory);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="productsList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_product')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $name = $item->name;
                // if(!$item->is_saleable):
                //     $name .= '<br><span class="text-danger">'.__('Not Saleable').'</span>';
                // endif;
                // if($item->is_variant):
                //     $name .= '<br><span class="text-info">'.__('Is Variant').'</span>';
                // endif;

                $outOfStock = '<div class="text-center">'.($item->is_out_of_stock == 1 ? __('Yes') : __('No')).'</div>';

                $action = '<div class="action-buttons-container">';
                if(checkPermission('edit_product')):
                    $action .= '<a href="'.route('productsForm', ['id' => $item->id]).'" class="btn-icon btn-info" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('create_product')):
                    $action .= '<a href="' . route('productsDuplicate', ['id' => $item->id]) . '" class="btn-icon btn-warning" data-toggle="tooltip" data-title="' . __('Duplicate') . '"><i class="fa fa-copy"></i></a>';
                endif;
                if (checkPermission('delete_product')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                // $country = $item->country ? implode(', ', array_map(function($countryItem) {
                //     return config('staticdata.country.' . $countryItem);
                // }, json_decode($item->country))) : '';

                $param = [
                    $checkbox,
                    $name,
                    $item->sku,
                    $item->productCategory ? $item->productCategory->name : '-',
                    $item->productSubcategory ? $item->productSubcategory->name : '-',
                    // config('staticdata.brand.' . $item->brand),
                    // $country,
                    // $item->priceGroup->name ?? '-',
                    $outOfStock,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_product') && !checkPermission('delete_product')):
                    unset($param[8]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function form(Request $request)
    {
        $id = $request->id ?? null;
        $duplicateId = $request->duplicate ?? null;
        $tab = $request->tab ?? 'details';
        $product_category = null;
        $product_subcategory = null;

        $title = __('Add Product');
        $result = new Products();
        $result->is_active = 1;
        $result->is_variant = 0;
        $result->is_saleable = 1;

        if ($duplicateId):

            $title = __('Duplicate Product');
            $originalProduct = Products::with(['productAttributes', 'productPrices', 'variations'])->find($duplicateId);
            if (!$originalProduct):
                return redirect(route('products'))->with('error', __('Original product not found!'));
            endif;

            $result = $originalProduct->replicate();
            $result->name = $originalProduct->name . ' (Copy)';
            $result->sku = $this->generateUniqueSku($originalProduct->sku);
            $result->id = null;

            $product_category = optional($originalProduct->productCategory)->id ?? null;
            $product_subcategory = optional($originalProduct->productSubcategory)->id ?? null;

            $result->country = json_decode($originalProduct->country);
            $result->manual_postcode = json_decode($originalProduct->manual_postcode);

            $result->_duplicate_source_id = $duplicateId;
            $result->_is_duplicate_mode = true;

        elseif ($id):
            $title = __('Edit Product');
            $result = Products::find($id);
            $product_category = optional($result->productCategory)->id ?? null;
            $product_subcategory = optional($result->productSubcategory)->id ?? null;
            if(!$result):
                return redirect(route('products'))->with('error', __('Product not found!'));
            endif;

            $result->country = json_decode($result->country);
            $result->manual_postcode = json_decode($result->manual_postcode);
        endif;

        $tab_list = [
            'details' => __('Details'),
            'customer-pricing' => __('Customer Pricing'),
            'retailer-pricing' => __('Retailer Pricing'),
        ];
        if($result && $result->is_variant == 1):
            $tab_list['variations'] = __('Variations');
        endif;

        $data = [
            'title' => $title,
            'result' => $result,
            'id' => $id,
            'tab_list' => $tab_list,
            'tab' => $tab,
            'product_category' => $product_category,
            'product_subcategory' => $product_subcategory
        ];

        if($tab == 'details'):
            $result->files = $result->getMedia('product');
            $price_groups = PriceGroups::where('is_active', 1)
                ->where('type', 'product')
                ->orderBy('name')
                ->get();
            $options = Attributes::where('is_active', 1)
                ->where('option_id', 0)
                ->orderBy('sequence')
                ->orderBy('name')
                ->get();
            $product_categories = ProductCategory::where('is_active', true)->get();
            if($result->is_variant == 1):
                $attributes = ProductAttributes::where('product_id', $id)->get();
                $attributes->each(function($item) {
                    $item->values = json_decode($item->values);
                });
            endif;

            $data['price_groups'] = $price_groups;
            $data['options'] = $options;
            $data['product_categories'] = $product_categories;
            $data['attributes'] = $attributes ?? [];
        endif;

        if(in_array($tab, ['customer-pricing', 'variations'])):
            $tier = MembershipTiers::where('is_active', 1)
                ->orderBy('sequence')
                ->orderBy('name')
                ->get();

            $price = ProductPrices::where('product_id', $id)
                ->when($tab == 'variations', function($query) {
                    $query->where('variation_id', '>', 0);
                }, function($query) {
                    $query->where(function($q) {
                        $q->where('variation_id', 0)
                            ->orWhereNull('variation_id');
                    });
                })
                ->get()
                ->keyBy('tier_id');

            $price->each(function($item) {
                if($item->discount_start && $item->discount_end) {
                    $item->discount_period = Carbon::parse($item->discount_start)->format('d-m-Y, g:ia').' to '.Carbon::parse($item->discount_end)->format('d-m-Y, g:ia');
                }
            });

            $data['tier'] = $tier;
            $data['price'] = $price;
        endif;

        if(in_array($tab, ['customer-pricing', 'retailer-pricing', 'variations'])):
            $taxes = Taxes::where('is_active', 1)
                ->orderBy('name')
                ->get();
            $data['taxes'] = $taxes;
        endif;

        if($tab == 'variations'):
            $variations = ProductVariations::where('product_id', $id)
                ->orderBy('sequence')
                ->get();
            $variations->each(function($item) use($result) {
                // pricing
                $item->price = ProductPrices::where('product_id', $item->product_id)
                    ->where('variation_id', $item->id)
                    ->get()
                    ->keyBy('tier_id')
                    ->each(function($price_item) {
                        if($price_item->discount_start && $price_item->discount_end) {
                            $price_item->discount_period = Carbon::parse($price_item->discount_start)->format('d-m-Y, g:ia').' to '.Carbon::parse($price_item->discount_end)->format('d-m-Y, g:ia');
                        }
                    });
                // END pricing

                $item->image = $item->image_url;
            });

            $data['variations'] = $variations;
        endif;

        return view('product-form', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $duplicateSourceId = $request->duplicate_source_id ?? null;
        $tab = $request->tab;

        if ($duplicateSourceId && !$id) {
            $msg = __('Product created successfully!');
        } else {
            $msg = ($id ? __('Updated!') : __('Added!'));
        }

        switch($tab):
            case 'details': $this->addUpdateDetails($request); break;
            case 'customer-pricing': $this->addUpdateCustomerPricing($request); break;
            case 'retailer-pricing': $this->addUpdateRetailerPricing($request); break;
            case 'variations': $this->addUpdateVariations($request); break;
        endswitch;

        return redirect(route('products'))->with('message', $msg);
    }

    function addUpdateDetails($request)
    {
        $id = $request->id ?? null;
        $duplicateSourceId = $request->duplicate_source_id ?? null;
        $manual_postcode = $request->manual_postcode;
        $postcodes = array_filter($manual_postcode, function ($key) use ($manual_postcode) {
            return $key !== 'new_row' && !empty($manual_postcode[$key]);
        }, ARRAY_FILTER_USE_KEY);

        if($id):
            $query = Products::find($id);
        else:
            $query = new Products();
        endif;

        $query->name = $request->name;
        $query->is_variant = $request->is_variant ?? 0;
        $query->sku = $request->sku;
        $query->price_group = $request->price_group;
        $query->brand = $request->brand;
        $query->country = json_encode($request->country);
        $query->manual_postcode = json_encode(array_values($postcodes));
        $query->short_desc = $request->short_desc;
        $query->desc = $request->desc;
        $query->is_saleable = $request->is_saleable ?? 0;
        $query->is_active = $request->is_active ?? 0;
        $query->weight = $request->weight ?? null;
        $query->is_out_of_stock = $request->is_out_of_stock ?? 0;
        $query->product_category_id = $request->product_category_id;
        $query->product_subcategory_id = $request->product_subcategory_id;

        if (isset($request->price)) {
            $query->price = $request->price;
        }

        if (isset($request->url)) {
            $query->url = $request->url;
        }

        if($request->is_variant == 1 && !$id):
            $query->variant_same_as_parent = 1;
        endif;
        $query->save();
        $id = $query->id;

        // attributes
        $option_list = [];
        if($request->is_variant == 1):
            $attribute_list = [];
            $option_list = $request->option_id;
            unset($option_list['new_row']);
            foreach($option_list as $key => $option_id):
                foreach($request->attribute_values[$key] as $value):
                    $attribute_list[$option_id][] = $option_id.'_'.$value;
                endforeach;

                ProductAttributes::updateOrCreate(
                    [
                        'product_id' => $id,
                        'option_id' => $option_id,
                    ],
                    [
                        'product_id' => $id,
                        'option_id' => $option_id,
                        'values' => json_encode($request->attribute_values[$key]),
                    ]
                );
            endforeach;

            $attribute_list = array_values($attribute_list);
            $variation_list = [];
            if($attribute_list):
                if(sizeof($attribute_list) > 1):
                    $combination = attributeCombination($attribute_list);
                else:
                    $combination = $attribute_list[0];
                endif;

                foreach($combination as $set):
                    $variations = [];
                    if(sizeof($attribute_list) > 1):
                        sort($set);
                        foreach($set as $value):
                            $variations[] = $value;
                        endforeach;
                    else:
                        $variations[] = $set;
                    endif;

                    $variation_list[] = json_encode($variations);
                    $variation = ProductVariations::updateOrCreate(
                        [
                            'product_id' => $id,
                            'attribute_id' => json_encode($variations),
                        ],
                        [
                            'product_id' => $id,
                            'attribute_id' => json_encode($variations),
                        ]
                    );

                    if($variation->wasRecentlyCreated):
                        $variation->quantity = 100;
                        $variation->sku = $request->sku;
                        $variation->save();
                    endif;
                endforeach;

                ProductVariations::where('product_id', $id)
                    ->whereNotIn('attribute_id', $variation_list)
                    ->delete();
            endif;
        endif;

        ProductAttributes::where('product_id', $id)
            ->whereNotIn('option_id', $option_list)
            ->delete();
        // END attributes

        // images
        $media = new MediaController();
        $media->mediaUpload($query, 'product', $request);
        // END images

        // Handle duplication if this is a duplicate operation
        if ($duplicateSourceId && !$request->id):
            $this->duplicateRelatedData($duplicateSourceId, $id);
        endif;

        // update variant sku to latest if same as parent
        if($query->variant_same_as_parent == 1):
            ProductVariations::where('product_id', $id)
                ->update(['sku' => $query->sku]);
        endif;
        // END update variant sku to latest if same as parent
    }

    function addUpdateCustomerPricing($request)
    {
        $id = $request->id;
        $tier_id = $request->tier_id;
        $tier_price = [];
        foreach($tier_id as $item):
            $query = ProductPrices::where('product_id', $id)
                ->where('tier_id', $item)
                ->where(function($q) {
                    $q->where('variation_id', 0)
                        ->orWhereNull('variation_id');
                })
                ->first();
            if(!$query):
                $query = new ProductPrices();
                $query->product_id = $id;
                $query->tier_id = $item;
            endif;

            $discount_type = $request->discount_type[$item];
            if($discount_type == 'fixed'):
                $discount_value_my = $request->discount_value_my[$item];
                $discount_value_sg = $request->discount_value_sg[$item] ?? 0;
            else:
                $discount_value_my = $request->discount_value[$item];
                $discount_value_sg = $request->discount_value[$item] ?? 0;
            endif;

            $discount_period = $request->discount_period[$item];
            $discount_start_date = null;
            $discount_end_date = null;
            if($discount_period):
                $discount_period = explode(" to ", $discount_period);
                $discount_start_date = Carbon::parse(current($discount_period))->format('Y-m-d H:i:s');
                $discount_end_date = Carbon::parse(end($discount_period))->format('Y-m-d H:i:s');
            endif;

            $query->price_my = $request->price_my[$item] ?? 0;
            $query->price_sg = $request->price_sg[$item] ?? 0;
            $query->discount_type = $request->discount_type[$item];
            $query->discount_value_my = $discount_value_my;
            $query->discount_value_sg = $discount_value_sg;
            $query->discount_start = $discount_start_date ?? null;
            $query->discount_end = $discount_end_date ?? null;
            $query->tax_id = $request->tax_id[$item];
            $query->save();

            $tier_price[$query->tier_id] = $query;
        endforeach;

        // update variant price to latest if same as parent
        $product = Products::find($id);
        if($product->variant_same_as_parent == 1):
            $variations = $product->variations->where('product_id', $id);
            $variations->each(function($item) use($product, $tier_price) {
                foreach($tier_price as $tier_id => $tier_item):
                    ProductPrices::updateOrCreate(
                        [
                            'product_id' => $item->product_id,
                            'tier_id' => $tier_id,
                            'variation_id' => $item->id,
                        ],
                        [
                            'price_my' => $tier_item->price_my,
                            'price_sg' => $tier_item->price_sg,
                            'discount_type' => $tier_item->discount_type,
                            'discount_value_my' => $tier_item->discount_value_my,
                            'discount_value_sg' => $tier_item->discount_value_sg,
                            'discount_start' => $tier_item->discount_start,
                            'discount_end' => $tier_item->discount_end,
                            'tax_id' => $tier_item->tax_id,
                        ]
                    );
                endforeach;
            });
        endif;
        // END variant price to latest if same as parent
    }

    function addUpdateRetailerPricing($request)
    {
        $id = $request->id;

        $query = Products::find($id);
        $query->retailer_price_my = $request->retailer_price_my;
        $query->retailer_price_sg = $request->retailer_price_sg;
        $query->retailer_price_group = $request->retailer_price_group;
        $query->retailer_tax_id = $request->retailer_tax_id;
        $query->save();

        // update variant price to latest if same as parent
        if($query->variant_same_as_parent == 1):
            $variations = ProductVariations::where('product_id', $id)
                ->orderBy('sequence')
                ->get();
            $variations->each(function($item) use($query) {
                $item->sku = $query->sku;
                $item->retailer_price_my = $query->retailer_price_my;
                $item->retailer_price_sg = $query->retailer_price_sg;
                $item->save();
            });
        endif;
        // END variant price to latest if same as parent
    }

    function addUpdateVariations($request)
    {
        $id = $request->id;

        $variant_same_as_parent = $request->variant_same_as_parent ?? 0;
        if(!$variant_same_as_parent):
            $variation_id = $request->variation_id;
            foreach($variation_id as $item):
                $query = ProductVariations::find($item);
                $query->sku = $request->sku[$item];
                $query->retailer_price_my = $request->retailer_price_my[$item];
                $query->retailer_price_sg = $request->retailer_price_sg[$item];
                $query->is_active = $request->is_active[$item] ?? 0;
                $query->sequence = $request->sequence[$item] ?? 0;
                if(isset($request->image[$item])):
                    $query->image = GlobalFunction::saveFileAndGivePath($request->image[$item]);
                endif;
                $query->save();

                $tier = MembershipTiers::where('is_active', 1)
                    ->orderBy('sequence')
                    ->orderBy('name')
                    ->get();
                $tier->each(function($tier_item) use($item, $request, $id) {
                    $tier_id = $tier_item->id;
                    $discount_type = $request->discount_type[$item][$tier_id];
                    if($discount_type == 'fixed'):
                        $discount_value_my = $request->discount_value_my[$item][$tier_id] ?? 0;
                        $discount_value_sg = $request->discount_value_sg[$item][$tier_id] ?? 0;
                    else:
                        $discount_value_my = $request->discount_value[$item][$tier_id] ?? 0;
                        $discount_value_sg = $request->discount_value[$item][$tier_id] ?? 0;
                    endif;

                    $discount_period = $request->discount_period[$item][$tier_id];
                    $discount_start_date = null;
                    $discount_end_date = null;
                    if($discount_period):
                        $discount_period = explode(" to ", $discount_period);
                        $discount_start_date = Carbon::parse(current($discount_period))->format('Y-m-d H:i:s');
                        $discount_end_date = Carbon::parse(end($discount_period))->format('Y-m-d H:i:s');
                    endif;

                    ProductPrices::updateOrCreate(
                        [
                            'product_id' => $id,
                            'tier_id' => $tier_id,
                            'variation_id' => $item,
                        ],
                        [
                            'product_id' => $id,
                            'tier_id' => $tier_id,
                            'variation_id' => $item,
                            'price_my' => $request->price_my[$item][$tier_id] ?? 0,
                            'price_sg' => $request->price_sg[$item][$tier_id] ?? 0,
                            'discount_type' => $request->discount_type[$item][$tier_id],
                            'discount_value_my' => $discount_value_my,
                            'discount_value_sg' => $discount_value_sg,
                            'discount_start' => $discount_start_date,
                            'discount_end' => $discount_end_date,
                            'tax_id' => $request->tax_id[$item][$tier_id],
                        ]
                    );
                });
            endforeach;
        else:
            // get sku & price from parent
            $product = Products::find($id);

            if($product->variant_same_as_parent == 1):
                $tier_price = ProductPrices::where('product_id', $id)
                    ->where('variation_id', 0)
                    ->get()
                    ->keyBy('tier_id');
                $variations = $product->variations->where('product_id', $id);
                $variations->each(function($item) use($product, $tier_price) {
                    $item->sku = $product->sku;
                    $item->retailer_price_my = $product->retailer_price_my;
                    $item->retailer_price_sg = $product->retailer_price_sg;
                    $item->save();

                    foreach($tier_price as $tier_id => $tier_item):
                        ProductPrices::updateOrCreate(
                            [
                                'product_id' => $product->product_id,
                                'tier_id' => $tier_id,
                                'variation_id' => $item->id,
                            ],
                            [
                                'price_my' => $tier_item->price_my,
                                'price_sg' => $tier_item->price_sg,
                                'discount_type' => $tier_item->discount_type,
                                'discount_value_my' => $tier_item->discount_value_my,
                                'discount_value_sg' => $tier_item->discount_value_sg,
                                'discount_start' => $tier_item->discount_start,
                                'discount_end' => $tier_item->discount_end,
                                'tax_id' => $tier_item->tax_id,
                            ]
                        );
                    endforeach;
                });
            endif;
        endif;

        Products::where('id', $id)
            ->update([
                'variant_same_as_parent' => $variant_same_as_parent,
            ]);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = Products::find($request->id);
            $selected_id = [$request->id];
        else:
            $query = Products::whereIn('id', $request->selected);
            $selected_id = $request->selected;
        endif;

        ProductAttributes::whereIn('product_id', $selected_id)->delete();
        ProductPrices::whereIn('product_id', $selected_id)->delete();
        ProductVariations::whereIn('product_id', $selected_id)->delete();

        $query = $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Products::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function duplicate(Request $request)
    {
        $id = $request->id;
        $originalProduct = Products::with(['productAttributes', 'productPrices', 'variations'])->find($id);

        if (!$originalProduct) {
            return redirect(route('products'))->with('error', __('Product not found!'));
        }

        return redirect(route('productsForm', ['duplicate' => $id]))->with('message', __('Product duplicated successfully.'));
    }

    private function generateUniqueSku($originalSku)
    {
        $baseSku = $originalSku . '-COPY';
        $counter = 1;
        $newSku = $baseSku;

        while (Products::where('sku', $newSku)->exists()) {
            $newSku = $baseSku . '-' . $counter;
            $counter++;
        }

        return $newSku;
    }

    private function duplicateRelatedData($sourceProductId, $newProductId)
    {
        $originalProduct = Products::with(['productAttributes', 'productPrices', 'variations', 'bundles.discount_packages', 'bundles.gwp', 'bundles.pwp'])->find($sourceProductId);

        if (!$originalProduct) {
            return;
        }

        // Duplicate product attributes
        foreach ($originalProduct->productAttributes as $attribute) {
            $newAttribute = $attribute->replicate();
            $newAttribute->product_id = $newProductId;
            $newAttribute->save();
        }

        // Duplicate product prices
        foreach ($originalProduct->productPrices as $price) {
            $newPrice = $price->replicate();
            $newPrice->product_id = $newProductId;
            $newPrice->save();
        }

        // Duplicate product variations
        foreach ($originalProduct->variations as $variation) {
            $newVariation = $variation->replicate();
            $newVariation->product_id = $newProductId;
            // Keep the original is_active value from the source variation
            $newVariation->save();
        }

        // Duplicate product bundles
        foreach ($originalProduct->bundles as $bundle) {
            $newBundle = $bundle->replicate();
            $newBundle->product_id = $newProductId;
            // Keep the original is_active value from the source bundle
            $newBundle->save();

            // Duplicate bundle discount packages
            foreach ($bundle->discount_packages as $discount) {
                $newDiscount = $discount->replicate();
                $newDiscount->product_bundle_id = $newBundle->id;
                $newDiscount->save();
            }

            // Duplicate bundle gifts (GWP - Gift With Purchase)
            foreach ($bundle->gwp as $gift) {
                $newGift = $gift->replicate();
                $newGift->product_bundle_id = $newBundle->id;
                $newGift->save();
            }

            // Duplicate bundle purchases (PWP - Purchase With Purchase)
            foreach ($bundle->pwp as $purchase) {
                $newPurchase = $purchase->replicate();
                $newPurchase->product_bundle_id = $newBundle->id;
                $newPurchase->save();
            }
        }
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Products::when($request->input('search.value'), function ($query) use ($request) {
            $search = $request->input('search.value');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%$search%")
                    ->orWhere('sku', 'LIKE', "%$search%");
            });
        })
            // ->whereIn('brand',userBrand())
            // ->when($request->brand, function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            // ->when($request->country, function($query) use ($request) {
            //     $query->whereJsonContains('country', $request->country);
            // })
            // ->when($request->pricegroup, function($query) use ($request) {
            //     $query->where('price_group', $request->pricegroup);
            // })
            // ->when(isset($request->variant) && $request->variant != 'all', function($query) use ($request) {
            //     $query->where('is_variant', $request->variant);
            // })
            // ->when(isset($request->saleable) && $request->saleable != 'all', function($query) use ($request) {
            //     $query->where('is_saleable', $request->saleable);
            // })
            ->when(isset($request->productcategory) && $request->productcategory != 'all', function ($query) use ($request) {
                $query->where('product_category_id', $request->productcategory);
            })
            ->when(isset($request->productsubcategory) && $request->productsubcategory != 'all', function ($query) use ($request) {
                $query->where('product_subcategory_id', $request->productsubcategory);
            })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                // $item->brand = config('staticdata.brand.' . $item->brand);
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                // $item->price_group = $item->priceGroup->name ?? '-';

                // $item->country = $item->country ? implode(', ', array_map(function($countryItem) {
                //     return config('staticdata.country.' . $countryItem);
                // }, json_decode($item->country))) : '';
            endforeach;
        endif;

        $fileName = 'products_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'products'),
            $fileName
        );
    }

    function getVariationName($attribute_id) // use in models/ProductVariations.php
    {
        $attribute_list = json_decode($attribute_id);
        $attributes = collect($attribute_list)->map(function ($attribute_id) {
            list($option_id, $value_id) = explode('_', $attribute_id);
            return [
                'option_sequence' => Attributes::find($option_id)->sequence ?? null,
                'value_name' => Attributes::find($value_id)->name ?? null,
            ];
        })->sortBy('option_sequence')
            ->pluck('value_name')
            ->implode(', ');

        return $attributes;
    }

    function ajaxProduct(Request $request)
    {
        if (is_retailer()):
            return $this->ajaxProductRetailer($request);
        endif;

        $results = Products::when($request->search, function ($query) use ($request) {
            $query->where(function ($q) use ($request) {
                $q->where('name', "LIKE", '%' . $request->search . '%')
                    ->orWhere('products.sku', "LIKE", '%' . $request->search . '%');
            });
        })
            ->when($request->brand, function ($q) use ($request) {
                $q->where(function ($q) use ($request) {
                    $brands = explode(",", $request->brand);
                    if (is_array($brands)) {
                        foreach ($brands as $brand) {
                            $q->orWhere('brand', $brand);
                        }
                    } else {
                        $q->where('brand', $request->brand);
                    }
                });
            })
            ->when($request->country, function ($q) use ($request) {
                $q->whereJsonContains('country', $request->country);
            })
            ->get();

        $products = [];
        foreach ($results as $item):
            $products[] = [
                'id' => $item->id,
                'text' => $item->name,
            ];
        endforeach;

        return response()->json([
            'results' => $products,
        ]);
    }

    function ajaxVariation(Request $request)
    {
        $product_id = $request->product_id;
        $first = $request->first ?? null;
        $result = Products::find($product_id);

        $html = "";
        if ($result->is_variant && $result->variations->count() > 0):
            if ($first):
                $html .= '<option value="">' . $first . '</option>';
            endif;
            foreach ($result->variations as $item):
                $html .= '<option value="' . $item->id . '">' . $item->variation_name . '</option>';
            endforeach;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function detail(Request $request)
    {
        if (is_retailer()):
            return $this->ajaxProductDetail($request);
        endif;

        $id = $request->product_id;
        $variation_id = $request->variation_id;
        $user_id = $request->user_id;
        $user_type = $request->user_type;

        // $product = Products::find($request->product_id);
        $product = Products::with([
            'productCategory',
            'productSubcategory'
        ])->find($request->product_id);

        if ($product->is_variant):
            $product->variation_id = $variation_id;
            $product->variation_name = $product->variations()->where('id', $variation_id)->first()->variation_name;
            $product->variation_sku = $product->variations()->where('id', $variation_id)->first()->sku;
        endif;

        // pricing
        // if($user_type == 'customer'):
        //     $user = Users::find($user_id);
        //     $tier = $user->membership_tier;
        //     $country = $user->country;

        //     $price_data = $this->getPriceByTier($product->id, $variation_id, $tier, $country);
        // elseif($user_type == 'retailer'):
        //     $country = $request->country;
        //     $price_data = $this->getRetailerPrice($user_id, $product->id, $variation_id, $country);
        // endif;

        // $product->price = $price_data['price'];
        // $product->tax = $price_data['tax_amount'];
        $product->tax = 0;
        // END pricing

        return response()->json([
            'results' => $product,
        ]);
    }

    // retailer product
    function ajaxProductRetailer($request)
    {
        $results = RetailerProduct::when($request->search, function ($query) use ($request) {
            $query->where(function ($q) use ($request) {
                $q->where('product_name', "LIKE", '%' . $request->search . '%')
                    ->orWhere('variation_name', "LIKE", '%' . $request->search . '%');
            });
        })
            ->when($request->brand, function ($q) use ($request) {
                $q->where('brand', $request->brand);
            })
            ->when($request->country, function ($q) use ($request) {
                $q->where('country', $request->country);
            })
            ->where('retailer_id', session('user')->user_id)
            ->get();

        $products = [];
        foreach ($results as $item):
            $name = $item->product_name;
            $id = $item->product_id;
            if ($item->variation_id):
                $name .= ' - ' . $item->variation_name;
                $id .= '_' . $item->variation_id;
            endif;
            $name .= ' (' . $item->available_stock . ' units)';

            $products[] = [
                'id' => $id,
                'text' => $name,
            ];
        endforeach;

        return response()->json([
            'results' => $products,
        ]);
    }

    function ajaxProductDetail($request)
    {
        $id = $request->product_id;
        $explode = explode('_', $id);
        $product_id = $explode[0];
        $variation_id = $explode[1] ?? null;

        $product = RetailerProduct::where('product_id', $product_id)
            ->when($variation_id, function ($q) use ($variation_id) {
                $q->where('variation_id', $variation_id);
            })
            ->where('retailer_id', session('user')->user_id)
            ->first();

        $discount_valid = true;
        if ($product->discount_start && $product->discount_start > date('Y-m-d H:i:s')):
            $discount_valid = false;
        endif;
        if ($product->discount_end && $product->discount_end < date('Y-m-d H:i:s')):
            $discount_valid = false;
        endif;

        $price = $product->selling_price;
        if ($discount_valid):
            $discount_amount = calculateDiscountAmount($price, $product->discount_type, $product->discount_value);
            $price -= $discount_amount;
        endif;
        $product->price = $price;
        $product->tax = 0;

        return response()->json([
            'results' => $product,
        ]);
    }

    function ajaxProductSubcategory(Request $request)
    {
        $data = [];

        // change subcategory
        $html = "";

        if ($request->filter == 1) {
            $html .= '<option value="">All Product Subcategory</option>';
        } else {
            $html .= '<option value="">Select Product Subcategory</option>';
        }

        if (!is_null($request->category_id)) {
            $subCategories = ProductSubcategory::where('product_category_id', $request->category_id)->where('is_active', true)->get();

            if ($subCategories->count() > 0) {
                foreach ($subCategories as $item) {
                    if ($item->id == $request->product_subcategory) {
                        $html .= '<option value="' . $item->id . '" selected>' . $item->name . '</option>';
                    } else {
                        $html .= '<option value="' . $item->id . '">' . $item->name . '</option>';
                    }
                }
            }
        }

        $data['html'] = $html;
        //end change subcategory

        // get product weight
        $productCategory = ProductCategory::find($request->category_id);
        $data['product_weight'] = $productCategory->product_weight;
        // end get product weight

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $data);
    }

    function ajaxGetProduct(Request $request)
    {
        $html = "";

        if ($request->filter == 1) {
            $html .= '<option value="">All Product</option>';
        } else {
            $html .= '<option value="">Select Product</option>';
        }

        if (!is_null($request->category_id) && !is_null($request->subcategory_id)) {
            $products = Products::where('product_category_id', $request->category_id)
                ->where('product_subcategory_id', $request->subcategory_id)
                ->where('is_active', true)
                ->get();

            if ($products->count() > 0) {
                foreach ($products as $item) {
                    $html .= '<option value="' . $item->id . '">' . $item->name . '</option>';
                }
            }
        }

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }
}
