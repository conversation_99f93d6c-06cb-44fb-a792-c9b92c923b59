<?php

namespace App\Http\Controllers\Api;

use App\Models\Carts;
use App\Models\Order;
use App\Models\Coupon;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\CartProducts;

class ApiCoupon extends Controller
{
    function apply(Request $request)
    {
        $coupon_code = $request->coupon_code;
        $amount = $request->amount;

        // check coupon exists
        $coupon = Coupon::where('code', $coupon_code)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNotNull('start_datetime')
                    ->where('start_datetime', '<=', date('Y-m-d H:i:s'))
                    ->orWhereNull('start_datetime');

                $query->whereNotNull('expiry_datetime')
                    ->where('expiry_datetime', '>=', date('Y-m-d H:i:s'))
                    ->orWhereNull('expiry_datetime');
            })
            ->first();

        if (!$coupon) {
            return GlobalFunction::sendSimpleResponse(false, 'Invalid coupon code.');
        }
        // end check coupon exists

        $cart = Carts::where('user_id', auth()->user()->id)->first();

        // check if cart exists
        if (!$cart) {
            return GlobalFunction::sendSimpleResponse(false, 'Cart not found.');
        } else {
            
            if ($cart->voucher_id == '[]' || $cart->voucher_id == null) {
            } else {
                return GlobalFunction::sendSimpleResponse(false, 'Unable to apply coupon, voucher already applied.');
            }
        }
        // end check if cart exists

        // check coupon usage
        $checkUsage = Order::where('user_id', auth()->user()->id)->where('coupon_id', $coupon->id)->count();

        if (!is_null($coupon->limit_per_user)) {
            if ($checkUsage >= $coupon->limit_per_user) {
                return GlobalFunction::sendSimpleResponse(false, 'Coupon usage limit exceeded.');
            }
        }
        // end check coupon usage

        // check minimum amount
        if (!is_null($coupon->minimum_amount)) {
            if ($amount < $coupon->minimum_amount) {
                return GlobalFunction::sendSimpleResponse(false, 'Minimum amount not met.');
            }
        }
        // end check minimum amount

        // check product ids and category ids
        $product_ids = null;
        $categery_ids = null;
        $invalidProducts = [];

        if (!is_null($coupon->product_ids)) {
            $product_ids = json_decode($coupon->product_ids, true);
        }

        if (!is_null($coupon->category_ids)) {
            $categery_ids = json_decode($coupon->category_ids, true);
        }

        $cartProducts = CartProducts::where('cart_id', $cart->id)
            ->pluck('product_id');

        if (!is_null($product_ids)) {
            $invalidProducts = $cartProducts->diff($product_ids);
        }

        if (count($invalidProducts) > 0) {
            return GlobalFunction::sendSimpleResponse(false, 'Invalid product selected.');
        }

        if (!is_null($categery_ids)) {
            $invalidProducts = CartProducts::where('cart_id', $cart->id)
                ->whereHas('product.productCategory', function ($query) use ($categery_ids) {
                    $query->whereNotIn('id', $categery_ids);
                })->pluck('product_id');

            if (count($invalidProducts) > 0) {
                return GlobalFunction::sendSimpleResponse(false, 'Invalid product selected.');
            }
        }
        // end check product ids and category ids

        $response = [
            'coupon_id' => (int) $coupon->id,
            'sub_amount' => number_format($amount, 2)
        ];

        if ($coupon->type == 'fixed') {
            $response['discount_amount'] = number_format($coupon->value, 2);
            $response['total_amount'] = number_format($amount - $coupon->value, 2);
        } else if ($coupon->type == 'percentage') {
            $response['discount_amount'] = number_format($amount * ($coupon->value / 100), 2);
            $response['total_amount'] = number_format($amount - $response['discount_amount'], 2);
        }

        $cart->coupon_id = $coupon->id;
        $cart->save();

        return GlobalFunction::sendDataResponse(true, '', $response);
    }

    function remove($coupon_id)
    {
        $cart = Carts::where('user_id', auth()->user()->id)->first();

        if ($cart && $cart->coupon_id != $coupon_id) {
            return GlobalFunction::sendSimpleResponse(false, 'Unable to remove coupon.');
        }

        $cart->coupon_id = null;
        $cart->save();

        return GlobalFunction::sendDataResponse(true, 'Removed coupon from cart', $cart);
    }
}
