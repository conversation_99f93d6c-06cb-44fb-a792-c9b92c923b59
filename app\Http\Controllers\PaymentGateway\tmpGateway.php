<?php
namespace App\Http\Controllers\PaymentGateway;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\User;
use App\Traits\PaymentGatewaysTraits;
use App\Traits\WalletsTraits;
use App\Traits\EmailTraits;

class tmpGateway extends Controller
{
    use PaymentGatewaysTraits;
    use WalletsTraits;
    use EmailTraits;

    function response($payment, $status='')
    {
        if($payment->status != Constants::paymentStatus['pending']):
            return GlobalFunction::sendSimpleResponse(false, 'This transaction has been processed before. Current status is '.$payment->status);
        endif;

        if(!$status):
            $response = $this->updatePaymentSuccess($payment);
            $msg = $response['msg'];
            $data = $response['data'];
        else:
            $this->updatePaymentFailed($payment);
            $msg = __('Payment failed.');
        endif;

        return GlobalFunction::sendDataResponse(true, $msg, $data ?? null);
    }
}