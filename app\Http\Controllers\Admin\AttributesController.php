<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Attributes;
use App\Models\Taxes;
use App\Models\GlobalFunction;
use App\Models\ProductAttributes;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class AttributesController extends Controller
{
    function index()
    {
        if(!checkPermission('view_attribute')):
            return redirect(route('index'))->with('error', __('You do not have permission to view attributes.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('attributesDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('attributesExport'),
                'filename' => 'attributes'
            ]
        ];

        if(!checkPermission('delete_attribute')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_attribute')):
            unset($bulk_action['export']);
        endif;

        $options = Attributes::where('option_id', 0)
            ->orderBy('name', 'ASC')
            ->get();
        $selected_option = request()->option ?? 0;

        $moduleID = 'attributes';
        return view('attributes', compact('bulk_action', 'options', 'selected_option', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'sequence';
        $sort_by = $request->sort_by ?? 'ASC';
        $option_id = $request->option ?? 0;
        $result = Attributes::where('option_id', $option_id)
            ->when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="attributesList">
                    <span></span>
                </label>';

                if(!checkPermission('edit_attribute')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_attribute')):
                    $action .= '<a href="" class="btn-icon btn-info mr-1 '.($item->option_id ? 'edit_value' : 'edit').'" rel="' . $item->id . '" data-toggle="tooltip" data-option="'.$item->option_id.'" data-name="' . $item->name . '" data-sequence="' . $item->sequence . '" data-status="' . $item->is_active . '" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_attribute')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    '<div class="text-center">'.$item->sequence.'</div>',
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_attribute') && !checkPermission('delete_attribute')):
                    unset($param[5]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = Attributes::find($id);
        else:
            $msg = __('Added!');
            $query = new Attributes();
        endif;

        $option_id = $request->option_id ?? 0;
        if($option_id && !$id):
            // add new value
            foreach($request->value_name as $key => $value):
                if($key != 'new_row' && $value):
                    $query = new Attributes();
                    $query->option_id = $option_id;
                    $query->name = $value;
                    $query->sequence = $request->sequence[$key] ?? 0;
                    $query->is_active = $request->status[$key] ?? 0;
                    $query->save();
                endif;
            endforeach;
        else:
            $query->option_id = $option_id;
            $query->name = $request->name;
            $query->sequence = $request->sequence ?? 0;
            $query->is_active = $request->status ?? 0;
            $query->save();
        endif;

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = Attributes::find($request->id);
            $option_id = $query->option_id;
            $selected_id = [$request->id];
        else:
            $query = Attributes::whereIn('id', $request->selected);
            $option_id = $query->first()->option_id;
            $selected_id = $request->selected;
        endif;

        // check if any product attached
        if($query->option->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This option is attached to some products.');
        endif;

        if($query->option_id):
            $values = ProductAttributes::where('option_id', $query->option_id)
                ->whereJsonContains('values', (string) $query->id)
                ->get();
            if($values->count() > 0):
                return GlobalFunction::sendSimpleResponse(false, 'This value is attached to some products.');
            endif;
        endif;
        // END check if any product attached

        if(!$option_id): // if deleting option, delete all the values
            Attributes::whereIn('option_id', $selected_id)->delete();
        endif;

        $query = Attributes::whereIn('id', $selected_id)->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Attributes::find($request->id);

        // check if any product attached
        if($item->option->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This option is attached to some products.');
        endif;

        if($item->option_id):
            $values = ProductAttributes::where('option_id', $item->option_id)
                ->whereJsonContains('values', (string) $item->id)
                ->get();
            if($values->count() > 0):
                return GlobalFunction::sendSimpleResponse(false, 'This value is attached to some products.');
            endif;
        endif;
        // END check if any product attached
        
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'sequence';
        $sort_by = $request->sort_by ?? 'ASC';
        $option_id = $request->option ?? 0;
        $result = Attributes::where('option_id', $option_id)
            ->when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($option_id):
            $option_name = Attributes::find($option_id)->name;
        endif;
        if($result->count() > 0):
            foreach($result as $item):
                $item->option_name = $option_name ?? '';
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;
        
        $fileName = 'attributes_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'attributes'),
            $fileName
        );
    }

    function ajaxOption($first='')
    {
        $result = Attributes::where('option_id', 0)
            ->when(request()->search, function($q) {
                $search = request()->search;
                $q->where('name', 'LIKE', "%{$search}%");
            })
            ->orderBy('name', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->name.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }

    function ajaxValue($first='')
    {
        $result = Attributes::where('option_id', request()->option_id)
            ->when(request()->search, function($q) {
                $search = request()->search;
                $q->where('name', 'LIKE', "%{$search}%");
            })
            ->orderBy('name', 'ASC')
            ->get();

        $html = "";
        if(isset(request()->search)):
            $html = $result;
        else:
            if($result->count() > 0):
                if($first):
                    $html .= '<option value="">'.$first.'</option>';
                endif;
                foreach($result as $item):
                    $html .= '<option value="'.$item->id.'">'.$item->name.'</option>';
                endforeach;
            endif;
        endif;

        return GlobalFunction::sendDataResponse(true, "List retrieved.", $html);
    }
}