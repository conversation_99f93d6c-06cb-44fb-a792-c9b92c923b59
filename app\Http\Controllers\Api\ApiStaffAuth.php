<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Models\Doctors;
use App\Models\GlobalFunction;
use App\Traits\VerificationsTraits;

class ApiStaffAuth extends Controller
{
    use VerificationsTraits;

    function login(Request $request)
    {
        $rules = [
            'email_address' => 'required|email',
            'password' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $error = '';
        $user = Doctors::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('email_address', $request->email_address)
            ->first();

        if(!$user):
            $error = __('Account not found.');
        else:
            if(!Hash::check($request->password, $user->password)):
                $error = __('Password is not correct.');
            endif;
        endif;

        if($error):
            return GlobalFunction::sendSimpleResponse(false, $error);
        endif;

        if($request->onesignal_token):
            $token_input = $request->onesignal_token;
            $user->device_token = $token_input;
        else:
            $token_input = $user->device_token;
        endif;
        if(!$token_input):
            $token_input = $user->email_address ? $user->email_address : $user->id.$user->mobile_number;
        endif;
        $user->save();
        
        Auth::guard('staffs')->login($user);
        $user->tokens()->delete();
        $user->token = $user->createToken($token_input)->plainTextToken;
        $user = GlobalFunction::getDoctorData($user);

        return GlobalFunction::sendDataResponse(true, __('Account logged in.'), $user);
    }

    function forgotPassword(Request $request)
    {
        $rules = [ 'email_address' => 'required|email' ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $user = Doctors::where('email_address', $request->email_address)
            ->where('is_active', 1)
            ->whereNull('deleted_at')
            ->first();
        if(!$user):
            return GlobalFunction::sendSimpleResponse(false, 'Account does not exists.');
        endif;

        $email_data = [
            'name' => $user->first_name.' '.$user->last_name,
            'email' => $user->email_address,
            'type' => 'staff_forgot_password',
            'type_id' => $user->id,
        ];
        $this->sendVerificationEmail($email_data);

        $data = [
            'id' => $user->id,
            'email_address' => $user->email_address,
        ];
        return GlobalFunction::sendDataResponse(true, __('Please check your email for reset passsword.'), $data);
    }

    function resetPassword(Request $request)
    {
        $rules = [
            'email_address' => 'required|email',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $user = Doctors::where('email_address', $request->email_address)
            ->update(['password' => Hash::make($request->password)]);

        return GlobalFunction::sendSimpleResponse(true, __('Password is reset.'));
    }
}