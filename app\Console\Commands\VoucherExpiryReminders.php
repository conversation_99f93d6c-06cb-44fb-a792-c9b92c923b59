<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\UserVouchers;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\ExpiryReminderNotification;
use App\Traits\PushNotificationTraits;

class VoucherExpiryReminders extends Command
{
    use PushNotificationTraits;

    protected $signature = 'voucher_expiry_reminder';
    protected $description = 'send notification reminder before voucher expired';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $voucher_expired_reminder = global_settings('voucher_expired_reminder');

        $user_vouchers = UserVouchers::whereNull('used_date')
            ->whereDate('effective_end_date', '=', Carbon::now()->addDays($voucher_expired_reminder))
            ->get();

        $reminder_recipient = [];
        foreach($user_vouchers as $user_voucher):
            $expired_date = Carbon::parse($user_voucher->effective_end_date)->format('d M Y');
            $user = $user_voucher->user;

            Notification::route('whatsapp', $user->phone_number)
                ->notify(new ExpiryReminderNotification($user, $user_voucher->voucher->name, 'voucher',  $expired_date));

            $reminder_recipient[] = $user->id.": ".$user->phone_number." - ".$user_voucher->id;
        endforeach;

        if($reminder_recipient):
            Cronjob::create([
                "type" => "voucher expiry reminder",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}