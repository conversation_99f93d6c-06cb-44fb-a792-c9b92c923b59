<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductPrices extends Model
{
    use HasFactory;
    public $table = "product_prices";
    public $timestamp = true;

    protected $fillable = [
        'product_id',
        'variation_id',
        'tier_id',
        'price_my',
        'price_sg',
        'discount_type',
        'discount_value_my',
        'discount_value_sg',
        'discount_start',
        'discount_end',
        'tax_id',
    ];

    public function membership_tier()
    {
        return $this->belongsTo(MembershipTiers::class, 'tier_id');
    }

    public function tax()
    {
        return $this->belongsTo(Taxes::class, 'tax_id');
    }
}
