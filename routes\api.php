<?php

use API\ApiGoogle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DoctorController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PointsAndVouchersController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// for other platform call this integration like 3PL
Route::namespace('\App\Http\Controllers\Api')
    ->controller(ApiOrderIntegration::class)
    ->prefix('integration')
    ->group(function () {
        Route::post('token', 'getToken');
        Route::get('state-list', 'getStateList');
        Route::get('order-status-list', 'getOrderStatusList');
        Route::post('create-order', 'orderCreate');
        Route::post('update-order-status', 'OrderUpdateStatus');
    });
// END for other platform call this integration like 3PL

Route::post('place-search/{path}', [ApiGoogle::class, 'placeSearch']);
Route::get('geocoding/{path}', [ApiGoogle::class, 'geocodingApi'])->where('path', '.*');
Route::get('place-detail/{place_id}', [ApiGoogle::class, 'placeDetail']);

Route::group([
    'prefix' => (config('feature_control.selector.brand') ? '{brand}' : ''),
    'middleware' => ['cors'],
], function () {
    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiAuth::class)
        ->group(function () {
            Route::prefix('user')->group(function () {
                Route::post('login', 'login');
                Route::post('signup', 'signup');
                Route::post('forgot-password', 'forgotPassword');
                Route::post('reset-password', 'resetPassword');
            });

            Route::post('send-otp', 'sendOtp');
            Route::post('code-verification', 'codeVerification');
            Route::post('resend-verification-email', 'resendVerificationEmail');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiStaffAuth::class)
        ->group(function () {
            Route::prefix('staff')->group(function () {
                Route::post('login', 'login');
                Route::post('forgot-password', 'forgotPassword');
                Route::post('reset-password', 'resetPassword');
            });
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiPages::class)
        ->group(function () {
            Route::prefix('page')
                ->group(function () {
                    Route::post('/', 'view');
                    Route::post('/faqs', 'faqs');
                    Route::post('/banners', 'banners');
                });

            Route::post('/settings', 'settings');
            Route::post('/user/mobile-version', 'mobileVersions');
            Route::post('/staff/mobile-version', 'mobileVersions');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiGeneral::class)
        ->prefix('general')
        ->group(function () {
            Route::get('/states', 'states');
            Route::get('/payment-method', 'paymentMethod');
            Route::get('/setting', 'setting');
            Route::get('/country-list', 'countryList');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiWoocommerce::class)
        ->prefix('woocommerce')
        ->group(function () {
            Route::get('/get-orders', 'getOrders');
            Route::get('/create-orders', 'createOrders');
            Route::post('/webhook-update-order', 'webhookUpdateOrder');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiNewsAndAnnouncement::class)
        ->prefix('news-and-announcement')
        ->group(function () {
            Route::get('/', 'listing');
            Route::get('/{id}', 'detail');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiProducts::class)
        ->prefix('products')
        ->group(function () {
            Route::get('/', 'listing');
            Route::get('/{id}', 'detail');
            Route::post('/bundle-detail', 'bundleDetail');
        });

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiDinlr::class)
        ->prefix('dinlr')
        ->group(function () {
            Route::get('/set-refresh-token/{token}', 'setRefreshToken');
            Route::post('/oauth', 'oauthToken');
            // Route::get('/customer-groups', 'retrieveCustomerGroups');
            Route::post('/callback-order-created', 'callbackOrderCreated');
            Route::post('/create-store-credit', 'createStoreCredit');
        });

    Route::middleware('non.auth')->group(function () {
        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiRedeemableVouchers::class)
            ->prefix('redeemable-vouchers')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiProducts::class)
            ->prefix('products')
            ->group(function () {
                Route::get('/', 'listing');
                Route::get('/{id}', 'detail');
                Route::post('/bundle-detail', 'bundleDetail');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiOrders::class)
            ->prefix('order')
            ->group(function () {
                Route::get('/payment/{order_no}', 'orderPaymentDetail');
                Route::post('/apply-voucher', 'applyVoucher');
                Route::post('/remove-voucher', 'removeVoucher');
                Route::post('/apply-coupon', 'applyCoupon');
                Route::post('/remove-coupon', 'removeCoupon');
                Route::post('/pay', 'pay');
            });
    });

    Route::group(['middleware' => ['auth:sanctum']], function () {
        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiUsers::class)
            ->prefix('user')
            ->group(function () {
                Route::post('logout', 'logout');
                Route::post('delete', 'delete');
                Route::post('profile', 'profile');
                Route::post('update-profile', 'updateProfile');
                Route::post('validate-current-password', 'validateCurrentPassword');
                Route::post('update-new-password', 'updateNewPassword');
                Route::post('update-phone-otp', 'updatePhoneOtp');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiNotifications::class)
            ->prefix('user/notification')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
                Route::post('/mark-as-read', 'markAsRead');
                Route::post('/total-unread', 'totalUnread');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiOrders::class)
            ->prefix('order')
            ->group(function () {
                Route::post('/', 'listing');
                Route::get('/{id}', 'detail');
                Route::post('/create', 'create');
                Route::post('/rating', 'rating');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiWallets::class)
            ->prefix('user/wallet')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
                Route::post('/topup', 'topup');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiPoints::class)
            ->prefix('user/point')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiWishlists::class)
            ->prefix('user-wishlist')
            ->group(function () {
                Route::get('/', 'listing');
                Route::post('/add', 'add');
                Route::delete('/delete/{id}', 'delete');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiMembershipTiers::class)
            ->prefix('membership-tiers')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiStaffUsers::class)
            ->prefix('staff')
            ->group(function () {
                Route::post('logout', 'logout');
                Route::post('delete', 'delete');
                Route::post('profile', 'profile');
                Route::post('update-profile', 'updateProfile');
                Route::post('validate-current-password', 'validateCurrentPassword');
                Route::post('update-new-password', 'updateNewPassword');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiNotifications::class)
            ->prefix('staff/notification')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
                Route::post('/mark-as-read', 'markAsRead');
                Route::post('/total-unread', 'totalUnread');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiOutlets::class)
            ->prefix('outlets')
            ->group(function () {
                Route::post('/', 'listing');
                Route::get('/{id}', 'detail');
                Route::post('/distance', 'calDistance');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiRedeemableVouchers::class)
            ->prefix('redeemable-vouchers')
            ->group(function () {
                Route::post('/claim', 'claim');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiRedeemedVouchers::class)
            ->prefix('redeemed-vouchers')
            ->group(function () {
                Route::post('/', 'listing');
                Route::post('/detail', 'detail');
                Route::post('/apply', 'apply');
                Route::get('/{voucher_id}/remove', 'removeFromCart');
                Route::post('/use-now/{voucher_id}', 'useNow');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiCoupon::class)
            ->prefix('coupon')
            ->group(function () {
                Route::post('/apply', 'apply');
                Route::post('/remove/{coupon_id}', 'remove');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiBookings::class)
            ->prefix('bookings')
            ->group(function () {
                Route::post('/available-slots', 'availableSlots');
                Route::post('/string-types', 'stringTypes');
                Route::post('/string-tensions', 'stringTensions');

                Route::post('/checkout', 'checkoutSummary');
                Route::post('/voucher/apply', 'voucherApply');
                Route::post('/voucher/remove', 'voucherRemove');
                Route::post('/checkout/validate', 'checkoutValidation');
                Route::post('/payment-methods', 'paymentMethods');
                Route::post('/create', 'create');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiBookings::class)
            ->group(function () {
                Route::post('/{type}/bookings', 'bookingListing');
                Route::post('/{type}/booking/detail', 'bookingDetail');
                Route::post('/{type}/booking/status', 'bookingUpdateStatus');
                Route::post('/{type}/reviews', 'reviewListing');
                Route::post('/{type}/review/detail', 'reviewDetail');

                Route::post('/{type}/booking/acceptance', 'bookingEditAcceptance');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiRatings::class)
            ->group(function () {
                Route::post('/bookings/rate-review', 'rateReview');
                Route::post('/{type}/reviews', 'reviewListing');
                Route::post('/{type}/review/detail', 'reviewDetail');

                Route::post('/staff/review/ranking/{outlet?}', 'reviewRanking');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiChainStore::class)
            ->prefix('chain-stores')
            ->group(function () {
                Route::get('/', 'listing');
                Route::get('/{store_id}', 'showOutlets');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiUserAddressBook::class)
            ->prefix('user-address-book')
            ->group(function () {
                Route::get('/', 'listing');
                Route::get('/{id}', 'detail');
                Route::post('/create', 'create');
                Route::post('/delete', 'delete');
                Route::post('/update-address', 'updateAddress');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiPurchaseReceipt::class)
            ->prefix('purchase-receipt')
            ->group(function () {
                Route::post('/upload', 'uploadReceipt');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiReferralReward::class)
            ->prefix('referral-reward')
            ->group(function () {
                Route::get('/', 'listing');
            });

        Route::namespace('\App\Http\Controllers\Api')
            ->controller(ApiCarts::class)
            ->prefix('carts')
            ->group(function () {
                Route::post('/add', 'add');
                Route::post('/update', 'update');
                Route::post('/delete', 'delete');
                Route::get('/summary', 'summary');
                Route::get('/badge', 'badge');
            });
    });

    Route::post('webhook', [AppointmentController::class, 'webhook'])->name('webhook');
});
