<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RetailerCreditTransaction extends Model
{
    use HasFactory;

    public function retailer()
    {
        return $this->belongsTo(Retailer::class, 'retailer_id');
    }

    public function order()
    {
        if($this->type == 'order'):
            return $this->belongsTo(Order::class, 'type_id');
        endif;
    }
}
