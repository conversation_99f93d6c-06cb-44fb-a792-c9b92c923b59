<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MediaController extends Controller
{
    function mediaStore(Request $request)
    {
        $path = storage_path('media-library/temp');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        $file = $request->file('file');
        $name = uniqid() . '_' . trim($file->getClientOriginalName());
        $file->move($path, $name);

        return response()->json([
            'name'          => $name,
            'original_name' => $file->getClientOriginalName(),
        ]);
    }

    function mediaUpload($query, $type, Request $request)
    {
        $existing_files = $query->getMedia($type);
        if(count($existing_files) > 0):
            foreach($existing_files as $item):
                if(!in_array($item->file_name, $request->input('files', []))):
                    $item->delete();
                endif;
            endforeach;
        endif;

        $media = $existing_files->pluck('file_name')->toArray();
        foreach ($request->input('files', []) as $file) {
            if (count($media) === 0 || !in_array($file, $media)) {
                $new_file = explode('_', $file);
                unset($new_file[0]);
                $new_file = implode('_', $new_file);
                $query->addMedia(storage_path('media-library/temp/' . $file))
                    ->usingName($new_file)
                    ->usingFileName($new_file)
                    ->toMediaCollection($type);
            }
        }
    }
}