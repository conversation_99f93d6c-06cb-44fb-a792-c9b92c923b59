<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PriceGroups;
use App\Models\Retailer;
use App\Models\GlobalFunction;
use App\Exports\GeneralExport;
use Maatwebsite\Excel\Facades\Excel;

class PriceGroupController extends Controller
{
    function index()
    {
        if(!checkPermission('view_price_group')):
            return redirect(route('index'))->with('error', __('You do not have permission to view price group.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('priceGroupsDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('priceGroupsExport'),
                'filename' => 'price_groups'
            ]
        ];

        if(!checkPermission('delete_price_group')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_price_group')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'price-groups';
        return view('price-groups', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = PriceGroups::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when($request->type, function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="price-groupsList">
                    <span></span>
                </label>';

                if($item->value_type == 'fixed'):
                    $value = formatNumber($item->value_my, 2, 1) . '<br>' . formatNumber($item->value_sg, 2, 2);
                else:
                    $value = $item->value_my . '%';
                endif;

                if(!checkPermission('edit_price_group')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_price_group')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-name="' . $item->name . '" data-type="'.$item->type.'" data-value_type="'.$item->value_type.'" data-value_my="'.$item->value_my.'" data-value_sg="'.$item->value_sg.'" data-status="' . $item->is_active . '" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_price_group')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    config('staticdata.price_group_type')[$item->type],
                    $value,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_price_group') && !checkPermission('delete_price_group')):
                    unset($param[6]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = PriceGroups::find($id);
        else:
            $msg = __('Added!');
            $query = new PriceGroups();
        endif;

        $value_type = $request->value_type;
        if($value_type == 'fixed'):
            $value_my = $request->value_my;
            $value_sg = $request->value_sg;
        else:
            $value_my = $request->value;
            $value_sg = $request->value;
        endif;

        $query->name = $request->name;
        $query->type = $request->type;
        $query->value_type = $value_type;
        $query->value_my = $value_my;
        $query->value_sg = $value_sg;
        $query->is_active = $request->status ?? 0;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = PriceGroups::find($request->id);
        else:
            $query = PriceGroups::whereIn('id', $request->selected);
        endif;

        // check if any product / retailer attached
        if($query->retailer_price_group->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This price group is attached with retailer.');
        endif;

        if($query->product_price_group->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This price group is attached with product(s).');
        endif;
        // END check if any product / retailer attached

        $query = $query->delete();

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = PriceGroups::find($request->id);

        // check if any product / retailer attached
        if($item->retailer_price_group->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This price group is attached with retailer.');
        endif;

        if($item->product_price_group->count() > 0):
            return GlobalFunction::sendSimpleResponse(false, 'This price group is attached with product(s).');
        endif;
        // END check if any product / retailer attached

        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = PriceGroups::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                });
            })
            ->when($request->type, function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                $item->type = config('staticdata.price_group_type')[$item->type];

                if($item->value_type == 'fixed'):
                    $item->value = formatNumber($item->value_my, 2, 1) . '<br>' . formatNumber($item->value_sg, 2, 2);
                else:
                    $item->value = $item->value_my . '%';
                endif;

                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;
        
        $fileName = 'price_groups_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'price_groups'),
            $fileName
        );
    }
}