<?php
namespace App\Http\Controllers\PaymentGateway;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Constants;
use App\Models\GlobalFunction;
use App\Models\User;
use App\Models\PaymentTransactions;
use App\Traits\PaymentGatewaysTraits;
use App\Traits\WalletsTraits;
use App\Traits\EmailTraits;

class razerGateway extends Controller
{
    use PaymentGatewaysTraits;
    use WalletsTraits;
    use EmailTraits;
    
    function url($data)
    {
        if(global_settings('razer_sandbox') == 1):
            $payment_url = "https://sandbox.merchant.razer.com/RMS/pay/";
        else:
            $payment_url = "https://merchant.razer.com/RMS/pay/";
        endif;

        $razerMerchant = global_settings('razer_id');
        $razerKey = global_settings('razer_key');
        if(is_array($data)):
            $data = (object) $data;
        endif;

        switch($data->type):
            case 'user-topup-wallet': $description = 'Topup Wallet'; break;
        endswitch;

        $user = User::find($data->user_id);
        $amount = $data->amount;
        $order_number = $data->id;
        $name = $user->first_name.' '.$user->last_name;
        $email = $user->email_address;
        $mobile = $user->phone_number;
        $description = $description ?? '';
        $country = "MY";
        $currency = global_settings('razer_currency');
        $vcode = md5( $amount.$razerMerchant.$order_number.$razerKey );
        $return_url = route('payment.response', ['gateway' => 'razer']);
        $maxAmount = global_settings('razer_max_amount');

        // validation
        $errors=[];
        if(!$razerMerchant): $errors[] = __('Razer Merchant ID is required'); endif;
        if(!$amount): $errors[] = __('Amount must be greater than 0'); endif;
        if($amount > $maxAmount): $errors[] = __('Amount ('.$amount.') is greather than maximum transaction limit ('.$maxAmount.')'); endif;
        if(!$order_number): $errors[] = __('Reference number is required'); endif;
        if(!$name): $errors[] = __('Name is required'); endif;
        if(!$email): $errors[] = __('Email address is required'); endif;
        if(!$mobile): $errors[] = __('Phone number is required'); endif;
        
        if($errors):            
            return GlobalFunction::sendSimpleResponse(false, $errors);
        endif;
        // END validation

        $url = $payment_url.$razerMerchant.'/?';
        $url .= 'amount='.$amount.'&';
        $url .= 'orderid='.urlencode($order_number).'&';
        $url .= 'bill_name='.urlencode($name).'&';
        $url .= 'bill_email='.urlencode($email).'&'; 
        $url .= 'bill_mobile='.urlencode($mobile).'&'; 
        $url .= 'bill_desc='.urlencode($description).'&';
        $url .= 'country='.$country.'&';
        $url .= 'currency='.$currency.'&';
        $url .= 'vcode='.$vcode.'&';
        $url .= 'returnurl='.$return_url;

        if(isset($data['cancel_url'])):
            $url .= '&cancelurl='.$data['cancel_url'];
        endif;

        return GlobalFunction::sendDataResponse(true, "Url retrieved", $url);
    }

    function response($request)
    {
        $payment_id = $request->orderid;
        if($payment_id):
            $payment = PaymentTransactions::find($payment_id);
            if($payment):
                if($payment->status != Constants::paymentStatus['pending']):
                    return GlobalFunction::sendSimpleResponse(false, 'This transaction has been processed before. Current status is '.$payment->status);
                endif;

                $razerSecret = global_settings('razer_secret');
                $amount = $request->amount;
                $tranID = $request->tranID;
                $status = $request->status;
                $domain = $request->domain;
                $currency = $request->currency;
                $paydate = $request->paydate;
                $appcode = $request->appcode;
                $key0 = md5( $tranID.$payment_id.$status.$domain.$amount.$currency );
                $check_key = md5( $paydate.$domain.$key0.$appcode.$razerSecret );

                if($status == 00 && $request->skey == $check_key):
                    $response = $this->updatePaymentSuccess($payment, $request->all(), $tranID);
                    $msg = $response['msg'];
                    $data = $response['data'];
                elseif($status != 22): // not pending
                    $this->updatePaymentFailed($payment, $request->all(), $tranID);
                    $msg = __('Payment failed.');
                endif;

                return GlobalFunction::sendDataResponse(true, $msg, $data ?? null);
            endif;
        else:
            return GlobalFunction::sendSimpleResponse(false, 'Transaction not found'); 
        endif;
    }
}