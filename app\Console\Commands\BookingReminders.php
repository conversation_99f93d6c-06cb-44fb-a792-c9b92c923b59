<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Constants;
use App\Models\Appointments;
use App\Models\BookingItems;
use App\Models\GlobalFunction;
use Illuminate\Console\Command;
use App\Emails\AppointmentReminder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\CronjobLogs as Cronjob;
use App\Traits\PushNotificationTraits;

class BookingReminders extends Command
{
    use PushNotificationTraits;

    protected $signature = 'booking_reminder:cron';
    protected $description = 'send notification reminder before booking time';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $booking_reminder_duration =global_settings('booking_reminder_duration');
        $bookings = BookingItems::with(['staff', 'booking.user'])
            ->whereHas('booking', function($q) {
                $q->where('booking_status', 'confirmed');
            })
            ->whereDate('booking_date', Carbon::now())
            ->whereTime('booking_time', '=', Carbon::now()->addMinutes($booking_reminder_duration)->setSecond(0))
            ->get();

        $title = "Booking Reminder";
        $remeinder_recipient = '';

        foreach($bookings as $booking) {
            $booking_date = Carbon::parse($booking->booking_date)->format('d M Y');
            if($booking->booking_date == Carbon::now()->format('Y-m-d')) {
                $booking_date .= " (today)";
            }
            $message = "You have a booking, ".$booking->booking->ref_no." on ".$booking_date." at " .$booking->booking_time_range.".";

            // Notification to member
            $notify_data = [
                'notify_type' => 'user',
                'title' => $title,
                'message' => $message,
                'type' => 'stringing_booking',
                'type_id' => $booking->booking_id,
                'user' => $booking->booking->user,
            ];
            $this->sendNotification($notify_data);

            // Notification to stringer
            $notify_data = [
                'notify_type' => 'doctor',
                'title' => $title,
                'message' => $message,
                'type' => 'stringing_booking',
                'type_id' => $booking->booking_id,
                'user' => $booking->staff,
            ];
            $this->sendNotification($notify_data);

            $remeinder_recipient .= $booking->booking->ref_no." - ".$booking->booking->user->first_name.' '.$booking->booking->user->last_name."|".$booking->staff->first_name.' '.$booking->staff->last_name.", ";
        }

        $remeinder_recipient = substr($remeinder_recipient, 0, -2);

        if(isset($remeinder_recipient) && $remeinder_recipient):
            Cronjob::create([
                "type" => "booking reminder",
                "recipient" => $remeinder_recipient
            ]);
        endif;

        return 0;
    }
}