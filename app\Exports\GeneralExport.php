<?php
namespace App\Exports;

use Illuminate\Support\Arr;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class GeneralExport implements FromView, WithColumnFormatting
{
    protected $data;
    /**
     * @return \Illuminate\Support\Collection
     */

    public function __construct($data, $type)
    {
        $this->type = $type;
        $this->data = $data->map(
            function ($item, $key) {
                return $item;
            }
        );
    }

    public function view(): View
    {
        $view_files = [
            "admin_users" => "admin_users",
            "banners" => "banners",
            "faqs" => "faqs",
            "membership_tiers" => "membership_tiers",
            "outlets" => "outlets",
            "redeemable_vouchers" => "redeemable_vouchers",
            "roles_and_priviledges" => "roles_and_priviledges",
            "staffs" => "staffs",
            "string_types" => "string_types",
            "string_tensions" => "string_tensions",
            "string_colors" => "string_colors",
            "users" => "users",
            "bookings" => "bookings",
            "doctor_holidays" => "doctor_holidays",
            "notifications" => "notifications",
            "reviews" => "reviews",
            "wallet" => "wallet",
            "point" => "point",
            "redeemed_vouchers" => "redeemed_vouchers",
            "point_redemptions" => "point_redemptions",
            "news_and_announcement" => "news_and_announcement",
            "sales_channels" => "sales_channels",
            "chain_stores" => "chain_stores",
            "user_address_book" => "user_address_book",
            "retailers" => "retailers",
            "shipping_fee" => "shipping_fee",
            "taxes" => "taxes",
            "price_groups" => "price_groups",
            "ads_spending_transaction" => "ads_spending_transaction",
            "attributes" => "attributes",
            "purchase_receipt" => "purchase_receipt",
            "gift_with_purchase" => "gift_with_purchase",
            'products' => 'products',
            'product_bundles' => 'product_bundles',
            'retailer_stock_transaction' => 'retailer_stock_transaction',
            'retailer_product' => 'retailer_product',
            'retailer_credit_transactions' => 'retailer_credit_transactions',
            'orders' => 'orders',
            'product_category' => 'product_category',
            'product_subcategory' => 'product_subcategory',
            'coupon' => 'coupon',
        ];

        $view_file = $view_files[$this->type] ?? null;
        $data = $this->data;

        return view('export.'.$view_file, [
            'data' => $data
        ]);
    }

    public function columnFormats(): array
    {
        if($this->type == "bookings"):
            return [
                'O' => NumberFormat::FORMAT_NUMBER_00,
                'Q' => NumberFormat::FORMAT_NUMBER_00,
                'R' => NumberFormat::FORMAT_NUMBER_00,
            ];
        elseif($this->type == "users"):
            return [
                'H' => NumberFormat::FORMAT_NUMBER_00,
                'I' => NumberFormat::FORMAT_NUMBER,
                'J' => NumberFormat::FORMAT_NUMBER,
            ];
        elseif($this->type == "staffs"):
            return [
                'G' => NumberFormat::FORMAT_NUMBER_00,
            ];
        elseif($this->type == "wallet"):
            return [
                'F' => NumberFormat::FORMAT_NUMBER_00,
            ];
        elseif($this->type == "point"):
            return [
                'E' => NumberFormat::FORMAT_NUMBER,
            ];
        elseif($this->type == "string_types"):
            return [
                'C' => NumberFormat::FORMAT_NUMBER_00,
            ];
        elseif($this->type == "orders"):
            return [
                'I' => NumberFormat::FORMAT_NUMBER_00,
            ];
        endif;

        return [];
    }
}