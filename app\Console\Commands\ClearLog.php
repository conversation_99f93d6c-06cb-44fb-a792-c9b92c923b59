<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\Bookings;
use App\Models\CronjobLogs as Cronjob;
use App\Models\ApiLogs;

class ClearLog extends Command
{
    protected $signature = 'clear_log:cron';
    protected $description = 'clear cronjob & api log after one week';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // clear cronjob logs
        Cronjob::where('created_at', '<=', Carbon::now()->subWeek()->format('Y-m-d H:i:s'))->delete();

        // clear api logs
        ApiLogs::where('created_at', '<=', Carbon::now()->subWeek()->format('Y-m-d H:i:s'))->delete();

        return 0;
    }
}