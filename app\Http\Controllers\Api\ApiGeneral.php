<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Http\Controllers\Controller;

class ApiGeneral extends Controller
{
    function states() {
        $states = [config('staticdata.states')];

        return GlobalFunction::sendDataResponse(true, '', $states);
    }

    function paymentMethod() {
        $payment_method = config('staticdata.payment_method');

        return GlobalFunction::sendDataResponse(true, '', $payment_method);
    }

    function setting() {

        $setting_name = [
            'bookings_points_rm_ratio',
            'bookings_points_rm_expired_enable',
            'bookings_points_rm_expired_duration',
            'points_referral_type',
            'points_referral_value'
        ];

        $settings = GlobalSettings::whereIn('name', $setting_name)->pluck('value', 'name')->toArray();

        return GlobalFunction::sendDataResponse(true, '', $settings);
    }
    
    function countryList()
    {
        $country = config('staticdata.country_list');
        
        return GlobalFunction::sendDataResponse(true, '', $country);
    }
}
