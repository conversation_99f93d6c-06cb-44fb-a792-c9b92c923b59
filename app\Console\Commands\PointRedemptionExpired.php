<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserPointRedemptions;
use App\Models\Constants;
use App\Models\CronjobLogs as Cronjob;

class PointRedemptionExpired extends Command
{
    protected $signature = 'point_redemptions_expired:cron';
    protected $description = 'update point redemptions to expired if have redemption date & date is meet';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $redemptions = UserPointRedemptions::where('status', Constants::userPointRedemptionAvailable)
            ->get();

        $updated_list = "";
        if(count($redemptions) > 0):
            foreach($redemptions as $item):
                $voucher = $item->redemption_voucher;
                if($voucher->redemption_end_date && $voucher->redemption_end_date < date("Y-m-d H:i:s")):
                    $item->status = Constants::userPointRedemptionExpired;
                    $item->save();

                    $updated_list .= $item->id . ", ";
                endif;
            endforeach;

            $updated_list = substr($updated_list, 0, -2);
        endif;

        // cronjob log
        if(isset($updated_list) && $updated_list):
            Cronjob::create([
                "type" => "point redemptions expired",
                "recipient" => $updated_list ? $updated_list : "No point redemptions expired"
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}