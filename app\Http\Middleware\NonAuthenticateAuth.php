<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NonAuthenticateAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->bearerToken()) {
            if (Auth::guard('sanctum')->check()) {
                Auth::setUser(
                    Auth::guard('sanctum')->user()
                );
            } else {
                return response()->json([
                    'message' => 'Unauthorized',
                ], 401);
            }
        }

        return $next($request);
    }
}
