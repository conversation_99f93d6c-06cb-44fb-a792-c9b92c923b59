<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class WalletToppedUp extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		$subject = __('[:site_name] Wallet is Topped Up', ['site_name' => global_settings('site_name')]);
		$build = $this->subject($subject)
            ->view('emails.wallet-topped-up')
            ->with('data', $this->data);
	}
}