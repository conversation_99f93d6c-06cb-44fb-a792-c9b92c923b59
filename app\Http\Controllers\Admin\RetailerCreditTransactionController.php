<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\RetailerCreditTransaction;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\RetailerCreditTransactionTraits;

class RetailerCreditTransactionController extends Controller
{
    use RetailerCreditTransactionTraits;

    function index()
    {
        if (!checkPermission('view_retailer_credit_transaction')):
            return redirect(route('index'))->with('error', __('You do not have permission to view retailer credit transaction.'));
        endif;

        $moduleID = 'retailer-credit-transaction';
        return view('retailer-credit-transaction', compact('moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'updated_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');

        $result = RetailerCreditTransaction::when($search, function ($q) use ($search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('order_no', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($q) use ($request) {
                $q->where('retailer_id', $request->retailer);
            })
            ->when($request->type, function ($q) use ($request) {
                $q->where('type', $request->type);
            })
            ->when($request->date, function ($q) use ($request) {
                $date = explode(" to ", $request->date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            });
        $summary_result = $result->clone();
        $result = $result->when($request->prefix, function ($q) use ($request) {
                $q->where('prefix', $request->prefix);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            // summary
            $summary_result = $summary_result->get();
            $add_credit = $summary_result->where('prefix', '+')->sum('amount');
            $deduct_credit = $summary_result->where('prefix', '-')->sum('amount');
            $credit_balance = $add_credit - $deduct_credit;
            // END summary

            foreach ($result as $count => $item):
                $type = $item->type;
                $type_detail = config('staticdata.retailer_credit_transaction.type.'.$type);
                if($type == 'order'):
                    $type_detail .= '<div class="text-grey">'.$item->order->order_no.'</div>';
                endif;

                $retailer = "<div>" . $item->retailer->first_name . '<div class="text-grey">' . $item->retailer->user_name . "</div></div>";

                $date = "<div class='text-center'><span style='display:none'>".$item->created_at."</span>" . Carbon::parse($item->created_at)->format('d-m-Y') . '<br>' . Carbon::parse($item->created_at)->format('g:i a') . "</div>";

                if ($count == 0):
                    $date .= '<input type="hidden" name="amountBalance" value="' . formatNumber($credit_balance) . '">';
                    $date .= '<input type="hidden" name="addAmounts" value="' . formatNumber($add_credit) . '">';
                    $date .= '<input type="hidden" name="deductAmounts" value="' . formatNumber($deduct_credit) . '">';
                endif;

                $credit = "<div class='text-right'>" . $item->prefix . " " . formatNumber($item->amount). "</div>";

                $param = [
                    $date,
                    $retailer,
                    $type_detail,
                    $credit,
                    nl2br($item->remarks),
                ];

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data,
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $data = [
            'retailer_id' => $request->retailer_id,
            'type' => $request->type,
            'type_id' => null,
            'prefix' => $request->prefix,
            'amount' => $request->amount,
            'remarks' => $request->remarks,
        ];
        $this->insertCreditTransaction($data);

        return GlobalFunction::sendSimpleResponse(true, ' Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'updated_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');

        $result = RetailerCreditTransaction::when($search, function ($q) use ($search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('order_no', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($q) use ($request) {
                $q->where('retailer_id', $request->retailer);
            })
            ->when($request->type, function ($q) use ($request) {
                $q->where('type', $request->type);
            })
            ->when($request->date, function ($q) use ($request) {
                $date = explode(" to ", $request->date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->when($request->prefix, function ($q) use ($request) {
                $q->where('prefix', $request->prefix);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->type_detail = '';
                if($item->type == 'order'):
                    $item->type_detail = $item->order->order_no;
                endif;

                $item->type = config('staticdata.retailer_credit_transaction.type.'.$item->type);
                $item->prefix = config('staticdata.retailer_credit_transaction.prefix.'.$item->prefix);
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'retailer_credit_transaction_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'retailer_credit_transactions'),
            $fileName
        );
    }
}
