<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Carts;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\FeedbackReminderNotification;
use App\Traits\PushNotificationTraits;

class OrderFeedbackReminders extends Command
{
    use PushNotificationTraits;

    protected $signature = 'order_feedback_reminder';
    protected $description = 'send notification reminder after order delivery';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $feedback_reminder = global_settings('feedback_reminder');

        $users = User::whereHas('orders',function($query) use($feedback_reminder){
                $query->where('status', 'completed')
                    ->where('user_type', 'customer')
                    ->whereDate('completed_at', '<=', Carbon::now()->subDays($feedback_reminder));
            })
            ->where('is_sent_feedback', 0)
            ->get();

        $reminder_recipient = [];
        foreach($users as $user):
            Notification::route('whatsapp', $user->phone_number)
                ->notify(new FeedbackReminderNotification($user, 'order'));

            $user->is_sent_feedback = 1;
            $user->save();

            $reminder_recipient[] = $user->id.": ".$user->phone_number;
        endforeach;

        if($reminder_recipient):
            Cronjob::create([
                "type" => "order feedback",
                "recipient" => implode(', ', $reminder_recipient),
            ]);
        endif;

        return 0;
    }
}