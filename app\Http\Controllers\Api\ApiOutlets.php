<?php

namespace App\Http\Controllers\Api;

use App\Models\Outlets;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use Ballen\Distical\Calculator;
use Ballen\Distical\Entities\LatLong;

class ApiOutlets
{
    function listing(Request $request)
    {
        // filters
        $state = $request->state ?? null;
        $search = $request->search ?? null;
        $per_page = $request->per_page ?? 10;

        $data = Outlets::where('is_active', 1)
            ->when(isset($state), function ($query) use ($state) {
                return $query->where('state', $state);
            })
            ->when(isset($search), function ($query) use ($search) {
                $query->where('name', 'LIKE', "%{$search}%")->orWhere('contact_number', 'LIKE', "%{$search}%");
            })
            ->get();

        if ($data->count() > 0):
            $data->map(function ($item) {
                if ($item->image):
                    $item->image = GlobalFunction::createMediaUrl($item->image);
                endif;

                if ($item->operating_hours):
                    $item->operating_hours = json_decode($item->operating_hours, true);

                    $hours = [];
                    foreach ($item->operating_hours as $key => $value):
                        if ($value['from']):
                            $hours[$key] = [
                                "from" => date('H:i', strtotime($value['from'])),
                                "to" => date('H:i', strtotime($value['to'])),
                            ];
                        else:
                            $hours[$key] = [
                                "from" => '',
                                "to" => '',
                            ];
                        endif;
                    endforeach;

                    $item->operating_hours = $hours;
                endif;

                // check if open
                $is_open = false;
                $close_time = null;
                $open_time = null;
                $today = strtolower(date('l'));
                $currentTime = date('H:i');
                if (isset($hours[$today])):
                    $from = $hours[$today]['from'];
                    $to = $hours[$today]['to'];
                    if ($from && isOpenNow($from, $to, $currentTime)):
                        $is_open = true;
                        $close_time = $to;
                    endif;
                endif;

                if (!$is_open):
                    $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                    $todayIndex = array_search($today, $daysOfWeek);
                    $today = $daysOfWeek[$todayIndex];

                    for ($i = 1; $i < 7; $i++):
                        $nextDayIndex = ($todayIndex + $i) % 7;
                        $nextDay = $daysOfWeek[$nextDayIndex];

                        if (!$open_time):
                            if (isset($hours[$today]) && $hours[$today]['from'] && $currentTime <= $hours[$today]['to']):
                                $open_time = $hours[$today]['from'];
                            elseif (isset($hours[$nextDay]) && $hours[$nextDay]['from']):
                                $open_time = ucwords($nextDay) . ', ' . $hours[$nextDay]['from'];
                            endif;
                        endif;
                    endfor;
                endif;
                $item->is_open = $is_open;
                $item->close_time = $close_time;
                $item->open_time = $open_time;
                // END check if open

                return $item;
            });
        endif;

        $data = collect($data)->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail($id)
    {
        $data = Outlets::find($id);

        if (!$data) {
            return GlobalFunction::sendSimpleResponse(false, 'Outlet not found.');
        }

        // tidy operating hours
        if ($data->operating_hours) {
            $data->operating_hours = json_decode($data->operating_hours, true);

            $hours = [];
            foreach ($data->operating_hours as $key => $value) {
                if ($value['from']) {
                    $hours[$key] = [
                        "from" => date('H:i', strtotime($value['from'])),
                        "to" => date('H:i', strtotime($value['to'])),
                    ];
                } else {
                    $hours[$key] = [
                        "from" => '',
                        "to" => '',
                    ];
                }
            }

            $data->operating_hours = $hours;
        }

        // check if open
        $is_open = false;
        $close_time = null;
        $open_time = null;
        $today = strtolower(date('l'));
        $currentTime = date('H:i');
        if (isset($hours[$today])) {
            $from = $hours[$today]['from'];
            $to = $hours[$today]['to'];
            if ($from && $currentTime >= $from && $currentTime <= $to) {
                $is_open = true;
                $close_time = $to;
            }
        }

        if (!$is_open) {
            $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            $todayIndex = array_search($today, $daysOfWeek);
            $today = $daysOfWeek[$todayIndex];

            for ($i = 1; $i < 7; $i++) {
                $nextDayIndex = ($todayIndex + $i) % 7;
                $nextDay = $daysOfWeek[$nextDayIndex];

                if (!$open_time) {
                    if (isset($hours[$today]) && $hours[$today]['from'] && $currentTime <= $hours[$today]['to']) {
                        $open_time = $hours[$today]['from'];
                    } elseif (isset($hours[$nextDay]) && $hours[$nextDay]['from']) {
                        $open_time = ucwords($nextDay) . ', ' . $hours[$nextDay]['from'];
                    }
                }
            }
        }
        $data->is_open = $is_open;
        $data->close_time = $close_time;
        $data->open_time = $open_time;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function calDistance(Request $request)
    {
        $outlet = Outlets::find($request->outlet_id);

        if (!$outlet) {
            return GlobalFunction::sendSimpleResponse(false, 'Outlet not found.');
        }

        $from = new LatLong($request->lat, $request->long);
        $to = new LatLong($outlet->latitude, $outlet->longitude);

        $distanceCalculator = new Calculator($from, $to);

        $distance = $distanceCalculator->get()->asKilometres();

        $data =[
            'distance' => number_format($distance,2) .' km'
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
