<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'is_active',
        'online_order',
    ];

    public function productSubcategories()
    {
        return $this->hasMany(ProductSubcategory::class);
    }

    public function products()
    {
        return $this->hasMany(Products::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }
}
