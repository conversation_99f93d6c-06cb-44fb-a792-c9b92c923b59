<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RetailerProduct extends Model
{
    use HasFactory;

    public function retailer()
    {
        return $this->belongsTo(Retailer::class, 'retailer_id', 'user_id');
    }

    public function getDiscountDateAttribute()
    {
        if($this->discount_start):
            return Carbon::parse($this->discount_start)->format('d-m-Y, g:ia').' to '.Carbon::parse($this->discount_end)->format('d-m-Y, g:ia');
        endif;
    }
}
