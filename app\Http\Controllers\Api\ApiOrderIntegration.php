<?php
namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use App\Models\User;
use App\Models\Order;
use App\Models\MembershipTiers;
use App\Models\UserMembership;
use App\Traits\OrderTraits;

// for 3rd party integrate to system
class ApiOrderIntegration
{
    use OrderTraits;

    function __construct()
    {
        $this->username = 'prideapi';
        $this->password = 'PxLDIsURGdQI4nk';
        $this->brand = 'nattome';
    }
    
    /** 
        * Generate Token
        *
        * <aside class="notice">Request username and password to generate token.</aside>
        *
        * @bodyParam username string required Example: user-name
        * @bodyParam password string required Example: pass-word
        *
        * @response scenario=success {
        *   "status": true,
        *   "message": "Token generated",
        *   "data": {
        *       "token": "00IowvJfBXbymTx3BlCPQYRNypdp3Ya1NSutzF0wvv9pN7DKTGRSpfLP3ndJ"
        *   }
        * }
        *
        * @response scenario=failed {
        *   "status": false,
        *   "message": {
        *       "The username is required.",
        *       "The password is required."
        *   }
        * }
        *
        * @unauthenticated 
    */
    function getToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required',
            'password' => 'required'
        ]);
        if ($validator->fails()):
            return GlobalFunction::sendSimpleResponse(false, $validator->errors()->all());
        endif;
    
        $username = $request->username;
        $password = $request->password;
        if($username != $this->username):
            return GlobalFunction::sendSimpleResponse(false, 'Invalid username');
        endif;
        if($password != $this->password):
            return GlobalFunction::sendSimpleResponse(false, 'Invalid password');
        endif;

        $token_expiry = global_settings('3pl_connect_token_expiry');
        if($token_expiry && strtotime($token_expiry) > time()):
            return GlobalFunction::sendDataResponse(true, 'Token generated', ['token' => global_settings('3pl_connect_token')]);
        endif;

        $token = \Str::random(60);
        $field = [
            '3pl_connect_token' => $token,
            '3pl_connect_token_expiry' => date('Y-m-d H:i:s', strtotime('+1 day'))
        ];
        foreach($field as $key => $value):
            GlobalSettings::setSetting($key, $value);
        endforeach;
        
        return GlobalFunction::sendDataResponse(true, 'Token generated', ['token' => $token]);
    }

    function checkToken(Request $request)
    {
        $token = $request->bearerToken();
        if(!$token):
            return GlobalFunction::sendSimpleResponse(false, 'Unauthorized');
        endif;
        if($token != global_settings('3pl_connect_token')):
            return GlobalFunction::sendSimpleResponse(false, 'Invalid token');
        endif;
        if(strtotime(global_settings('3pl_connect_token_expiry')) < time()):
            return GlobalFunction::sendSimpleResponse(false, 'Token expired');
        endif;

        return true;
    }
    
    /** 
        * State List
        *
        * <aside class="notice">Follow this list for shipping state</aside>
        *
        * @response scenario=success {
        *   "status": true,
        *   "message": "States list",
        *   "data": {
        *       "johor",
        *       "kedah",
        *       "kelantan",
        *       "kuala lumpur",
        *       "labuan",
        *       "melaka",
        *       "negeri sembilan",
        *       "pahang",
        *       "penang",
        *       "perak",
        *       "perlis",
        *       "putrajaya",
        *       "sabah",
        *       "sarawak",
        *       "selangor",
        *       "terengganu",
        *   }
        * }
    */
    function getStateList(Request $request)
    {
        $checkToken = $this->checkToken($request);
        if($checkToken !== true):
            return $checkToken;
        endif;

        $data = array_keys(config('staticdata.states'));

        return GlobalFunction::sendDataResponse(true, 'States list', $data);
    }

    /** 
        * Order - Status List
        *
        * <aside class="notice">Follow this list for order status</aside>
        *
        * @response scenario=success {
        *   "status": true,
        *   "message": "Order status list",
        *   "data": {
        *       "pending",
        *       "paid",
        *       "shipped",
        *       "completed",
        *       "cancelled",
        *       "refunded",
        *   }
        * }
    */
    function getOrderStatusList(Request $request)
    {
        $checkToken = $this->checkToken($request);
        if($checkToken !== true):
            return $checkToken;
        endif;

        $data = array_keys(config('staticdata.order_status'));

        return GlobalFunction::sendDataResponse(true, 'Order status list', $data);
    }

    /** 
        * Order - Create
        *
        * @bodyParam order_id integer required The order id from pride Example: 364
        * @bodyParam order_reference string required Order reference number from pride Example: WET3242341346
        * @bodyParam created_date string required Example: 2025-03-07 09:16:37
        * @bodyParam payment_country string required Malaysia or singapore only Example: malaysia
        * @bodyParam subtotal string required Example: 150.00
        * @bodyParam shipping_fee string Example: 20.00
        * @bodyParam discount_amount string If any else return 0 Example: 10.00
        * @bodyParam total_tax string If any else return 0 Example: 0.00
        * @bodyParam total_amount string required Example: 160.00
        * @bodyParam tracking_number string If any else return null Example: 8457257358356
        * @bodyParam delivery_method_name string If any else return null Example: null
        * @bodyParam billing_user_name string required Example: Customer Name
        * @bodyParam billing_user_email string required Example: <EMAIL>
        * @bodyParam billing_user_phone string required Example: +60126668888
        * @bodyParam shipping_recipient_name string required Example: Recipient Name
        * @bodyParam shipping_recipient_phone string required Example: +***********
        * @bodyParam shipping_address string required Example: Recipient Address
        * @bodyParam shipping_city string required Example: Puchong
        * @bodyParam shipping_postcode string required Example: 47100
        * @bodyParam shipping_state string required Refer to state list Example: selangor
        * @bodyParam shipping_country string required Example: malaysia
        * @bodyParam payment_method string If any else null Example: Bank Transfer
        * @bodyParam status string required Refer to status list Example: paid
        * @bodyParam remarks string If any else null Example: please deliver by today
        * @bodyParam products object[] required
        * @bodyParam products[].product_name string required Example: Nattome Product
        * @bodyParam products[].product_sku string required Example: nattome-food
        * @bodyParam products[].variation_name string Example: null
        * @bodyParam products[].variation_sku string Example: null
        * @bodyParam products[].is_bundle boolean True or False Example: null
        * @bodyParam products[].bundle_name string Example: null
        * @bodyParam products[].is_gwp boolean True or False Example: null
        * @bodyParam products[].is_pwp boolean True or False Example: null
        * @bodyParam products[].quantity integer required Example: 3
        * @bodyParam products[].unit_price string required Example: 50.00
        * @bodyParam products[].unit_tax string Example: null
        * @bodyParam products[].total_price string required Example: 150.00
        * @bodyParam products[].total_tax string Example: null
        * @bodyParam products[].tax_name string Example: null
        * @bodyParam products[].tax_rate string Example: null
        *
        * @response scenario=success {
        *   "status": true,
        *   "message": "Order created.",
        *   "data": {
        *       "order_id": 34,
        *       "3pl_order_id": 364,
        *       "3pl_order_reference": "WET3242341346",
        *       "order_status": "paid",
        *   }
        * }
        *
        * @response scenario=failed {
        *   "status": false,
        *   "message": {
        *       "The order id is required.",
        *       "The selected status is invalid.",
        *   }
        * }
    */
    function orderCreate(Request $request)
    {
        // currently only payment from malaysia and singapore
        // brand nattome
        $checkToken = $this->checkToken($request);
        if($checkToken !== true):
            return $checkToken;
        endif;

        $validator = Validator::make($request->all(), [
            'order_id' => 'required',
            'order_reference' => 'required',
            'created_date' => 'required',
            'payment_country' => 'required|in:malaysia,singapore',
            'subtotal' => 'required',
            'total_amount' => 'required',
            'billing_user_name' => 'required',
            'billing_user_email' => 'required|email',
            'billing_user_phone' => 'required',
            'shipping_recipient_name' => 'required',
            'shipping_recipient_phone' => 'required',
            'shipping_address' => 'required',
            'shipping_city' => 'required',
            'shipping_postcode' => 'required',
            'shipping_state' => 'required|in:'.implode(',', array_merge(array_keys(config('staticdata.states')), array_map('ucwords', array_keys(config('staticdata.states'))))),
            'shipping_country' => 'required',
            'status' => 'required|in:'.implode(',', array_merge(array_keys(config('staticdata.order_status')), array_map('ucwords', array_keys(config('staticdata.order_status'))))),
            'products' => 'required|array',
            'products.*.product_name' => 'required',
        ], [
            'products.*.product_name.required' => 'At least one product is required.'
        ]);
        if ($validator->fails()):
            return GlobalFunction::sendSimpleResponse(false, $validator->errors()->all());
        endif;   

        $user_name = $request->billing_user_name;
        $user_email = $request->billing_user_email;
        $user_phone = $request->billing_user_phone;
        $country = $request->payment_country;
        // check if user exist, else create user
            $user = User::where('phone_number', $user_phone)
                ->where('brand', $this->brand)
                ->where('country', $country)
                ->first();
            if($user):
                $user_id = $user->id;
            else:
                $user = new User();
                $user->name = $user_name;
                $user->brand = $this->brand;
                $user->country = $country;
                $user->email_address = $user_email ?? null;
                $user->phone_number = $user_phone;
                $user->is_notification = 1;
                $user->is_active = 1;
                $user->is_verified = 1;
                $user->referral_code = \Str::random(8);

                $lowest_tier = getLowestMembershipTier($this->brand);
                $user->membership_tier = $lowest_tier->id ?? 0;
                $user->save();
                $user_id = $user->id;

                // create membership record
                if($lowest_tier):
                    UserMembership::create([
                        'user_id' => $user_id,
                        'membership_id' => $lowest_tier->id,
                        'remaining_maintain_purchase' => $lowest_tier->min_purchase,
                        'start_date' => Carbon::now()->format(config('app.db_date_format')),
                        'expiry_date' => Carbon::now()->addMonth($lowest_tier->expiration_month)->format(config('app.db_date_format')),
                    ]);
                endif;
            endif;
        // END check if user exist, else create user

        $request->merge([
            'user_id' => $user_id,
            'user_type' => 'customer',
            'brand' => $this->brand,
            'country' => $country,
            'voucher_amount' => $request->discount_amount ?? 0,
            'platform_from' => '3PL',
        ]);

        $order = $this->createOrderFromIntegration($request);
        if(!$order):
            return GlobalFunction::sendSimpleResponse(false, 'This order is exist.');
        endif;

        $data = [
            'order_id' => $order->id,
            '3pl_order_id' => $order->platform_from_id,
            '3pl_order_reference' => $order->order_no,
            'order_status' => $order->status,
        ];
        return GlobalFunction::sendDataResponse(true, 'Order created.', $data);
    }

    /** 
        * Order - Update Status
        *
        * @bodyParam order_id integer required The order id from BW Example: 9
        * @bodyParam 3pl_order_id integer required The order id from pride Example: 264
        * @bodyParam status string required Refer to status list Example: shipped
        *
        * @response scenario=success {
        *   "status": true,
        *   "message": "Status updated.",
        *   "data": {
        *       "order_id": 34,
        *       "3pl_order_id": "364",
        *       "3pl_order_reference": "WET3242341346",
        *       "order_status": "shipped",
        *   }
        * }
        *
        * @response scenario=failed {
        *   "status": false,
        *   "message": {
        *       "Order not found.",
        *   }
        * }
    */
    function OrderUpdateStatus(Request $request)
    {
        $checkToken = $this->checkToken($request);
        if($checkToken !== true):
            return $checkToken;
        endif;

        $order = $this->updateOrderStatus($request->order_id, $request->status, $request->post('3pl_order_id'));
        if(!$order):
            return GlobalFunction::sendSimpleResponse(false, 'Order not found.');
        endif;

        $data = [
            'order_id' => $order->id,
            '3pl_order_id' => $order->platform_from_id,
            '3pl_order_reference' => $order->order_no,
            'order_status' => $order->status,
        ];
        return GlobalFunction::sendDataResponse(true, 'Status updated.', $data);
    }
}