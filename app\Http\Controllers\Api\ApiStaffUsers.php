<?php
namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\GlobalFunction;
use App\Models\Doctors;

class ApiStaffUsers
{
    function logout()
    {
        $user = auth()->user();
        $user->tokens()->delete();

        return GlobalFunction::sendSimpleResponse(true, __('Account is logged out.'));
    }

    function delete()
    {
        $user = auth()->user();
        $user->is_active = 0;
        $user->save();

        return GlobalFunction::sendSimpleResponse(true, __('Account is deleted.'));
    }

    function profile()
    {
        $user = auth()->user();
        $user = GlobalFunction::getDoctorData($user);

        return GlobalFunction::sendDataResponse(true, '', $user);
    }

    function updateProfile(Request $request)
    {
        $user = auth()->user();
        if(isset($request->first_name) && $request->first_name):
            $user->first_name = $request->first_name;
        endif;

        if(isset($request->last_name) && $request->last_name):
            $user->last_name = $request->last_name;
        endif;

        if(isset($request->email_address) && $request->email_address):
            // Check if email is already taken
            $email_exists = Doctors::where('email_address', $request->email_address)
                ->where('id', '!=', $user->id)
                ->first();
            if($email_exists):
                return GlobalFunction::sendSimpleResponse(false, __('Email address is already taken.'));
            endif;
            $user->email_address = $request->email_address;
        endif;

        if(isset($request->phone_number) && $request->phone_number):
            if(!GlobalFunction::validatePhoneNumber($request->phone_number)):
                return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
            endif;
            $user->mobile_number = $request->phone_number;
        endif;

        $user->save();
        $user = GlobalFunction::getDoctorData($user);
        return GlobalFunction::sendDataResponse(true, __('Profile updated.'), $user);
    }

    function validateCurrentPassword(Request $request)
    {
        $rules = [
            'current_password' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $user = auth()->user();
        if(!Hash::check($request->current_password, $user->password)):
            return GlobalFunction::sendSimpleResponse(false, __('Current password is incorrect.'));
        endif;

        return GlobalFunction::sendSimpleResponse(true, __('Current password is correct.'));
    }

    function updateNewPassword(Request $request)
    {
        $rules = [
            'new_password' => 'required',
            'confirm_password' => 'required|same:new_password'
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;
        
        $user = auth()->user();
        $user->password = Hash::make($request->new_password);
        $user->save();

        return GlobalFunction::sendSimpleResponse(true, __('Password updated.'));
    }
}