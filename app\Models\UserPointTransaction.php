<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPointTransaction extends Model
{
    use HasFactory;
    public $table = "user_point_transactions";

    public function appointment()
    {
        return $this->hasOne(Appointments::class, 'id', 'appointment_id');
    }

    public function redemption_voucher()
    {
        return $this->hasOne(RedemptionVouchers::class, 'id', 'voucher_id');
    }

    public function booking()
    {
        return $this->belongsTo(Bookings::class, 'type_id', 'id');
    }

    public function voucher()
    {
        return $this->belongsTo(UserVouchers::class, 'type_id', 'id');
    }

    public function user()
    {
        return $this->hasOne(Users::class, 'id', 'user_id');
    }

    public function order()
    {
        return $this->hasOne(Order::class, 'id', 'type_id');
    }
}
