<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Models\Products;
use Illuminate\Http\Request;
use App\Models\ProductBundle;
use App\Traits\ProductTraits;
use App\Models\GlobalFunction;
use App\Models\ProductCategory;
use App\Http\Controllers\Controller;

class ApiProducts extends Controller
{
    use ProductTraits;

    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'];
        }
    }

    public function listing(Request $request)
    {
        $tax = global_settings('tax')/100;

        $categories = ProductCategory::with(['products' => function($query) {
            $query->active();
        }])
        ->whereHas('products', function($query) {
            $query->active();
        })
        ->active()
        ->get()
        ->makeHidden(['created_at', 'updated_at'])
        ->each(function ($category) use($tax){
            $category->products->each(function ($product) use($tax,$category) {
                $product->tax = 0;
                if($category->product_weight & $tax > 0){
                    $product->tax = formatNumber((float)$product->price * $tax,2);
                    // $product->price += $product->tax;
                }

                $product->price = formatNumber((float) $product->price, 2);
                $product->currency = global_settings('currency');
                $product->image = $product->single_image_url;
                $product->product_subcategory = $product->productSubcategory ?? null;
                $product->setHidden([
                    'variant_same_as_parent',
                    'price_group',
                    'country',
                    'brand',
                    'retailer_price_my',
                    'retailer_price_sg',
                    'retailer_price_group',
                    'retailer_tax_id',
                    'created_at',
                    'updated_at',
                    'image_url',
                    'single_image_url',
                    'media',
                ]);
            });
        });

        return GlobalFunction::sendDataResponse(true, '', $categories);
    }

    public function detail(Request $request)
    {
        $id = $request->id;
        // $brand = $request->route()->parameters['brand'];
        $user = auth()->user();

        $user_tier = null;
        $current_tier = null;
        if (!is_null($user)) {
            $user_tier = $user->membership_tier;
            $current_tier = $user->membership_tiers->name;
        }
        // $country = $user->country;

        $data = Products::with(['productCategory', 'productSubcategory'])
            ->where('is_active', 1)
            // ->where('is_saleable', 1)
            // ->when(isset($this->brand), function ($query) {
            //     return $query->where('brand', $this->brand);
            // })
            // ->whereJsonContains('country', 'malaysia')
            ->find($id);

        if (!$data) {
            return GlobalFunction::sendSimpleResponse(false, 'Product not found!');
        }

        $data->currency = global_settings('currency');
        $tax = global_settings('tax')/100;
        $data->current_tier = $current_tier;

        $data->tax = 0;
        if($data->productCategory->product_weight & $tax > 0){
            $data->tax = formatNumber((float)$data->price * $tax,2);
            // $product->price += $product->tax;
        }

        // pricing
        if ($data->productPrices->count() > 0) {
            $is_variant = $data->is_variant;
            $price = $data->productPrices()->where('product_id', $id)
                ->when(!$is_variant, function ($query) {
                    return $query->where(function ($query) {
                        $query->where('variation_id', 0)
                            ->orWhereNull('variation_id');
                    });
                })
                ->get();

            if ($is_variant):
                $tier_price = null;
                // variation
                $variations = $data->variations()->where('product_id', $id)
                    ->orderBy('sequence', 'asc')
                    ->where('is_active', 1)
                    ->get();
                $variation_list = $variations->map(function ($item) use ($price, $user_tier, $data) {
                    $price = $price->where('variation_id', $item->id)->keyBy('tier_id');
                    $tier_price = $price->map(function ($price_item) use ($user_tier) {
                        $discounted_price = $price_item->price_my;
                        $discount_value = $price_item->discount_value_my;

                        $discount_amount = calculateDiscountAmount($discounted_price, $price_item->discount_type, $discount_value);
                        $discounted_price -= $discount_amount;

                        return [
                            'current_tier' => ($user_tier == $price_item->tier_id),
                            'tier_id' => $price_item->tier_id,
                            'tier_name' => $price_item->membership_tier->name,
                            'regular_price' => formatNumber((float) $price_item->price_my, 2),
                            'discounted_price' => formatNumber((float) $discounted_price, 2),
                            'discount_value' => $discount_value,
                            'discount_type' => $price_item->discount_type,
                        ];
                    })->values()->toArray();

                    return [
                        'id' => $item->id,
                        'name' => $item->variation_name,
                        'sku' => $item->sku,
                        'image' => $item->image_url,
                        'price' => $tier_price,
                    ];
                })->toArray();
            // END variation
            else:
                $tier_price = $price->map(function ($item) use ($user_tier) {
                    $discounted_price = $item->price_my;
                    $discount_value = $item->discount_value_my;

                    $discount_amount = calculateDiscountAmount($discounted_price, $item->discount_type, $discount_value);
                    $discounted_price -= $discount_amount;

                    return [
                        'current_tier' => ($user_tier == $item->tier_id),
                        'tier_id' => $item->tier_id,
                        'tier_name' => $item->membership_tier->name,
                        'regular_price' => formatNumber((float) $item->price_my, 2),
                        'discounted_price' => formatNumber((float) $discounted_price, 2),
                        'discount_value' => $discount_value,
                        'discount_type' => $item->discount_type,
                    ];
                })->toArray();
            endif;
            $data->price = ($is_variant ? null : $tier_price);
            $data->variation = ($is_variant ? $variation_list : null);
        }

        // END pricing

        // bundle
        $bundle = null;
        if ($data->bundle_with_quantity_discount):
            $bundle_data = $data->bundle_with_quantity_discount;

            $tier_data = getMemberTierList();
            $tier_list = [];
            foreach ($tier_data as $tier_item):
                $bundle_item = $bundle_data->discount_packages()
                    ->where('membership_tier_ids', 'like', '%' . $tier_item->id . '%')
                    ->orderBy('quantity', 'asc')
                    ->get();
                if ($bundle_item->count() > 0):
                    $tier_list[] = [
                        'tier_id' => $tier_item->id,
                        'tier_name' => $tier_item->name,
                        'current_tier' => ($user_tier == $tier_item->id),
                        'bundle_id' => $bundle_data->id,
                        'bundle_name' => $bundle_data->name,
                        'is_mix_match' => $bundle_data->is_mix_match,
                        'packages' => $bundle_item->map(function ($item) {
                            return $item->package_name;
                        })->toArray(),
                    ];
                endif;
            endforeach;

            usort($tier_list, function ($a, $b) {
                return $a['current_tier'] < $b['current_tier'];
            });

            $bundle = $tier_list;
        endif;

        $data->bundles = $bundle;

        $data->is_wishlist = $data->wishlists()->where('user_id', $user->id ?? 0)->count() > 0 ? 1 : 0;
        // END bundle

        $data->makeHidden([
            'variant_same_as_parent',
            'price_group',
            'country',
            'brand',
            'retailer_price_my',
            'retailer_price_sg',
            'retailer_price_group',
            'retailer_tax_id',
            'quantity',
            'created_at',
            'updated_at',
            'single_image_url',
            'media',
            'bundle_with_quantity_discount'
        ]);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    public function bundleDetail(Request $request)
    {
        $product_id = $request->product_id;
        $product_qty = $request->product_qty ?? 0; // if dont have variation
        $variant = $request->variant_id ?? null;
        $variant_qty = $request->variant_qty ?? null;

        // $brand = $request->route()->parameters['brand'];
        $user = auth()->user();
        $user_tier = (string) $user->membership_tier;
        $country = $user->country;

        $product = Products::find($product_id);
        // get price
        $variation = [
            'variant_id' => $variant,
            'variant_qty' => $variant_qty,
        ];
        $bundle_data = $this->getBundlePrice($user_tier, $country, $product, $variation, $product_qty);
        $price = $bundle_data['price'];
        $bundle_discount = $bundle_data['bundle_discount'];
        $bundle_id = $bundle_data['bundle_id'];
        // END get price

        if ($variant):
            $product_qty = array_sum($variant_qty);
        endif;

        // get gwp & pwp
        $gwp = null;
        $pwp = null;
        if ($bundle_id > 0):
            $bundle = ProductBundle::find($bundle_id);
            $gwp = $this->getGwpList($bundle, $user_tier, $product_qty, $price, $bundle_discount);
            $pwp = $this->getPwpList($bundle, $user_tier, $product_qty, $price, $bundle_discount);
        endif;

        // find normal product type bundle
        if (!$gwp || !$pwp):
            $bundle = $product->bundle_with_normal;
            $bundle_discount = null;

            if ($bundle):
                if (!$gwp):
                    $gwp = $this->getGwpList($bundle, $user_tier, $product_qty, $price, $bundle_discount);
                endif;

                if (!$pwp):
                    $pwp = $this->getPwpList($bundle, $user_tier, $product_qty, $price, $bundle_discount);
                endif;
            endif;
        endif;

        $data = [
            'bundle_id' => $bundle_id ?? 0,
            'currency' => global_settings('currency'),
            'price' => isset($price) ? number_format($price, 2) : null,
            'gwp' => $gwp ?? null,
            'pwp' => $pwp ?? null,
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
