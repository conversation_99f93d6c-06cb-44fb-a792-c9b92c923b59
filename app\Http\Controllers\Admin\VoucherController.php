<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Users;
use App\Models\Products;
use App\Models\Constants;
use App\Models\UserVouchers;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use App\Models\RedemptionVouchers;
use App\Models\RedemptionVoucherProducts;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class VoucherController extends Controller
{
    function index()
    {
        if(!checkPermission('view_redemption_voucher')):
            return redirect(route('index'))->with('error', __('You do not have permission to view redemption voucher.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('redemptionVouchersDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('redemptionVouchersExport'),
                'filename' => 'redeemable_vouchers'
            ]
        ];

        if(!checkPermission('delete_redemption_voucher')):
            unset($bulk_action['delete']);
        endif;
        if(!checkPermission('export_redemption_voucher')):
            unset($bulk_action['export']);
        endif;

        $portal = Constants::portal;
        $membership_tiers = MembershipTiers::where('is_active', 1)->get();

        $moduleID = 'redemption_vouchers';
        return view('redemption-vouchers', compact('bulk_action', 'portal', 'membership_tiers', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = RedemptionVouchers::whereNull('deleted_at')
            ->when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%")
                        ->orWhere('code', 'LIKE', "%$search%");
                });
            })
            // ->whereIn('brand', userBrand())
            ->when($request->available_date, function ($query) use ($request) {
                $query->where(function($query)  use($request) {
                    $available_date = explode(" to ", $request->available_date);
                    $available_start_date = Carbon::parse(current($available_date))->format('Y-m-d');
                    $available_end_date = Carbon::parse(end($available_date))->format('Y-m-d');

                    $query->whereBetween('available_start_date', [$available_start_date, $available_end_date])
                        ->orWhereBetween('available_end_date', [$available_start_date, $available_end_date]);
                })
                ->orWhere(function($query) {
                    $query->whereNull('available_start_date')
                        ->whereNull('available_end_date');
                });
            })
            ->when($request->redemption_date, function ($query) use ($request) {
                $query->where(function($query)  use($request) {
                    $redemption_date = explode(" to ", $request->redemption_date);
                    $redemption_start_date = Carbon::parse(current($redemption_date))->format('Y-m-d');
                    $redemption_end_date = Carbon::parse(end($redemption_date))->format('Y-m-d');

                    $query->whereBetween('redemption_start_date', [$redemption_start_date, $redemption_end_date])
                        ->orWhereBetween('redemption_end_date', [$redemption_start_date, $redemption_end_date]);
                })
                ->orWhere(function($query) {
                    $query->whereNull('redemption_start_date')
                        ->whereNull('redemption_end_date');
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            ->when(isset($request->birthday) && $request->birthday != 'all', function($query) use ($request) {
                $query->where('is_birthday', $request->birthday);
            })
            ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->when(isset($request->type) && $request->type != 'all', function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->when($request->point_from, function($query) use ($request) {
                $query->where('redeem_points', '>=', $request->point_from);
            })
            ->when($request->point_to, function($query) use ($request) {
                $query->where('redeem_points', '<=', $request->point_to);
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());
        
        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="'.$item->id.'" class="check-selected form-check-input" data-datatable="redemption_vouchersList">
                    <span></span>
                </label>';

                $name = $item->name.'<div class="text-muted">'.$item->code.'</div>';

                $available_date = $redemption_date = '-';
                if ($item->available_date) {
                    $available_date = str_replace(' to ', '<br>to<br>', $item->available_date);
                }

                if ($item->redemption_date) {
                    $redemption_date = str_replace(' to' , '<br>to<br>', $item->redemption_date);
                }

                $available = $item->max_quantity ?? 0;
                $redeemed = $item->redeemed_quantity;
                if($item->fully_redeemed):
                    $redeemed .= '<br><span class="text-danger">'.__('Fully redeemed').'</span>';
                endif;

                if(!checkPermission('edit_redemption_voucher')):
                    $status = '<div class="text-center">'.($item->is_active == 1 ? __('Yes') : __('No')).'</div>';
                    $feautred = '<div class="text-center">'.($item->is_featured == 1 ? __('Yes') : __('No')).'</div>';

                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="enable"'.($item->is_active == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';

                    $feautred = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel='. $item->id.' type="checkbox" class="featured"'.($item->is_featured == 1 ? ' checked' : '').'>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $discount_type = '-';
                if ($item->discount_type) {
                    $discount_type = config('staticdata.discount_type')[$item->discount_type];
                }

                $discount_value = '-';
                if ($item->value != 0) {
                    $discount_value = $item->value;
                }

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(checkPermission('edit_redemption_voucher')):
                    $action .= '<a href="'.route('redemptionVouchersForm', $item->id).'" class="btn-icon btn-info mr-1" data-toggle="tooltip" data-title="'.__('Edit').'"><i class="fa fa-edit"></i></a>';
                endif;
                if(checkPermission('delete_redemption_voucher')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="'.$item->id.'" data-toggle="tooltip" data-title="'.__('Delete').'"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->name,
                    '<div class="text-center">'. $discount_type .'</div>',
                    '<div class="text-center">'.$discount_value.'</div>',
                    // config('staticdata.brand')[$item->brand] ?? '-',
                    // config('staticdata.voucher_type')[$item->type] ?? '-',
                    '<div class="text-center">'.$item->redeem_points.'</div>',
                    '<div class="text-center">'.$available.'</div>',
                    '<div class="text-center">'.$redeemed.'</div>',
                    // '<div class="text-center">'.$item->used_voucher_total.'</div>',
                    '<div class="text-center">'.$available_date.'</div>',
                    '<div class="text-center">'.$redemption_date.'</div>',
                    '<div class="text-center">'.($item->is_birthday ? __('Yes') : __('No')).'</div>',
                    $feautred,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(!checkPermission('edit_redemption_voucher') && !checkPermission('delete_redemption_voucher')):
                    unset($param[11]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function form(Request $request)
    {
        $id = $request->id ?? null;

        if(!$id):
            $title = __('Add Redemption Voucher');
            $result = new RedemptionVouchers();
        else:
            $title = __('Edit Redemption Voucher');
            $result = RedemptionVouchers::find($id);
            $result->membership_tiers = json_decode($result->membership_tiers);
        endif;

        $portal = Constants::portal;
        $membership_tiers = MembershipTiers::where('is_active', 1)->get();
        $data = [
            'title' => $title,
            'id' => $id,
            'result' => $result,
            'portal' => $portal,
            'membership_tiers' => $membership_tiers
        ];
        return view('redemption-voucher-form', $data);
    }

    function addUpdate(Request $request)
    {
        // if(!is_numeric($request->redeem_points)):
        //     return redirect(route('redemptionVouchers'))->with('error', __('Please enter valid points!'));
        // endif;
        // if($request->max_quantity && !is_numeric($request->max_quantity)):
        //     return redirect(route('redemptionVouchers'))->with('error', __('Please enter valid max. vouchers!'));
        // endif;

        $id = $request->id ?? null;
        if($id):
            $msg = __('Updated!');
            $query = RedemptionVouchers::find($id);
        else:
            $msg = __('Added!');
            $query = new RedemptionVouchers();
        endif;

        $available_date = convertDatePeriodToDbFormat($request->available_date ?? '');
        $redemption_date = convertDatePeriodToDbFormat($request->redemption_date ?? '');

        $query->name = $request->name;
        $query->code = $request->code ?? null;
        if($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;
        if($request->has('banner')):
            $query->banner = GlobalFunction::saveFileAndGivePath($request->banner);
        endif;

        if($request->has('list_image')):
            $query->list_image = GlobalFunction::saveFileAndGivePath($request->list_image);
        endif;

        $type = $request->type;
        if($type == 'delivery' || $type == 'discount' || !isset($type)) {
            $value = $request->value;
            $discount_type = $request->discount_type;
        }

        $query->value = $value ?? 0;
        $query->voucher_per_usage = $request->voucher_per_usage ?? 0;
        $query->discount_type = $discount_type ?? null;
        $query->description = $request->description;
        $query->redeem_points = $request->redeem_points ?? 0;
        $query->max_quantity = $request->max_quantity ?? 0;
        $query->balance_quantity = $request->max_quantity ?? 0;
        $query->available_start_date = $available_date['start_date'] ?? null;
        $query->available_end_date = $available_date['end_date'] ?? null;
        $query->redemption_start_date = $redemption_date['start_date'] ?? null;
        $query->redemption_end_date = $redemption_date['end_date'] ?? null;
        $query->portal = $request->portal ? json_encode($request->portal) : null;
        $query->membership_tiers = $request->membership_tier ? json_encode($request->membership_tier) : null;
        $query->first_time_purchase = $request->first_time_purchase ?? 0;
        $query->is_stackable = $request->is_stackable ?? 0;
        $query->is_featured = $request->is_featured ?? 0;
        $query->is_birthday = $request->is_birthday ?? 0;
        $query->is_active = $request->status ?? 0;
        $query->brand = $request->brand ?? null;
        $query->country = $request->country ?? null;
        $query->type = $request->type ?? null;
        $query->min_spend = $request->min_spend ?? null;
        $query->how_to_use = $request->how_to_use ?? null;
        $query->terms_condition = $request->terms_condition ?? null;
        $query->is_new_user = $request->is_new_user ?? 0;
        $query->is_auto_issuance = $request->is_auto_issuance ?? 0;
        $query->save();
        $id = $query->id;

        if($query->type == "free_product"):
            $exist = [];
            foreach($request->product_id as $key => $product_id):
                $voucher_product_id = $request->voucher_product_id[$key] ?? null;

                if(!$voucher_product_id):
                    $voucher_product = new RedemptionVoucherProducts();
                else:
                    $voucher_product = RedemptionVoucherProducts::find($voucher_product_id);
                endif;

                $voucher_product->redemption_voucher_id = $id;
                $voucher_product->product_id = $product_id;
                $voucher_product->variation_id = $request->variation_id[$key] ?? null;
                $voucher_product->quantity = $request->quantity[$key];
                $voucher_product->save();
                $exist[] = $voucher_product->id;
            endforeach;

            if($exist):
                RedemptionVoucherProducts::where('redemption_voucher_id', $id)
                    ->whereNotIn('id', $exist)
                    ->delete();
            else:
                RedemptionVoucherProducts::where('redemption_voucher_id', $id)->delete();
            endif;
        else:
            RedemptionVoucherProducts::where('redemption_voucher_id', $id)->delete();
        endif;

        return redirect(route('redemptionVouchersForm', $id))->with('message', $msg);
    }

    function delete(Request $request)
    {
        if($request->id):
            $query = RedemptionVouchers::find($request->id);
        else:
            $query = RedemptionVouchers::whereIn('id', $request->selected);
        endif;

        $query = $query->update(['deleted_at' => Carbon::now()]);

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = RedemptionVouchers::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function featuredUpdate(Request $request)
    {
        $item = RedemptionVouchers::find($request->id);
        $item->is_featured = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function detail($id, Request $request)
    {
        $data = RedemptionVouchers::find($id);
        if($data->image):
            $data->image = GlobalFunction::createMediaUrl($data->image);
        endif;
        if($data->banner):
            $data->banner = GlobalFunction::createMediaUrl($data->banner);
        endif;

        if($data->available_start_date):
            $available_start_date = Carbon::parse($data->available_start_date)->format('d-m-Y, g:ia');
            $available_end_date = Carbon::parse($data->available_end_date)->format('d-m-Y, g:ia');
            $data->available_date = $available_start_date.' to '.$available_end_date;
        endif;

        if($data->redemption_start_date):
            $redemption_start_date = Carbon::parse($data->redemption_start_date)->format('d-m-Y, g:ia');
            $redemption_end_date = Carbon::parse($data->redemption_end_date)->format('d-m-Y, g:ia');
            $data->redemption_date = $redemption_start_date.' to '.$redemption_end_date;
        endif;

        if($data->portal):
            $data->portal = json_decode($data->portal);
        endif;

        if($data->membership_tiers):
            $data->membership_tiers = json_decode($data->membership_tiers);
        endif;

        if($data->free_product):
            $data->free_product = json_decode($data->free_product);
        endif;

        if(isset($request->page)):
            $image = '-';
            if($data->image):
                $image = '<a href="'.$data->image.'" class="image-enlarge" target="_blank">
                        <img src="'.$data->image.'" class="border">
                    </a>';
            endif;

            $data = '<div class="row">
                <div class="col-md-6 form-group">
                    <label>Name</label>
                    <div>'.$data->name.'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Image</label>
                    <div>'.$image.'</div>
                </div>
                <div class="col-12 form-group">
                    <label>Description</label>
                    <div>'.nl2br($data->description).'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Redeem Points</label>
                    <div>'.formatNumber($data->redeem_points).'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Max. Vouchers</label>
                    <div>'.($data->max_quantity ? formatNumber($data->max_quantity) : 0).'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Redeemed Vouchers</label>
                    <div>'.($data->redeemed_quantity ? formatNumber($data->redeemed_quantity) : 0).'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Balance Vouchers</label>
                    <div>'.($data->balance_quantity ? formatNumber($data->balance_quantity) : 0).'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Voucher\'s Available Date</label>
                    <div>'.(isset($data->available_date) ? $data->available_date : '-').'</div>
                </div>
                <div class="col-md-6 form-group">
                    <label>Redemption Date</label>
                    <div>'.(isset($data->redemption_date) ? $data->redemption_date : '-').'</div>
                </div>
                <div class="col-12 form-group mb-0">
                    <label>Available for</label>
                    <div>'.(isset($selected_users) ? $selected_users : __('All Users')).'</div>
                </div>
            </div>';
        endif;

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'id';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'DESC';
        $result = RedemptionVouchers::whereNull('deleted_at')
            ->when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%")
                        ->orWhere('code', 'LIKE', "%$search%");
                });
            })
            ->when($request->available_date, function ($query) use ($request) {
                $query->where(function($query)  use($request) {
                    $available_date = explode(" to ", $request->available_date);
                    $available_start_date = Carbon::parse(current($available_date))->format('Y-m-d');
                    $available_end_date = Carbon::parse(end($available_date))->format('Y-m-d');

                    $query->whereBetween('available_start_date', [$available_start_date, $available_end_date])
                        ->orWhereBetween('available_end_date', [$available_start_date, $available_end_date]);
                })
                ->orWhere(function($query) {
                    $query->whereNull('available_start_date')
                        ->whereNull('available_end_date');
                });
            })
            ->when($request->redemption_date, function ($query) use ($request) {
                $query->where(function($query)  use($request) {
                    $redemption_date = explode(" to ", $request->redemption_date);
                    $redemption_start_date = Carbon::parse(current($redemption_date))->format('Y-m-d');
                    $redemption_end_date = Carbon::parse(end($redemption_date))->format('Y-m-d');

                    $query->whereBetween('redemption_start_date', [$redemption_start_date, $redemption_end_date])
                        ->orWhereBetween('redemption_end_date', [$redemption_start_date, $redemption_end_date]);
                })
                ->orWhere(function($query) {
                    $query->whereNull('redemption_start_date')
                        ->whereNull('redemption_end_date');
                });
            })
            // ->whereIn('brand', userBrand())
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->where('brand', $request->brand);
            // })
            ->when(isset($request->type) && $request->type != 'all', function($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->when($request->point_from, function($query) use ($request) {
                $query->where('redeem_points', '>=', $request->point_from);
            })
            ->when($request->point_to, function($query) use ($request) {
                $query->where('redeem_points', '>=', $request->point_to);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if($result->count() > 0):
            foreach($result as $item):
                if($item->image):
                    $item->image = GlobalFunction::createMediaUrl($item->image);
                endif;

                // if($item->banner):
                //     $item->banner = GlobalFunction::createMediaUrl($item->banner);
                // endif;

                if($item->available_start_date):
                    $available_start_date = Carbon::parse($item->available_start_date)->format('d-m-Y, g:ia');
                    $available_end_date = Carbon::parse($item->available_end_date)->format('d-m-Y, g:ia');
                    $item->available_date = $available_start_date.'<br>to<br>'.$available_end_date;
                endif;

                if($item->redemption_start_date):
                    $redemption_start_date = Carbon::parse($item->redemption_start_date)->format('d-m-Y, g:ia');
                    $redemption_end_date = Carbon::parse($item->redemption_end_date)->format('d-m-Y, g:ia');
                    $item->redemption_date = $redemption_start_date.'<br>to<br>'.$redemption_end_date;
                endif;

                if($item->portal):
                    $item->portal = implode('<br>', json_decode($item->portal));
                endif;

                // if($item->type =="free_product"){
                //     $product_list = '';
                //     foreach($item->voucher_products as $product):
                //         $product_name = $product->product->name;
                //         if($product->variation_id):
                //             $product_name .= ' - '.$product->variation_name;
                //         endif;
                //         $product_list .= $product_name.' x '.$product->quantity.'<br>';
                //     endforeach;
                //     $item->free_product = $product_list;
                // }else{
                //     $item->free_product= '-';
                // }

                if($item->type == 'discount' && $item->discount_type == 'fixed'){
                    $item->discount = global_settings('currency')." ".$item->value;
                }elseif($item->type == 'discount' && $item->discount_type == 'percentage'){
                    $item->discount = $item->value.'%';
                }else{
                    $item->discount = '-';
                }

                if($item->membership_tiers):
                    $item->membership_tiers = json_decode($item->membership_tiers);
                    $membership = MembershipTiers::whereIn('id', $item->membership_tiers)->get();
                    $selected_list = '';
                    foreach($membership as $tier):
                        $selected_list .= $tier->name.'<br>';
                    endforeach;
                    $item->membership_tiers = $selected_list;
                endif;

                $item->is_active = $item->is_active == 1 ? 'Yes' : 'No';
                // $item->first_time_purchase = $item->first_time_purchase == 1 ? 'Yes' : 'No';
                // $item->is_stackable = $item->is_stackable == 1 ? 'Yes' : 'No';
                $item->is_birthday = $item->is_birthday == 1 ? 'Yes' : 'No';
                $item->is_auto_issuance = $item->is_auto_issuance == 1 ? 'Yes' : 'No';
                $item->is_new_user = $item->is_new_user == 1 ? 'Yes' : 'No';
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;
        
        $fileName = 'redeemable-vouchers_'.date('ymd').'.xlsx';
        return Excel::download(
            new GeneralExport($result, 'redeemable_vouchers'),
            $fileName
        );
    }
}