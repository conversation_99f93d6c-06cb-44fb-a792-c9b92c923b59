<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Appointments;
use App\Models\Constants;
use App\Models\CronjobLogs as Cronjob;
use App\Models\GlobalFunction;

class AppointmentAutoCancelled extends Command
{
    protected $signature = 'appointment_auto_cancelled:cron';
    protected $description = 'update appointment to cancelled after certain time (set at dashboard)';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $appointments = Appointments::whereIn('status', [Constants::orderPlacedPending, Constants::orderWaitingConfirmation])->get();

        $cancelled = "";
        if(count($appointments) > 0):
            foreach($appointments as $appointment):
                $created_date = $appointment->created_at;
                $onhold_time = global_settings('appointment_cancel_duration');
                $onhold_time_unit = global_settings('appointment_cancel_unit');

                if($onhold_time_unit == "minutes"):
                    $created_date = $created_date->addMinutes($onhold_time);
                elseif($onhold_time_unit == "days"):
                    $created_date = $created_date->addDays($onhold_time);
                elseif($onhold_time_unit == "hours"):
                    $created_date = $created_date->addHours($onhold_time);
                endif;
                $created_date = $created_date->format("Y-m-d H:i:s");
                
                if($created_date < date("Y-m-d H:i:s")):
                    $appointment->status = Constants::orderCancelled;
                    $appointment->save();

                    // notification
                    $title = "Appointment :" . $appointment->appointment_number;
                    $notification_msg = "The appointment has been cancelled!";

                    // Push notification to user
                    $notify_data = [
                        'notify_type' => 'user',
                        'title' => $title,
                        'message' => $notification_msg,
                        'type' => 'appointment',
                        'type_id' => $appointment->id,
                        'user' => $appointment->user,
                    ];
                    GlobalFunction::sendNotification($notify_data);

                    // notification to doctor
                    $notify_data = [
                        'notify_type' => 'doctor',
                        'title' => $title,
                        'message' => $notification_msg,
                        'type' => 'appointment',
                        'type_id' => $appointment->id,
                        'user' => $appointment->doctor,
                    ];
                    GlobalFunction::sendNotification($notify_data);
                    // END notification

                    $cancelled .= $appointment->id."|".$appointment->appointment_number.", ";
                endif;
            endforeach;

            $cancelled = substr($cancelled, 0, -2);
        endif;

        // cronjob log
        if(isset($cancelled) && $cancelled):
            Cronjob::create([
                "type" => "appointment auto cancelled",
                "recipient" => $cancelled
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}