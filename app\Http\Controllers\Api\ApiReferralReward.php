<?php

namespace App\Http\Controllers\Api;

use App\Models\ReferredUser;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Carbon\Carbon;

class ApiReferralReward extends Controller
{
    public function listing(Request $request)
    {
        // filters
        $type = $request->type;
        $per_page = $request->per_page ?? 10;

        $data = [];

        $loggedUser = auth()->user();
        $referredUsers = ReferredUser::where('referrer_id', $loggedUser->id)->orderBy('created_at', 'desc')->get();

        foreach ($referredUsers as $key => $referredUser) {
            if (!is_null($referredUser->rewards)) {
                $rewardsDecoded = json_decode($referredUser->rewards, true);
                $rewards = implode(' / ', array_map(function ($reward) {
                    return $reward['name'];
                }, $rewardsDecoded));
            }

            $checkFirstOrder = Order::where('user_id', $referredUser->referred_id)->where('status', 'completed')->first();

            $data[] = [
                'name' => $referredUser->referred->name,
                'date' => Carbon::parse($referredUser->created_at)->format('d/m/Y'),
                'rewards' => $rewards,
                'status' => isset($checkFirstOrder) ? 'Completed' : 'Pending'
            ];
        }

        if ($type == 'purchased') {
            $data = array_filter($data, function ($item) {
                return $item['status'] == 'Completed';
            });
        } elseif ($type == 'signed') {
            $data = array_filter($data, function ($item) {
                return $item['status'] == 'Pending';
            });
        }

        $data = collect($data)->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
