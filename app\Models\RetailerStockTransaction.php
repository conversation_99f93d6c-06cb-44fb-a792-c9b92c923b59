<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\RetailerProduct;

class RetailerStockTransaction extends Model
{
    use HasFactory;

    public function retailer()
    {
        return $this->belongsTo(Retailer::class, 'retailer_id', 'user_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(RetailerProduct::class, 'product_id', 'product_id');
    }

    public function getTypeTextAttribute()
    {
        return config('staticdata.retailer_stock_type')[$this->type] ?? null;
    }

    public function getProductAttribute()
    {
        $product_id = $this->product_id;
        $variation_id = $this->variation_id ?? null;

        $product = RetailerProduct::where('product_id', $product_id)
            ->when($variation_id, function ($query) use ($variation_id) {
                return $query->where('variation_id', $variation_id);
            })
            ->first();

        return (object) [
            'product_name' => $product->product_name,
            'variation_name' => $product->variation_name ?? null,
        ];
    }
}
