<?php

namespace App\Models;

use App\Models\Country;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserAddressBook extends Model
{
    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class,'country','id');
    }

    public function getFullAddressAttribute()
    {
        $country_name = Country::find($this->country)->name;
        return "{$this->address}, {$this->postcode} {$this->city}, {$this->state}, {$country_name}";
    }

}
