<?php

namespace App\Console\Commands;

use DateTime;
use App\Models\Order;
use App\Models\Users;
use App\Models\UserMembership;
use App\Models\MembershipTiers;
use Illuminate\Console\Command;
use App\Models\CronjobLogs as Cronjob;
use App\Notifications\ExpiredNotification;
use Illuminate\Support\Facades\Notification;

class MembershipTierExpired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'membership_tier_expired:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if user membership has expired';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = Users::where('is_active', 1)
            ->whereNull('deleted_at')
            ->get();

        $recipient = [];
        foreach ($users as $user) {
            // get user's brand basic tier
            $brand = $user->brand;
            $basic_tier = MembershipTiers::whereJsonContains('brand', $brand)->orderby('point_from', 'asc')->first();

            // if user' tier is lowest then ignore
            if ($basic_tier && ($user->membership_tier != $basic_tier->id)) {
                $downgrade = false;
                $current_tier = $user->membership_tiers;
                $user_memberships = UserMembership::where('user_id', $user->id)->where('membership_id', $current_tier->id)->first();

                if ($user_memberships) {
                    $today = new DateTime();

                    if ($today > $user_memberships->expiry_date) {
                        $min_purchase = $current_tier->min_purchase;
                        $total_orders = 0;

                        $orders = Order::where('user_id', $user->id)->where('status', '!=', 'cancelled')->get(); // get total orders, where not cancelled
                        if ($orders) {
                            $total_orders = count($orders);
                        }
        
                        if ($total_orders < $min_purchase) {
                            // expired and didnt reach min purchase
                            $downgrade = true;
                        }
                        // expired but reach min purchase
                    }
                }
                // Not expired yet

                if ($downgrade) {
                    // Downgrade
                    $next_lower_tier = MembershipTiers::whereJsonContains('brand', $brand)->where('point_from', '<', $current_tier->point_from)->orderBy('point_from', 'desc')->first();

                    // in some cases data error cant find lower tier
                    if ($next_lower_tier) {
                        $user->membership_tier = $next_lower_tier->id;
                        $user->save();
                        
                        $membership_name = 'Membership ' . $current_tier->name . ' tier';
    
                        // send whatsapp
                        Notification::route('whatsapp', $user->phone_number)
                            ->notify(new ExpiredNotification($user, $membership_name, 'Membership Tier'));

                        $recipient[] = $user->id.": ".$user->phone_number;
                    }
                }
                // No need to downgrade
            }
        }

        // cronjob log
        if($recipient):
            Cronjob::create([
                "type" => "members tier expired",
                "recipient" => implode(', ', $recipient),
            ]);
        endif;
        // END cronjob log

        return 0;
    }
}
