<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Users;
use App\Models\Constants;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\UserPointRedemptions;
use App\Models\UserPointTransaction;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Admin\UsersController;
use App\Traits\PointsTraits;

class PointController extends Controller
{
    use PointsTraits;

    function point()
    {
        if (!checkPermission('view_point_transactions')):
            return redirect(route('index'))->with('error', __('You do not have permission to view point transactions.'));
        endif;

        $filter_user = (new UsersController)->usersDropdownList('All Members');
        $filter_user = $filter_user->getData()->data;

        $user_list = (new UsersController)->usersDropdownList('Select Member');
        $user_list = $user_list->getData()->data;

        $filter_type = config('staticdata.point.type');
        $filter_point_prefix = config('staticdata.point.prefix');
        $filter_status = config('staticdata.point.status');

        $moduleID = 'points';
        return view('point', compact('filter_user', 'user_list', 'filter_type', 'filter_point_prefix', 'filter_status', 'moduleID'));
    }

    function pointList(Request $request)
    {
        $sort_col = $request->sort_col ?? 'updated_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');
        $user = $request->user;
        $points_prefix = $request->points_prefix;
        $point_date = $request->date;
        $point_type = $request->type;
        $point_status = $request->status;
        $brand = $request->brand;
        $expired_date = $request->expired_date;

        $result = UserPointTransaction::with(['user', 'voucher', 'order'])
            ->when($search, function ($q) use ($search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('order_no', 'LIKE', "%{$search}%");
                    })
                    ->orWhereHas('voucher', function ($q) use ($search) {
                        $q->where('voucher_code', 'LIKE', "%{$search}%");
                    });
                });
            })
            // ->whereHas('user', function ($q) use ($user) {
            //     $q->whereIn('brand', userBrand());
            // })
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->when($points_prefix, function ($q) use ($points_prefix) {
                $q->where('points_prefix', $points_prefix);
            })
            ->when($point_date, function ($q) use ($point_date) {
                $date = explode(" to ", $point_date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->when($point_type, function ($q) use ($point_type) {
                $q->where('type', $point_type);
            })
            ->when($point_status, function ($q) use ($point_status) {
                $q->where('status', $point_status);
            })
            // ->when($brand && $brand != 'all', function ($q) use ($brand) {
            //     $q->where('brand', $brand);
            // })
            ->when($expired_date, function ($q) use ($expired_date) {
                $date = explode(" to ", $expired_date);
                $q->whereDate('expired_date', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('expired_date', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            // summary
            $summary_result = UserPointTransaction::when($search, function ($q) use ($search) {
                    $q->where(function ($q) use ($search) {
                        $q->whereHas('order', function ($q) use ($search) {
                            $q->where('order_no', 'LIKE', "%{$search}%");
                        })
                        ->orWhereHas('voucher', function ($q) use ($search) {
                            $q->where('voucher_code', 'LIKE', "%{$search}%");
                        });
                    });
                })
                ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                    $q->where('user_id', $request->user);
                })
                ->when($point_date, function ($q) use ($point_date) {
                    $date = explode(" to ", $point_date);
                    $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                        ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
                })
                ->when($point_type, function ($q) use ($point_type) {
                    $q->where('type', $point_type);
                })
                ->when($point_status, function ($q) use ($point_status) {
                    $q->where('status', $point_status);
                })
                ->get();

            $add_points = $summary_result->where('points_prefix', '+')->sum('points');
            $deduct_points = $summary_result->where('points_prefix', '-')->sum('points');
            $points_balance = $add_points - $deduct_points;
            // END summary

            foreach ($result as $count => $item):
                $type = $item->type;
                $type_detail = config('staticdata.point.type.'.$type) ?? '';
                if($type == 'booking'):
                    $type_detail .= '<div class="text-grey">'.$item->booking->ref_no.'</div>';
                elseif($type == 'order'):
                    $type_detail .= '<div class="text-grey">'.($item->order->order_no ?? '').'</div>';
                elseif($type == 'redeem_voucher'):
                    $type_detail .= '<div class="text-grey">' . ($item->voucher ? $item->voucher->voucher_code : '') . '</div>';
                endif;

                $user = "<div>" . $item->user->name . '<div class="text-grey">' . $item->user->phone_number . "</div></div>";

                // $expired_date = '';
                // if ($item->expired_date && $item->point_expiry_balance > 0):
                //     $expired_date = '<div class="text-danger">';

                //     if ($item->is_expired):
                //         $expired_date .= __('Expired on');
                //     else:
                //         $expired_date .= __('Expires on');
                //     endif;
                //     $expired_date .= " ".Carbon::parse($item->expired_date)->format('d-m-Y');

                //     $expired_date .= '</div>';
                // endif;

                $date = "<div class='text-center'><span style='display:none'>".$item->updated_at."</span>" . Carbon::parse($item->updated_at)->format('d-m-Y') . '<br>' . Carbon::parse($item->updated_at)->format('g:i a') . "</div>";

                $action = '<div class="d-flex flex-wrap justify-content-center">
                    <a href="' . route('pointDetail', $item->id) . '" class="btn-icon btn-info" data-toggle="tooltip" data-title="' . __('View') . '"><i class="fa fa-search"></i></a>
                </div>';

                if ($count == 0):
                    $date .= '<input type="hidden" name="pointBalance" value="' . formatNumber($points_balance) . '">';
                    $date .= '<input type="hidden" name="addPoints" value="' . formatNumber($add_points) . '">';
                    $date .= '<input type="hidden" name="deductPoints" value="' . formatNumber($deduct_points) . '">';
                endif;

                $point = "<div>" . $item->points_prefix . " " . formatNumber($item->points) . $expired_date . "</div>";

                $param = [
                    // config('staticdata.brand.'.$item->brand),
                    $date,
                    $user,
                    $type_detail,
                    $point,
                    __($item->status),
                    "<div class='text-right'>" . formatNumber($item->available_points ?? $item->points) . "</div>",
                    $action
                ];

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data,
        ];

        echo json_encode($json_data);
        exit();
    }

    function pointUpdate(Request $request)
    {
        $point_data = [
            'user_id' => $request->user,
            'type' => 'adjustment',
            'type_id' => null,
            'points_prefix' => $request->points_prefix,
            'points' => $request->points,
            'remarks' => $request->remarks,
            'status' => 'ready',
            'deduct_accumulated' => true,
            'brand' => $request->brand,
        ];
        $this->insertPointTransaction($point_data);

        return GlobalFunction::sendSimpleResponse(true, 'Point Updated!');
    }

    function getUserPoint(Request $request)
    {
        $point_balance = 0;

        if($request->id){
            $point_balance = User::find($request->id)->point_balance;
        }
        $data = ["point_balance" => $point_balance];
        return $data;
    }

    function pointExport(Request $request)
    {
        $sort_col = $request->sort_col ?? 'updated_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $search = $request->input('search.value');
        $user = $request->user;
        $points_prefix = $request->points_prefix;
        $point_date = $request->date;
        $point_type = $request->type;
        $point_status = $request->status;

        $result = UserPointTransaction::with(['user', 'voucher', 'order'])
            ->when($search, function ($q) use ($search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('order_no', 'LIKE', "%{$search}%");
                    })
                    ->orWhereHas('voucher', function ($q) use ($search) {
                        $q->where('voucher_code', 'LIKE', "%{$search}%");
                    });
                });
            })
            // ->whereIn('brand', userBrand())
            ->when(is_numeric($request->user) && $request->user > 0, function ($q) use ($request) {
                $q->where('user_id', $request->user);
            })
            ->when($points_prefix, function ($q) use ($points_prefix) {
                $q->where('points_prefix', $points_prefix);
            })
            ->when($point_date, function ($q) use ($point_date) {
                $date = explode(" to ", $point_date);
                $q->whereDate('created_at', '>=', Carbon::parse(current($date))->format('Y-m-d'))
                    ->whereDate('created_at', '<=', Carbon::parse(end($date))->format('Y-m-d'));
            })
            ->when($point_type, function ($q) use ($point_type) {
                $q->where('type', $point_type);
            })
            ->when($point_status, function ($q) use ($point_status) {
                $q->where('status', $point_status);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $type = $item->type;
                $item->type = config('staticdata.point.type.'.$type) ?? '';
                if($type == 'booking'):
                    $item->type_ref = $item->booking->ref_no;
                elseif($type == 'order'):
                    $item->type_ref = $item->order->order_no;
                elseif($type == 'redeem_voucher'):
                    $item->type_ref = $item->voucher->voucher_code;
                endif;

                $item->date = Carbon::parse($item->updated_at)->format('d-m-Y g:i a');

                if ($item->expired_date && $item->point_expiry_balance > 0):
                    $item->expired_date = Carbon::parse($item->expired_date)->format('d M Y');
                else:
                    $item->expired_date = '';
                endif;

                $item->points_prefix = $item->points_prefix == '+' ? 'Earned' : 'Spent';
            endforeach;
        endif;

        $fileName = 'user_points_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'point'),
            $fileName
        );
    }

    function pointDetail($id)
    {
        if (!checkPermission('view_point_transactions')):
            return redirect(route('index'))->with('error', __('You do not have permission to view point transactions.'));
        endif;

        $point_transaction = UserPointTransaction::with(['user', 'voucher', 'booking'])->find($id);


        return view('point-transaction-details', compact('point_transaction'));
    }

    function pointRedemptions()
    {
        if (!checkPermission('view_point_redemptions')):
            return redirect(route('index'))->with('error', __('You do not have permission to view point redemptions.'));
        endif;

        $filter_user = (new UsersController)->usersDropdownList('All Users');
        $filter_user = $filter_user->getData()->data;

        $filter_type = [
            Constants::userPointRedemptionAvailable,
            Constants::userPointRedemptionRedeemed,
            Constants::userPointRedemptionExpired,
        ];

        return view('point-redemptions', compact('filter_user', 'filter_type'));
    }

    function pointRedemptionsList(Request $request)
    {
        $status = $request->status ?? '';
        $user = $request->user;
        $result = UserPointRedemptions::with(['user']);

        if ($request->input('search.value')):
            $search = $request->input('search.value');
            $result = $result->where(function ($q) use ($search) {
                $q->whereHas('redemption_voucher', function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%");
                })
                    ->orWhere('redeem_code', 'LIKE', "%{$search}%");
            });
        endif;
        if ($user):
            $result = $result->where('user_id', $user);
        endif;
        if ($request->date):
            $date_exp = explode(" to ", $request->date);
            $result = $result->whereDate('created_at', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                ->whereDate('created_at', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
        endif;
        if ($status):
            $result = $result->where('status', $status);
        endif;
        if ($request->sort_col):
            $result = $result->orderBy($request->sort_col, $request->sort_by);
        else:
            $result = $result->orderBy('id', 'DESC');
        endif;
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->length;
            $start = $request->start;
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $voucher = $item->redemption_voucher->name . '
                    <a href="" class="btn-modal-detail" data-url="' . route("redemptionVouchersDetail", $item->voucher_id) . '" data-toggle="tooltip" data-title="' . __('View') . '"><i class="fa fa-info-circle text-warning font-16"></i></a>
                    <div>
                        <span class="text-grey">' . __('Redeem Code:') . '</span> ' . $item->redeem_code . '
                    </div>';

                $user = "";
                if (!$request->userPage):
                    $user = $item->user->first_name . ' ' . $item->user->last_name;
                endif;

                $param = [
                    $user,
                    $voucher,
                    "<div class='text-center'>" . formatNumber($item->point_used) . "</div>",
                    "<div class='text-center'>" . $item->status . "</div>",
                    "<div class='text-center'>" . Carbon::parse($item->created_at)->format('d-m-Y') . '<br>' . Carbon::parse($item->created_at)->format('g:i a') . "</div>",
                ];

                if ($request->userPage):
                    unset($param[0]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function pointRedemptionsExport(Request $request)
    {
        $status = $request->status ?? '';
        $user = $request->user;
        $result = UserPointRedemptions::with(['user']);

        if ($request->search):
            $search = $request->search;
            $result = $result->where(function ($q) use ($search) {
                $q->whereHas('redemption_voucher', function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%");
                });
            });
        endif;
        if ($user):
            $result = $result->where('user_id', $user);
        endif;
        if ($request->date):
            $date_exp = explode(" to ", $request->date);
            $result = $result->whereDate('created_at', '>=', Carbon::parse(current($date_exp))->format('Y-m-d'))
                ->whereDate('created_at', '<=', Carbon::parse(end($date_exp))->format('Y-m-d'));
        endif;
        if ($status):
            $result = $result->where('status', $status);
        endif;
        $result = $result->orderBy('id', 'DESC')->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->points = formatNumber($item->point_used);
                $item->user_name = $item->user->fullname;
                $item->date = Carbon::parse($item->created_at)->format('d-m-Y g:i a');
            endforeach;
        endif;

        $fileName = 'point_redemptions_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'point_redemptions'),
            $fileName
        );
    }
}
