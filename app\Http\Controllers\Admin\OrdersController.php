<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Admin;
use App\Models\Order;
use App\Models\Users;
use App\Models\Country;
use App\Models\Outlets;
use App\Models\Products;
use App\Models\Retailer;
use App\Traits\OrderTraits;
use App\Models\OrderBundles;
use App\Models\SalesChannel;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\UserMembership;
use App\Models\ProductCategory;
use App\Models\UserAddressBook;
use App\Services\LalamoveService;
use App\Models\PaymentTransactions;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\PaymentGatewaysTraits;

class OrdersController extends Controller
{
    use OrderTraits;
    use PaymentGatewaysTraits;

    function index()
    {
        if(!checkPermission('view_orders')):
            return redirect(route('index'))->with('error', __('You do not have permission to view order.'));
        endif;

        $bulk_action = [
            // 'export' => [
            //     'text' => __('Export Selected'),
            //     'url' => route('orderExport'),
            //     'filename' => 'orders'
            // ]
        ];

        if(!checkPermission('export_orders')):
            unset($bulk_action['export']);
        endif;

        $status_list = config('staticdata.order_status');
        $payment_method_list = config('staticdata.payment_method');

        if(is_retailer()):
            unset($status_list['pending_approval'], $status_list['approved'], $status_list['rejected']);
        endif;
        $sales_channels = SalesChannel::orderBy('title', 'asc')->get();
        $moduleID = 'orders';
        return view('orders', compact('bulk_action', 'moduleID', 'status_list', 'sales_channels','payment_method_list'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Order::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('order_no', 'LIKE', "%$search%")
                        ->orWhere('name', 'LIKE', "%$search%")
                        ->orWhere('phone_number', 'LIKE', "%$search%")
                        ->orWhere('tracking_number', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->brand, function($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->where('country', $request->country);
            })
            ->when($request->type, function($q) use ($request) {
                $q->where('user_type', $request->type);
            })
            ->when($request->channel, function($q) use ($request) {
                $q->where('sales_channel_id', $request->channel);
            })
            ->where(
            //     is_retailer(), function ($query) use ($request) {
            //     // when retailer login
            //     $query->where(function($q) {
            //         $q->where('retailer_id', session('user')->user_id)
            //             ->orWhere('user_id', session('user')->user_id);
            //     })
            //     ->whereNotIn('status', ['pending_approval', 'approved', 'rejected']);
            // }, 
            function ($q) use ($request) {
                // when admin login
                // $q->where('user_type', '!=', 'retailer_customer');

                if($request->type == 'customer' && is_numeric($request->user) && $request->user > 0):
                    $q->where('user_id', $request->user);
                elseif($request->type == 'retailer'):
                    if(is_numeric($request->retailer) && $request->retailer > 0):
                        $q->where('user_id', $request->retailer);
                    endif;

                    if($request->payment):
                        $q->where('payment_method', $request->payment);
                    endif;
                endif;
            })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item) {
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="ordersList">
                    <span></span>
                </label>';

                if(is_retailer() && $item->user_type == 'retailer'):
                    $item->name = 'Self';
                endif;

                if($item->user_type == 'retailer'):
                    $payment_method = config('staticdata.retailer_type.'. $item->payment_method);
                else:
                    $payment_method = $item->payment_method;
                endif;

                $payment = $item->payment;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if(!checkPermission('edit_orders') || (is_retailer() && $item->user_type == 'retailer')):
                    $action .= '<a href="'.route('orderForm', ['id' => $item->id]).'" class="btn-icon btn-info" data-toggle="tooltip" data-title="' . __('View') . '" style="padding-left:7px"><i class="fa fa-eye"></i></a>';
                elseif(checkPermission('edit_orders')):
                    $action .= '<a href="'.route('orderForm', ['id' => $item->id]).'" class="btn-icon btn-info" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $item->order_no,
                    // ucwords($item->user_type),
                    $item->name,
                    // config('staticdata.brand.'. $item->brand),
                    // config('staticdata.country.'. $item->country),
                    $payment_method,
                    $payment->status ?? '-', 
                    $item->delivery_method,
                    '<div class="text-right">'.number_format($item->total, 2).'</div>',
                    '<div class="text-right">'. config('staticdata.order_status.'. $item->status).'</div>',
                    // $item->sales_channel,
                    $item->tracking_number,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(is_retailer()):
                    unset($param[2]);
                endif;
                if(!checkPermission('edit_orders')):
                    unset($param[10]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            }
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];
        
        echo json_encode($json_data);
        exit();
    }

    function ratingIndex()
    {
        if(!checkPermission('view_order_ratings')):
            return redirect(route('index'))->with('error', __('You do not have permission to view order rating.'));
        endif;

        $bulk_action = [
            // 'export' => [
            //     'text' => __('Export Selected'),
            //     'url' => route('orderExport'),
            //     'filename' => 'orders'
            // ]
        ];

        if(!checkPermission('export_orders')):
            unset($bulk_action['export']);
        endif;

        $order_ratings = config('staticdata.order_rating');
        $moduleID = 'order_ratings';
        return view('order_ratings', compact('bulk_action', 'moduleID', 'order_ratings'));
    }

    function ratingListing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Order::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('order_no', 'LIKE', "%$search%")
                        ->orWhere('name', 'LIKE', "%$search%")
                        ->orWhere('phone_number', 'LIKE', "%$search%");
                });
            })
            ->when($request->rating, function($query) use ($request) {
                $query->where('rating', $request->rating);
            })
            ->where('rating', '>', 0)
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item) {
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="ordersList">
                    <span></span>
                </label>';

                $order_no = '<a style="text-decoration: none" class="text-black" href="' . route('orderForm', ['id' => $item->id]) . '">' . $item->order_no . '</a>';


                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('delete_order_ratings')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $param = [
                    $checkbox,
                    $order_no,
                    $item->name,
                    $item->phone_number,
                    $item->rating,
                    $item->review,
                    $action
                ];

                if($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if(is_retailer()):
                    unset($param[2]);
                endif;
                if(!checkPermission('edit_orders')):
                    unset($param[9]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            }
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function ratingDelete(Request $request)
    {
        if ($request->id):
            $query = Order::find($request->id);
        else:
            $query = Order::whereIn('id', $request->selected);
        endif;

        $query = $query->update(['rating' => 0, 'review' => '']);

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function form(Request $request)
    {
        $id = $request->id ?? null;
        $user = session('user');

        $is_retailer = is_retailer();
        $user_types = config('staticdata.user_types');
        $status_list = config('staticdata.order_status');
        $payment_method_list = config('staticdata.payment_method');

        if($is_retailer || !checkPermission('approval_orders')):
            unset($status_list['pending_approval'], $status_list['approved'], $status_list['rejected']);
        endif;
        $countries = Country::all();
        $sales_channels = SalesChannel::orderBy('title', 'asc')->get();
        $store_locators = Outlets::where('is_active', true)->get();
        $product_categories = ProductCategory::where('is_active', true)->get();

        $tax = global_settings('tax') /100;

        if ($id):
            $result = Order::find($id);
            $payment = $result->payment;
            if(!$result):
                return redirect(route('order'))->with('error', __('Order not found!'));
            endif;
            $title = __('Update Order').': '.$result->order_no;

            // $result->currency = ($result->country == 'malaysia' ? global_settings('currency') : global_settings('currency_sg'));
            $result->currency = 'RM ';
            $result->receipt = GlobalFunction::createMediaUrl($result->receipt);
            
            if($result->user_type == 'retailer'):
                $result->payment_method = config('staticdata.retailer_type.'. $result->payment_method);
            endif;

            $result->bundles = null;
            if($result->bundle_items->count() > 0):
                $bundle_list = $result->bundle_items->groupBy('order_bundle_id')->toArray();
                $result->bundles = OrderBundles::whereIn('id', array_keys($bundle_list))->get();
            endif;
        else:
            $title = __('Create Order');
            $result = new Order();
            $result->user_type = '';
            $result->status = 'pending';
            $result->address_country = 129;
            $result->currency = global_settings('currency');
        endif;

        $moduleID = 'orders';
        $data = [
            'title' => $title,
            'moduleID' => $moduleID,
            'id' => $id,
            'user_types' => $user_types,
            'status_list' => $status_list,
            'payment_method_list' => $payment_method_list,
            'is_retailer' => $is_retailer,
            // 'order_type' => $result->user_type ?? '',
            'result' => $result,
            'payment' => $payment ?? '',
            'countries' => $countries,
            'sales_channels' => $sales_channels,
            'store_locators' => $store_locators,
            'product_categories' => $product_categories,
            'tax' => $tax
        ];
        return view('order-form', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        $msg = ($id ? __('Updated!') : __('Created!'));
        $user_type = $request->user_type;
        $user_id = $request->user_id ?? null;
        $retailer_id = $request->retailer_id ?? null;
        $quotation_id = $request->quotation_id ?? null;
        $order_total_weight = 0 ;

        $is_retailer = is_retailer();
        if($is_retailer):
            $user_type = 'retailer_customer';
        endif;

        $order_form = route('orderForm', ['id' => $id]);
        $order_data = (object) $request->all();
        if(!$id):
            // if (!$user_type):
            //     return redirect($order_form)->with('error', __('Please select customer type.'.$user_type));
            // endif;
            if (!$request->products):
                return redirect($order_form)->with('error', __('Please select at least one product.'));
            endif;

            $check = new Request([
                'products' => $request->products,
                'postcode' => $request->postcode,
                'quotation_id' => $quotation_id,
            ]);

            if($request->delivery_method == 'delivery' && $request->postcode):
                $delivery_check = $this->checkDeliveryAvailability($check);
                if(!$delivery_check['success']):
                    return redirect($order_form)->with('error', $delivery_check['message']);
                endif;
            endif;

            // retailer portal - new customer
            if($is_retailer && !$user_id):
                // check if exist
                $phone_prefix = ($request->country == 'malaysia' ? '+60' : '+65');
                $phone_number = $phone_prefix . $request->phone_number;
                $exist = Users::where('phone_number', $phone_number)
                    ->where('brand', $request->brand)
                    ->whereNull('deleted_at')
                    ->first();
                
                if(!$exist):
                    $user = new Users();
                    $user->name = $request->name;
                    $user->brand = $request->brand;
                    $user->country = $request->country;
                    $user->email_address = $request->email_address ?? null;
                    $user->phone_number = $phone_number;
                    $user->is_notification = 1;
                    $user->is_active = 1;
                    $user->is_verified = 1;
                    $user->referral_code = \Str::random(8);

                    $lowest_tier = getLowestMembershipTier($request->brand);
                    $user->membership_tier = $lowest_tier->id ?? 0;
                    $user->save();
                    $user_id = $user->id;

                    // create membership record
                    if($lowest_tier):
                        UserMembership::create([
                            'user_id' => $user_id,
                            'membership_id' => $lowest_tier->id ?? 0,
                            'remaining_maintain_purchase' => $lowest_tier->min_purchase ?? 0,
                            'start_date' => Carbon::now()->format(config('app.db_date_format')),
                            'expiry_date' => Carbon::now()->addMonth($lowest_tier->expiration_month)->format(config('app.db_date_format')),
                        ]);
                    endif;
                else:
                    $user_id = $exist->id;
                endif;
            endif;
            // END retailer portal - new customer

            if ($user_type == 'retailer'):
                // admin create order for retailer
                if (!$retailer_id):
                    return redirect($order_form)->with('error', __('Please select retailer.'));
                endif;

                $user = Retailer::where('user_id', $retailer_id)->first();
                $billing_user_name = $user->first_name;
                $billing_user_email = $user->user_name;
                $billing_user_phone = $user->phone_number;
                $payment_method = $user->type;
                $retailer_id = null;
                $user_id = $request->retailer_id;
            else:
                // either admin create order for customer or retailer create order for customer
                if (!$is_retailer && !$user_id):
                    return redirect($order_form)->with('message', __('Please select customer.'));
                endif;

                if($user_id):
                    $user = Users::find($user_id);
                    $billing_user_name = $user->name;
                    $billing_user_email = $user->email_address;
                    $billing_user_phone = $user->phone_number;
                    $brand = $user->brand;
                    $country = $user->country;
                endif;

                if($is_retailer):
                    $user_type = "retailer_customer";
                    $retailer_id = session('user')->user_id;

                    $billing_user_name = $request->name;
                    $billing_user_email = $request->email_address;
                endif;
            endif;

            $order_data->user_type = $user_type ?? 'customer';
            if(!isset($order_data->brand)):
                $order_data->brand = $brand ?? null;
            endif;
            if(!isset($order_data->country)):
                $order_data->country = $country ?? null;
            endif;
            if($order_data->user_type == 'retailer'):
                $order_data->payment_method = $payment_method;
            endif;
            $order_data->user_id = $user_id;
            $order_data->retailer_id = $retailer_id ?? null;
            $order_data->billing_user_name = $billing_user_name;
            $order_data->billing_user_email = $billing_user_email;
            $order_data->billing_user_phone = $billing_user_phone;
            $order_data->created_by = session('user')->user_id ?? null;

            $product_list = [];
            $product_total_price = 0;
            $product_total_tax = 0;
            $subtotal = 0;
            $total_tax = 0;
            foreach($order_data->products as $key => $item):
                if($key != 'new_row'):
                    $item_id = $item['id'] ?? null;
                    $product_id = $item['product_id'];
                    $variation_id = $item['variation_id'] ?? null;
                    $quantity = $item['quantity'];
                    $unit_price = $item['price'];
                    $unit_tax = $item['tax'];
                    $weight = $item['weight'] ?? 0;
                    if(!is_numeric($variation_id)):
                        $variation_id = 0;
                    endif;

                    if(!$item_id):
                        $product_result = Products::find($product_id);
                        if($variation_id && $variation_id > 0):
                            $variation_result = $product_result->variations->where('id', $variation_id)->first();
                            $variation_name = $variation_result->variation_name ?? '';
                            $variation_sku = $variation_result->sku ?? '';
                            $image = $variation_result->image_url ?? '';
                        else:
                            $variation_name = '';
                            $variation_sku = '';
                            $image = $product_result->single_image_url;
                        endif;
                    endif;

                    if($item['is_product_weight']){
                        $product_total_price = $unit_price * $quantity;
                    }else{
                        $product_total_price = $unit_price * $quantity * $weight;
                    }

                    $product_total_tax = $unit_tax * $quantity;
                    $total_weight = $weight * $quantity;
                    $subtotal += $product_total_price;
                    $total_tax += $product_total_tax;
                    $order_total_weight += $total_weight;
                    $product_list[] = [
                        'id' => $item_id,
                        'product_id' => $product_id,
                        'product_name' => $product_result->name ?? '',
                        'product_sku' => $product_result->sku ?? '',
                        'image' => $image ?? '',
                        'variation_id' => $variation_id,
                        'variation_name' => $variation_name ?? '',
                        'variation_sku' => $variation_sku ?? '',
                        'quantity' => $quantity,
                        'unit_price' => $unit_price,
                        'unit_tax' => $unit_tax,
                        'total_price' => $product_total_price,
                        'total_tax' => $product_total_tax,
                        'unit_weight' => $weight,
                        'total_weight' => $total_weight
                    ];
                endif;
            endforeach;
            $order_data->products = $product_list;
            $order_data->subtotal = $subtotal;
            $order_data->tax = $total_tax;

            if($quotation_id){
                $order_data->lalamove = 1;
                $order_data->total_weight = $order_total_weight;
            }

            $total_data = $this->priceCalculation($order_data);
            $order_data->subtotal = $total_data['subtotal'];
            $order_data->tax = $total_data['tax'];
            $order_data->shipping_fee = $total_data['shipping_fee'];
            $order_data->voucher_amount = $total_data['voucher'];
            $order_data->total_amount = $total_data['total'];
            $order_data->quotation_id = $total_data['quotation_id'];
            $order_data->delivery_vehicle = $order_data->delivery_method == 'delivery' ? $order_data->delivery_vehicle :'';

            if($order_data->address_country == 129):
                $order_data->state = $request->state_my;
            endif;
        endif;

        if(!checkPermission('approval_orders') && $user_type == 'retailer' && $order_data->payment_method == 'cash_before_delivery' && (!$id || $order_data->status == 'pending_approval')):
            $order_data->status = 'pending_approval';
        endif;

        if ($request->has('image')):
            $order_data->receipt = GlobalFunction::saveFileAndGivePath($request->image);
        else:
            unset($order_data->receipt);
        endif;

        $order = $this->createOrderFromDashboard($order_data);
        $id = $order->id;

        return redirect(route('orderForm', ['id' => $id]))->with('message', $msg);
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Order::when($request->input('search.value'), function ($query) use ($request) {
                $search = $request->input('search.value');
                $query->where(function($q) use ($search) {
                    $q->where('order_no', 'LIKE', "%$search%")
                        ->orWhere('name', 'LIKE', "%$search%")
                        ->orWhere('phone_number', 'LIKE', "%$search%")
                        ->orWhere('tracking_number', 'LIKE', "%$search%");
                });
            })
            ->when(isset($request->status) && $request->status != 'all', function($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->brand, function($query) use ($request) {
                $query->where('brand', $request->brand);
            })
            ->when($request->country, function($query) use ($request) {
                $query->where('country', $request->country);
            })
            ->when($request->type, function($q) use ($request) {
                $q->where('user_type', $request->type);
            })
            ->when($request->channel, function($q) use ($request) {
                $q->where('sales_channel_id', $request->channel);
            })
            ->when(is_retailer(), function ($query) use ($request) {
                // when retailer login
                $query->where(function($q) {
                    $q->where('retailer_id', session('user')->user_id)
                        ->orWhere('user_id', session('user')->user_id);
                })
                ->whereNotIn('status', ['pending_approval', 'approved', 'rejected']);
            }, function ($q) use ($request) {
                // when admin login
                $q->where('user_type', '!=', 'retailer_customer');

                if($request->type == 'customer' && is_numeric($request->user) && $request->user > 0):
                    $q->where('user_id', $request->user);
                elseif($request->type == 'retailer'):
                    if(is_numeric($request->retailer) && $request->retailer > 0):
                        $q->where('user_id', $request->retailer);
                    endif;

                    if($request->payment):
                        $q->where('payment_method', $request->payment);
                    endif;
                endif;
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        $exportData = collect();
        
        if ($result->count() > 0) {
            foreach ($result as $item) {
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $payment = $item->payment;
                
                if($item->user_type == 'retailer') {
                    $item->payment_method = config('staticdata.retailer_type.'. $item->payment_method);
                }
                // if(is_retailer()):
                //     if($item->user_type == 'retailer'):
                //         $item->user_type = 'Self';
                //     else:
                //         $item->user_type = 'Customer';
                //     endif;
                // else:
                //     $item->user_type = ucwords($item->user_type);
                // endif;

                $item->status = config('staticdata.order_status.'. $item->status);
                $item->payment_status = $payment->status ?? '';
                // $item->country = config('staticdata.country.'. $item->country);
                // $item->brand = config('staticdata.brand.'. $item->brand);
                
                foreach ($item->orderItems as $orderItem) {
                    $order = clone $item;
                    $order->order_products_name = $orderItem->product_name;
                    $order->quantity = $orderItem->quantity;
                    unset($order->orderItems);
                    $exportData->push($order);
                }
            }
        }

        $fileName = 'orders_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($exportData, 'orders'),
            $fileName
        );
    }

    function pdfReceipt($id)
    {
        $data = Order::find($id);
        if(!$data):
            return redirect(route('order'))->with('error', __('Order not found!'));
        endif;

        $data->currency = global_settings('currency');
        $data->bundles = null;
        if($data->bundle_items->count() > 0):
            $bundle_list = $data->bundle_items->groupBy('order_bundle_id')->toArray();
            $data->bundles = OrderBundles::whereIn('id', array_keys($bundle_list))->get();
        endif;

        $file_path = "order/".$data->order_no.".pdf";
        $pdf = \PDF::loadView('pdf.order-receipt', compact('data'));

        $pdf->getDomPDF()->getOptions()->set([
                'isRemoteEnabled' => true,
            ]);

        \Storage::disk('public')->put($file_path, $pdf->output());
        return redirect(\Storage::disk('public')->url($file_path));
    }

    function checkDeliveryAvailability(Request $request)
    {
        $unavailable_products = [];
        if($request->quotation_id != '') {
            return ['success' => true];
        }
        
        foreach($request->products as $key => $item) {
            if($key != 'new_row') {
                $product = Products::find($item['product_id']);
                if(!$product) continue;
                
                $manual_postcodes = json_decode($product->manual_postcode ?? '[]', true);
                
                $can_deliver_manual = in_array($request->postcode, $manual_postcodes);
                
                if(!$can_deliver_manual) {
                    $unavailable_products[] = [
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'reason' => 'Not available for manual delivery: ' . $request->postcode
                    ];
                }
            }
        }
        
        if(!empty($unavailable_products)) {
            return [
                'success' => false,
                'message' => 'Some products cannot be delivered ' . $request->postcode,
                'unavailable_products' => $unavailable_products
            ];
        }
        
        return ['success' => true];
    }

    function priceCalculation($data='')
    {
        $ajax = false;
        if(!$data):
            $data = (object) request()->all();
            $ajax = true;
        endif;

        $subtotal = convertStringToNumber($data->subtotal);
        $tax = convertStringToNumber($data->tax);
        $quotation_id = $data->quotation_id ?? '';
        $delivery_type = '';
        $use_lalamove = false;

        if($data->delivery_method == 'delivery' && (!$data->outlet_id || !$data->address_id)):
            if($ajax):
                return response()->json(['success'=>false, 'message'=>'Invalid address or outlet']);
            else:
                return redirect(route('order'))->with('error', __('Invalid address or outlet'));
            endif;
        endif;

        if($data->lalamove ?? null):
            //calculate with state
            // $shipping_fee = $this->calculateShippingFee($subtotal+$tax, $data->brand, $data->country, $data->state);

            //calculate with lalamove
            $outlet = Outlets::find($data->outlet_id);
            $address = UserAddressBook::find($data->address_id);

            foreach (config('staticdata.delivery_vehicle_type') as $key => $value) {
                if ($data->total_weight <= $key) {
                    $delivery_type = $value;
                    break;
                }
            }

            $quotationData = [
                "data" => [
                    "serviceType" => $delivery_type,
                    "language" => "en_MY",
                    "stops" => [
                        [
                            "coordinates" => [
                                "lat" => $outlet->latitude,
                                "lng" => $outlet->longitude
                            ],
                            "address" => $outlet->address
                        ],
                        [
                            "coordinates" => [
                                "lat" => $address->latitude,
                                "lng" => $address->longitude
                            ],
                            "address" => $address->full_address
                        ]

                    ],
                    "isRouteOptimized" => true
                ]
            ];

            $lalamove = (new LalamoveService)->getQuotation($quotationData);
            
            //lalamove failed
            if (isset($lalamove['error']['errors'][0])) {
                $lalamove_error = $lalamove['error']['errors'][0]['message'] ?? 'Lalamove error';
                $manual_check = $this->manualDelivery($data);
                
                if(!$manual_check['success']) {
                    //lalamove and manual delivery failed
                    $result = [
                        'success' => false,
                        'message' => 'Delivery unavailable: ' . $manual_check['message'],
                        'subtotal' => number_format($subtotal, 2),
                        'tax' => number_format($tax, 2),
                        'shipping_fee' => '0.00',
                        'voucher' => number_format($data->voucher ?? 0, 2),
                        'total' => number_format($subtotal + $tax - ($data->voucher ?? 0), 2),
                        'point_earned' => 0,
                        'quotation_id' => '',
                        'delivery_vehicle' => ''
                    ];
                    return $ajax ? response()->json(['results' => $result]) : $result;
                }
                
                $shipping_fee = $data->shipping_fee ?? 0;
                $quotation_id = '';
                $delivery_fallback_message = 'Lalamove unavailable: ' . $lalamove_error . '. Proceeded with manual delivery.';
            } else {
                $shipping_fee = $lalamove['data']['priceBreakdown']['total'];
                $quotation_id = $lalamove['data']['quotationId'];
                $use_lalamove = true;
            }
        else:
            // Default manual delivery
            if($data->delivery_method == 'delivery') {
                $address = UserAddressBook::find($data->address_id);
                $manual_check = $this->manualDelivery($data);
                
                if(!$manual_check['success']) {
                    $result = [
                        'success' => false,
                        'message' => $manual_check['message'],
                        'subtotal' => number_format($subtotal, 2),
                        'tax' => number_format($tax, 2),
                        'shipping_fee' => '0.00',
                        'voucher' => number_format($data->voucher ?? 0, 2),
                        'total' => number_format($subtotal + $tax - ($data->voucher ?? 0), 2),
                        'point_earned' => 0,
                        'quotation_id' => '',
                        'delivery_vehicle' => ''
                    ];
                    return $ajax ? response()->json(['results' => $result]) : $result;
                }
                $shipping_fee = $data->shipping_fee ?? 0;
            }
        endif;
        $voucher = $data->voucher;
        $total = $subtotal + $tax + $shipping_fee - $voucher;

        // calculate point
        $point_earned = $this->calculatePoints($total, $data->country);
        // END calculate point

        $result = [
            'success' => true,
            'subtotal' => number_format($subtotal, 2),
            'tax' => number_format($tax, 2),
            'shipping_fee' => number_format($shipping_fee, 2),
            'voucher' => number_format($voucher, 2),
            'total' => number_format($total, 2),
            'point_earned' => $point_earned,
            'quotation_id' => $quotation_id,
            'delivery_vehicle' => $delivery_type,
            'delivery_method' => $use_lalamove ? 'lalamove' : 'manual',
            'message' => $delivery_fallback_message ?? null,
        ];

        if(!$ajax):
            return $result;
        endif;
        
        return response()->json([
            'results' => $result,
        ]);
    }
    function manualDelivery($data)
    {
        if(!isset($data->address_id)) {
            return ['success' => false, 'message' => 'No delivery address provided'];
        }
        
        $address = UserAddressBook::find($data->address_id);
        if(!$address) {
            return ['success' => false, 'message' => 'Invalid delivery address'];
        }
        
        $delivery_postcode = $address->postcode;
        $unavailable_products = [];
        $products = $data->products ?? [];
        if(empty($products) && request()->has('products')) {
            $products = request()->products;
        }
        
        foreach($products as $key => $item) {
            if($key != 'new_row') {
                $product_id = is_array($item) ? $item['product_id'] : $item;
                $product = Products::find($product_id);

                if(!$product) continue;
                
                $manual_postcodes = json_decode($product->manual_postcode ?? '[]', true);
                if(!in_array($delivery_postcode, $manual_postcodes)) {
                    $unavailable_products[] = [
                        'name' => $product->name,
                    ];
                }
            }
        }
        
        if(!empty($unavailable_products)) {
            $product_names = array_column($unavailable_products, 'name');
            return [
                'success' => false, 
                'message' => 'Products not available for delivery to postcode ' . $delivery_postcode . ': ' . implode(', ', $product_names),
                'unavailable_products' => $unavailable_products
            ];
        }
        return ['success' => true];
    }

    function orderPaymentLink($id)
    {
        $order = Order::with('payment')->find($id);
        if(!$order):
            return redirect(route('order'))->with('error', __('Order not found!'));
        endif;

        $payment_link = env('WEB_PAYMENT_URL').$order->order_no;

        return redirect($payment_link);
    }

    function orderCheckStock()
    {
        $request = request()->all();

        $no_stock = [];
        if($request['type'] == 'retailer_customer'):
            $no_stock = $this->checkRetailerStock($request);
        elseif($request['brand'] == 'nattome'):
            foreach($request['products'] as $item):
                $key = $item['index'];
                $product_id = $item['product_id'];
                $variation_id = $item['variation_id'] ?? 0;
                $sku = $item['sku'];
                $quantity = $item['quantity'];

                $available_stock = $this->getStock($sku);
                if(isset($available_stock['status']) && $available_stock['status'] == false):
                    $no_stock[] = [
                        'key' => $key,
                        'stock' => 'SKU required'
                    ];
                elseif($available_stock < $quantity):
                    $no_stock[] = [
                        'key' => $key,
                        'stock' => $available_stock.' available'
                    ];
                endif;
            endforeach;
        endif;

        return response()->json([
            'results' => $no_stock,
        ]);
    }
}
