<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\PurchaseReceipt;
use Illuminate\Support\Facades\Auth;

class ApiPurchaseReceipt
{
    function uploadReceipt(Request $request)
    {
        $user = Auth::user();
        $ref_no = GlobalFunction::generateRandomNumber();

        if (!isset($request->image) && $request->image):
            return GlobalFunction::sendSimpleResponse(false, __('Image receipt required.'));
        endif;

        foreach ($request->image as $image) {
            $purchase_receipt = new PurchaseReceipt();
            $purchase_receipt->user_id = $user->id;
            $purchase_receipt->ref_no = $ref_no;
            $purchase_receipt->image = GlobalFunction::saveFileAndGivePath($image);
            $purchase_receipt->status = 'pending';
            $purchase_receipt->save();
        }

        return GlobalFunction::sendDataResponse(true, __('Purchase receipt uploaded.'), []);
    }
}