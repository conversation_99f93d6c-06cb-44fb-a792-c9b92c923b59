<?php

namespace App\Http\Controllers\Api;

use App\Traits\DinlrTraits;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GlobalFunction;
use App\Models\GlobalSettings;
use Illuminate\Support\Facades\Log;

class ApiDinlr extends Controller
{
    use DinlrTraits;

    function setRefreshToken($token)
    {
        GlobalSettings::setSetting('dinlr_refresh_token', $token);
    }

    function oauth()
    {
        try {
            $response = $this->oauthToken();
        } catch (\Exception $th) {
            //throw $th;
            dd($th);
        }
        return $response;
    }

    // function retrieveCustomerGroups()
    // {
    //     return $this->getCustomerGroups();
    // }

    function callbackOrderCreated(Request $request)
    {
        $data = $request->getContent();
        GlobalFunction::storeDinlrTransaction('callback', 'order.created', null, $data);
        $this->callbackStoreOrder($data);
    }

    function createStoreCredit(Request $request) {
        $this->allocateStoreCredit($request->customer_id, $request->point);
    }
}
