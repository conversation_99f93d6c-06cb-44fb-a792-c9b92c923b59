<?php
namespace app\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AccountCreated extends Mailable
{
	use Queueable, SerializesModels;

	public function __construct($data)
    {
        $this->data = $data;
    }

	public function build()
	{
		$subject = __('[:site_name] Account Created', ['site_name' => global_settings('site_name')]);
		$build = $this->subject($subject)
            ->view('emails.account-created')
            ->with('data', $this->data);
	}
}