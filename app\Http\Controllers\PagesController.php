<?php
namespace App\Http\Controllers;

use App\Models\Pages;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;

class PagesController extends Controller
{
    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function pagesView($type , Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->brand ?? userBrand()[0];
        }

        $data = Pages::where('brand', $brand )->first();

        switch($type):
            case "privacy-policy":
                $title = __('Privacy Policy');
                $content = $data->privacy ?? '';
                $menu = 'privacy';
                $editable = (checkPermission('edit_privacy') ? true : false);

                if(!checkPermission('view_privacy')):
                    return redirect(route('index'))->with('error', __('You do not have permission to view privacy policy.'));
                endif;
                break;
            case "terms-of-use":
                $title = __('Terms and Conditions');
                $content = $data->termsofuse ?? '';
                $menu = 'terms';
                $editable = (checkPermission('edit_term') ? true : false);

                if(!checkPermission('view_term')):
                    return redirect(route('index'))->with('error', __('You do not have permission to view terms and conditions.'));
                endif;
                break;
            case "about-us":
                $title = __('About Us');
                $content = $data->aboutus ?? '';
                $menu = 'about';
                $editable = (checkPermission('edit_about') ? true : false);

                if(!checkPermission('view_about')):
                    return redirect(route('index'))->with('error', __('You do not have permission to view about us.'));
                endif;
                break;
        endswitch;

        return view('pages', [
            'type' => $type,
            'brand' => $brand,
            'menu' => $menu,
            'title' => $title ?? null,
            'content' => $content ?? null,
            'editable' => $editable ?? null
        ]);
    }

    function pagesUpdate(Request $request)
    {
        $brand = null;
        
        if ($this->brandSelector) {
            $brand = $request->brand;
        }

        $data = Pages::where('brand', $brand)->first();

        if(!$data){
            $data = new Pages();
            $data->brand = $brand;
        }

        $type = $request->type;
        $content = $request->content;
        if($type == "privacy-policy"):
            $data->privacy = $content;
        elseif($type == "terms-of-use"):
            $data->termsofuse = $content;
        elseif($type == "about-us"):
            $data->aboutus = $content;
        endif;
        $data->save();

        // return GlobalFunction::sendSimpleResponse(true, 'Updated!');
        return redirect(route('pagesView', ['type' => $type , 'brand' => $brand]))->with('message', "Updated!");
    }

    function pagesDetail(Request $request)
    {
        if ($this->brandSelector) {
            $brand = $request->brand;
        }

        $data = Pages::where('brand', $brand)->first();

        $type = $request->type;
        $content = '';
        if($type == "privacy-policy"):
         $content = $data->privacy ?? '';
        elseif($type == "terms-of-use"):
         $content = $data->termsofuse ?? '';
        elseif($type == "about-us"):
         $content = $data->aboutus ?? '';
        endif;

        return response()->json(['success' => true, 'content' => $content ]);
    }
}