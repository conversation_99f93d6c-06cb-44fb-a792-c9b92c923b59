<?php

namespace App\Http\Controllers\Api;

use App\Models\ChainStore;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\Outlets;

class ApiChainStore extends Controller
{
    function listing(Request $request, $brand)
    {
        // filters
        $state = $request->state ?? null;
        $search = $request->search ?? null;
        $per_page = $request->per_page ?? 10;
        $featured = $request->featured ?? null;

        $data = [];
        $chainStores = ChainStore::whereHas('outlets', function ($query) {
                $query->where('is_active', 1);
            })
            ->when(isset($state), function ($query) use ($state) {
                $query->whereHas('outlets', function ($query) use ($state) {
                    $query->where('state', $state);
                });
            })
            ->when(isset($search), function ($query) use ($search) {
                $query->where('name', 'LIKE', "%{$search}%");
            })
            ->when(isset($featured), function ($query) use ($featured) {
                $query->where('is_featured', $featured);
            })
            ->get();

        if ($chainStores->count() > 0) {
            foreach ($chainStores as $chainStore) {
                foreach ($chainStore->outlets as $chainStoreOutlet) {
                    $outletBrands = json_decode($chainStoreOutlet->brand);
                    if (in_array($brand, $outletBrands)) {
                        array_push($data, $chainStore->toArray());
                    }
                }
            }
        }

        $storeLocators = Outlets::whereNull('chain_store_id')
            ->where('is_active', 1)
            ->when(isset($state), function ($query) use ($state) {
                $query->where('state', $state);
            })
            ->when(isset($search), function ($query) use ($search) {
                $query->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('contact_number', 'LIKE', "%{$search}%")
                    ->orWhere('address', 'LIKE', "%{$search}%");
            })
            ->when(isset($featured), function ($query) use ($featured) {
                $query->where('is_featured', $featured);
            })
            ->get();

        foreach ($storeLocators as $outlet) {
            $outletBrands = json_decode($outlet->brand);
            if (in_array($brand, $outletBrands)) {
                array_push($data, $outlet->toArray());
            }
        }

        usort($data, function ($a, $b) {
            return $b['is_featured'] <=> $a['is_featured'];
        });

        $data = collect($data)->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function showOutlets(Request $request, $brand, $id)
    {
        // filters
        $per_page = $request->per_page ?? 10;
        $state = $request->state ?? null;
        $search = $request->search ?? null;

        $chainStore = ChainStore::find($id);

        if (!$chainStore) {
            return GlobalFunction::sendSimpleResponse(false, 'Chain store not found!');
        }

        $data = collect();

        $outlets = Outlets::where('chain_store_id', $chainStore->id)
            ->where('is_active', 1)
            ->when(isset($state), function ($query) use ($state) {
                $query->where('state', $state);
            })
            ->when(isset($search), function ($query) use ($search) {
                $query->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('contact_number', 'LIKE', "%{$search}%")
                    ->orWhere('address', 'LIKE', "%{$search}%");
            })
            ->get();


        foreach ($outlets as $outlet) {
            $outletBrands = json_decode($outlet->brand);

            if (in_array($brand, $outletBrands)) {
                $data[] = $outlet;
            }
        }

        $data = $data->paginate($per_page);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
