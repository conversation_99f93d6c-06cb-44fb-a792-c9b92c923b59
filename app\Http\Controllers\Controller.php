<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Carts;
use App\Models\BirthdayGift;
use App\Models\CartProducts;
use App\Models\GlobalFunction;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Http\Controllers\Admin\CartController;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function generateRandomString($length = 8)
    {
        $string = GlobalFunction::generateRandomString($length);

        return GlobalFunction::sendSimpleResponse(true, $string);
    }

    public function emailBirthdayGift($id)
    {
        $msg = '';
        $status = 0;
        $user = User::where('id', $id)->whereNull('deleted_at')->first();

        if (!$user) {
            $msg = 'User not found.';
        } else {
            $current_month = Carbon::now()->month;
            $dob = $user->dob;
            $dob_month = Carbon::parse($dob)->month;
            $brand = $user->brand;
            $membership_tier = $user->membership_tier;

            if ($current_month == $dob_month) {
                $birthday_gifts = BirthdayGift::whereJsonContains('month',(string)$dob_month)
                    ->whereJsonContains('brand', $brand)
                    ->where('is_active', 1)
                    ->get();

                $birthday_gift_add = [];
                foreach ($birthday_gifts as $birthday_gift) {
                    $birthday_gift_items = $birthday_gift->items()->whereJsonContains('membership_tier_ids', (string)$membership_tier)->get();

                    if (count($birthday_gift_items) > 0) {
                        foreach ($birthday_gift_items as $birthday_gift_item) {

                            $cart = Carts::where('user_id', $user->id)->first();
                            $cart_product= CartProducts::where('cart_id', $cart->id)
                                ->where('product_id', $birthday_gift_item->product_id)
                                ->where('variation_id', $birthday_gift_item->variation_id)
                                ->where('is_birthday', 1)
                                ->first();

                            if (!$cart_product) {
                                $birthday_gift_add[] = (object)[
                                    'product_id' => $birthday_gift_item->product_id,
                                    'variation_id' => $birthday_gift_item->variation_id,
                                    'quantity' => $birthday_gift_item->quantity,
                                    'is_birthday' => 1,
                                ];
                            }
                        }
                    }
                }

                if (count($birthday_gift_add) > 0) {
                    $cart_controller = new CartController();
                    $cart_controller->add($user->id, $birthday_gift_add);
                }
                $status = 1;
                $msg = 'Birthday gift added.';
            } else {
                $msg = 'Birthday gift expired.';
            }
        }

        return redirect()->away(env('MOBILE_URL').'?status='. $status.'&msg='.$msg);
    }
}
