<?php
namespace App\Http\Controllers;

use App\Models\Users;
use App\Models\Constants;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Traits\PaymentGatewaysTraits;
use Illuminate\Support\Facades\Validator;

class PaymentGatewayController extends Controller
{
    use PaymentGatewaysTraits;

    function response($gateway, Request $request)
    {
        $response = $this->getPaymentResponse($gateway, $request);
        return $response;
    }

    function callback($gateway, Request $request)
    {
        $response = $this->getPaymentCallback($gateway, $request);
        return $response;
    }

    function payment($gateway, Request $request)
    {
        $paymentUrl = $this->getPaymentUrl($gateway, $request);
        return redirect($paymentUrl);
    }

    function notification($gateway, Request $request)
    {
        $response = $this->getPaymentNotification($gateway, $request);
        return $response;
    }

    // API
    /*function fetchPaymentUrl($gateway, Request $request)
    {
        $rules = [
            'amount' => 'required',
            'type' => 'required',
        ];

        if($request->type == "user-topup-wallet"):
            $rules['user_id'] = 'required';
        endif;

        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $amount = $request->amount;
        if($request->type == "user-topup-wallet"):
            $user = Users::find($request->user_id);
            $ref_id = $user->id;
            $ref_no = Constants::prefixUserTransactionId.$user->id;
            $name = $user->fullname;
            $email = $user->email_address;
            $phone = $user->phone_number;
            $description = __('Top-up wallet');
        endif;

        $data = [
            'amount' => $amount,
            'ref_no' => $ref_no,
            'ref_id' => $ref_id ?? null,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'description' => $description,
        ];
        return $this->url($gateway, $data);
    }*/
}