<?php
namespace App\Http\Controllers\Api;

use App\Models\MembershipTiers;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\UserPointTransaction;
use Illuminate\Support\Facades\Validator;

class ApiMembershipTiers
{
    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'];
        }
    }

    function listing(Request $request)
    {
        $data = MembershipTiers::where('is_active', 1)
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', "LIKE", "%{$this->brand}%");
            })
            ->with('benefits')
            ->orderBy('point_from', 'asc')
            ->get()
            ->makeHidden([
                'created_at',
                'updated_at',
            ]);

        $user = auth()->user();
        $accumulated_points = $user->accumulated_points;
        $point_balance = $user->point_balance;
        $point_conversion = global_settings('bookings_points_redeem_ratio');
        $point_amount = number_format($point_balance * $point_conversion, 2);

        $count=1;
        foreach($data as $item)
        {
            if($item->image):
                $item->image = GlobalFunction::createMediaUrl($item->image);
            endif;

            if($item->icon):
                $item->icon = GlobalFunction::createMediaUrl($item->icon);
            endif;

            $item->point_balance = $point_balance;
            $item->point_amount = global_settings('currency')." ".$point_amount;
            $item->accumulated_points = $accumulated_points;
            $item->is_current = false;
            $item->is_passed = false;
            $item->is_locked = false;
            // if($accumulated_points >= $item->point_from && $accumulated_points <= $item->point_to):
            if($user->membership_tier == $item->id):
                $item->is_current = true;
            elseif($accumulated_points >= $item->point_to):
                $item->is_passed = true;
            elseif($accumulated_points < $item->point_from):
                $item->is_locked = true;
            endif;

            if($count == sizeof($data) && $item->is_passed):
                $item->is_passed = false;
                $item->is_current = true;
            endif;

            $next_level_name = $data[$count]->name ?? null;
            $item->next_level_name = $next_level_name;
            if($item->is_current == true):
                // how many points to next level
                $item->points_to_next = $next_level_name ? $item->point_to - $accumulated_points : null;

                // points expiring soon
                $points = UserPointTransaction::where('user_id', $user->id)
                    ->where('point_expiry_balance', '>', 0)
                    ->where('is_expired', 0)
                    ->where('status', 'ready')
                    ->whereNotNull('expired_date')
                    ->orderBy('expired_date', 'asc')
                    ->first();

                if($points):
                    $points_expiring_date = $points->expired_date;
                    $points_expiring = UserPointTransaction::where('user_id', $user->id)
                        ->where('is_expired', 0)
                        ->where('status', 'ready')
                        ->where('expired_date', $points_expiring_date)
                        ->sum('point_expiry_balance');
                    $points_expiring = (int) $points_expiring;
                endif;

                $item->points_expiring = $points_expiring ?? 0;
                $item->points_expiring_date = $points_expiring_date ?? null;
            endif;

            $count++;
        }

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function detail(Request $request)
    {
        $rules = [ 'id' => 'required' ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if($result):
            return $result;
        endif;

        $id = $request->id;

        $data = MembershipTiers::where('is_active', 1)
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', "LIKE", "%{$this->brand}%");
            })
            ->with('benefits')
            ->where('id', $id)
            ->first()
            ->makeHidden([
                'created_at',
                'updated_at',
            ]);

        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}