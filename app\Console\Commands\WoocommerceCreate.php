<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Integration\WoocommerceController;

class WoocommerceCreate extends Command
{
    protected $signature = 'woocommerce:create';

    protected $description = 'Create Order From Woocommerce';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $data = [
            'status' => 'completed',
            'after' => (new \DateTime('yesterday'))->setTime(0, 0, 0)->format(\DateTime::ATOM),
            'per_page' => 100
        ];

        foreach (['derma' => 'The Perfect Derma','heartio' => 'The Perfect Heartio'] as $brand => $brand_name) {
            $data['brand'] = $brand;
            (new WoocommerceController)->createOrder($data);
        }
    }
}