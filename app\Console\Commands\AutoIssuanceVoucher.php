<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserVouchers;
use Illuminate\Console\Command;
use App\Models\RedemptionVouchers;
use App\Models\CronjobLogs as Cronjob;
use Illuminate\Support\Facades\Notification;
use App\Notifications\AutoIssuanceNotification;

class AutoIssuanceVoucher extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto_issuance:voucher';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Distribute Auto Issuance Vouchers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $vouchers = RedemptionVouchers::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('is_birthday', 0)
            ->where('is_auto_issuance', 1)
            ->where('available_start_date', '<=', date('Y-m-d H:i:s'))
            ->where('available_end_date', '>=', date('Y-m-d H:i:s'))
            ->get();

        $reminder_recipient = [];
        foreach ($vouchers as $voucher) {
            $users = User::where('is_active', 1)
                ->whereIn('membership_tier', json_decode($voucher->membership_tiers))
                ->get();

            foreach ($users as $user) {
                $user_voucher = UserVouchers::where('user_id', $user->id)
                    ->where('voucher_id', $voucher->id)
                    ->first();

                if ($user_voucher) {
                    continue;
                }

                //check the voucher limit
                if ($voucher->max_quantity && $voucher->redeemed_quantity >= $voucher->max_quantity):
                    return 0;
                endif;

                $voucher->redeemed_quantity += 1;
                $voucher->balance_quantity -= 1;
                $voucher->save();

                $user_voucher = new UserVouchers;
                $user_voucher->user_id = $user->id;
                $user_voucher->voucher_id = $voucher->id;
                $user_voucher->voucher_code = $voucher->code;
                $user_voucher->voucher_value = $voucher->value;
                $user_voucher->portal = $voucher->portal;
                $user_voucher->effective_start_date = $voucher->redemption_start_date;
                $user_voucher->effective_end_date = $voucher->redemption_end_date;
                $user_voucher->voucher_data = json_encode($voucher);
                $user_voucher->save();

                Notification::route('whatsapp', $user->phone_number)
                    ->notify(new AutoIssuanceNotification($user, $voucher));

                $reminder_recipient[] = $user->id . ": " . $user->phone_number . " - " . $user_voucher->id;
            }
        }

        if ($reminder_recipient) {
            Cronjob::create([
                'type' => 'auto issuance voucher',
                'recipient' => implode(', ', $reminder_recipient),
            ]);
        }

        return 0;
    }
}
