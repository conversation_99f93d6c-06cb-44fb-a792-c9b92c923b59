<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\NewsAndAnnouncement;
use App\Http\Controllers\Controller;

class ApiNewsAndAnnouncement extends Controller
{
    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'];
        }
    }

    function listing(Request $request)
    {
        // filters
        $per_page = $request->per_page ?? 10;

        $result = NewsAndAnnouncement::where('is_active', true)
            ->when(isset($this->brand), function ($query) {
                return $query->where('brand', $this->brand);
            })
            ->paginate($per_page);

        if (isset($result['error'])):
            return GlobalFunction::sendSimpleResponse(false, $result['error']);
        endif;

        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function detail($id)
    {
        $data = NewsAndAnnouncement::find($id);
        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
