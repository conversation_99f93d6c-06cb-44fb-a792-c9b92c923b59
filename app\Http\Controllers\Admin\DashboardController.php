<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Users;
use App\Models\Doctors;
use App\Models\Bookings;
use App\Models\Constants;
use App\Models\Appointments;
use App\Models\UserVouchers;
use App\Models\MembershipTiers;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\RedemptionVouchers;

class DashboardController extends Controller
{
    function index()
    {
        $bookings = Bookings::get();
        $total_bookings = $bookings->count();
        $total_completed_bookings = $bookings->where('booking_status', 'completed')->count();
        $total_cancelled_bookings = $bookings->where('booking_status', 'cancelled')->count();
        $total_collection_bookings = $bookings->where('payment_status', 'paid')->where('booking_status', '!=', 'cancelled')->sum('total');

        $redeemed_vouchers = UserVouchers::get();
        $total_redeemed_vouchers = $redeemed_vouchers->count();
        $most_redeemed_voucher = DB::table('user_vouchers')
            ->select('voucher_id', DB::raw('COUNT(voucher_id) AS most_redeemed_voucher'))
            ->groupBy('voucher_id')
            ->orderBy('most_redeemed_voucher', 'DESC')
            ->first();
        $most_used_voucher = DB::table('user_vouchers')
            ->select('voucher_id', DB::raw('COUNT(voucher_id) AS most_used_voucher'))
            ->where('used_date', '!=', null)
            ->groupBy('voucher_id')
            ->orderBy('most_used_voucher', 'DESC')
            ->first();

        $total_members = Users::whereNull('deleted_at')->count();
        $total_new_members = Users::whereMonth('created_at', Carbon::now()->month)->count();

        $membership_tier = MembershipTiers::where('is_active', 1)->get();
        foreach($membership_tier as $tier_item):
            $most_redeemed = UserVouchers::whereHas('user', function($query) use ($tier_item) {
                $query->where('membership_tier', $tier_item->id);
            })
            ->selectRaw('voucher_id, COUNT(voucher_id) AS most_redeemed_voucher')
            ->groupBy('voucher_id')
            ->orderBy('most_redeemed_voucher', 'DESC')
            ->first();

            $most_used = UserVouchers::whereHas('user', function($query) use ($tier_item) {
                $query->where('membership_tier', $tier_item->id);
            })
            ->selectRaw('voucher_id, COUNT(voucher_id) AS most_used_voucher')
            ->whereNotNull('used_date')
            ->groupBy('voucher_id')
            ->orderBy('most_used_voucher', 'DESC')
            ->first();

            $tier_item->most_redeemed = $most_redeemed->voucher->name ?? '-';
            $tier_item->most_used = $most_used->voucher->name ?? '-';
        endforeach;

        return view('index', [
            'stringing_booking' => [
                'total_booking' => $total_bookings,
                'completed' => $total_completed_bookings,
                'cancelled' => $total_cancelled_bookings,
                'collection' => $total_collection_bookings,
            ],
            'loyalty' => [
                'total_voucher' => $total_redeemed_vouchers,
                'most_redeemed_voucher_name' => $most_redeemed_voucher ? RedemptionVouchers::find($most_redeemed_voucher->voucher_id)->name : '-',
                'most_used_voucher' => $most_used_voucher ? RedemptionVouchers::find($most_used_voucher->voucher_id)->name : '-',

            ],
            'member' => [
                'total' => $total_members,
                'total_new' => $total_new_members,
            ],
            'membership_tier' => $membership_tier,
        ]);
    }
}
