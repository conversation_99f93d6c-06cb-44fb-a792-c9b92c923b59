<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Carts;
use App\Models\CartProducts;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CartController extends Controller
{
    public function add($user_id, $birthday_gift_products)
    {
        $user = User::find($user_id);

        if (!$user) {
            return false;
        }

        $cart = Carts::firstOrCreate(['user_id' => $user->id]);
        $cart_id = $cart->id;

        foreach ($birthday_gift_products as $birthday_gift_product) {
            $product_id = $birthday_gift_product->product_id;
            $variation_id = $birthday_gift_product->variation_id ?? null;
            $quantity = $birthday_gift_product->quantity ?? null;
            $is_birthday = $birthday_gift_product->is_birthday ?? 0;

            $cartProducts = new CartProducts();
            $cartProducts->cart_id = $cart_id;
            $cartProducts->product_id = $product_id;
            $cartProducts->variation_id = $variation_id;
            $cartProducts->quantity = $quantity;
            $cartProducts->is_birthday = $is_birthday;
            $cartProducts->save();
        }

        return $cart_id;
    }
}
