<?php
namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Models\RetailerProduct;
use App\Models\RetailerStockTransaction;
use App\Models\Products;
use App\Models\ProductVariations;
use App\Models\Retailer;
use App\Models\Order;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\OrderTraits;

class RetailerStockTransactionController extends Controller
{
    use OrderTraits;

    function index()
    {
        if (!checkPermission('view_retailer_stock_transaction')):
            return redirect(route('index'))->with('error', __('You do not have permission to view retailer stock transaction.'));
        endif;

        $bulk_action = [
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('retailerStockTransactionExport'),
                'filename' => 'retailer_stock_transaction'
            ]
        ];

        if (!checkPermission('export_retailer_stock_transaction')):
            unset($bulk_action['export']);
        endif;

        $is_retailer = is_retailer();
        $moduleID = 'retailer_stock_transaction';
        return view('retailer-stock-transaction', compact('bulk_action', 'moduleID', 'is_retailer'));
    }

    function listing(Request $request)
    {
        $is_retailer = is_retailer();
        if(!$is_retailer):
            return $this->reportListing($request);
        endif;
    
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';

        $result = RetailerStockTransaction::when($request->input('search.value'), function ($q) use ($request, $is_retailer) {
                $search = $request->input('search.value');
                $q->where(function ($query) use ($search) {
                    $query->whereHas('product', function ($query) use ($search) {
                        $query->where('product_name', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when($is_retailer, function ($query) {
                // when retailer login
                $query->where('retailer_id', session('user')->user_id);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($query) use ($request) {
                $query->where('retailer_id', $request->retailer);
            })
            ->when($request->daterange, function ($query) use ($request) {
                $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->daterange)[0])->format('Y-m-d');
                $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->daterange)[1])->format('Y-m-d');
                $query->whereDate('date_time', '>=', $start_date)->whereDate('date_time', '<=', $end_date);
            })
            ->when($request->type, function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                        <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="retailerStockTransactionList">
                        <span></span>
                    </label>';

                $product_data = $item->product;
                $product = $product_data->product_name;
                if(isset($product_data->variation_name)):
                    $product .= ' <div class="text-muted">' . $product_data->variation_name.'</div>';
                endif;

                $param = [
                    $checkbox,
                    $item->retailer->first_name ?? '-',
                    $product,
                    '<div class="text-center">'.$item->type.number_format($item->quantity, 0, '', ',').'</div>',
                    $item->order->order_no ?? '-',
                    nl2br($item->remarks),
                    Carbon::parse($item->date_time)->format(config('app.display_datetime_format'))
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if($is_retailer):
                    unset($param[1]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function reportListing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $is_retailer = is_retailer();

        $result = Retailer::when($is_retailer, function ($query) {
                // when retailer login
                $query->where('user_id', session('user')->user_id);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($query) use ($request) {
                $query->where('user_id', $request->retailer);
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                        <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="retailerStockTransactionList">
                        <span></span>
                    </label>';

                // from orders
                $order = Order::where('user_id', $item->user_id)
                    ->where('user_type', 'retailer')
                    ->join('order_items', 'order_items.order_id', '=', 'orders.id')
                    ->when($request->date, function ($query) use ($request) {
                        $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[0])->format('Y-m-d');
                        $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[1])->format('Y-m-d');
                        $query->whereDate('orders.created_at', '>=', $start_date)->whereDate('orders.created_at', '<=', $end_date);
                    });
                $order_delivered = $order->clone()
                    ->where('status', 'completed')
                    ->sum('quantity');
                $order_returned = $order->clone()
                    ->whereExists(function ($query) {
                        $query->select(\DB::raw(1))
                            ->from('order_status_histories')
                            ->whereColumn('order_status_histories.order_id', 'orders.id')
                            ->where('order_status_histories.status', 'completed');
                    })
                    ->when($request->date, function ($query) use ($request) {
                        $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[0])->format('Y-m-d');
                        $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[1])->format('Y-m-d');
                        $query->whereDate('orders.created_at', '>=', $start_date)->whereDate('orders.created_at', '<=', $end_date);
                    })
                    ->where('orders.status', 'cancelled')
                    ->sum('quantity');
                // END from orders

                // from retailer_stock_transactions
                $stock = RetailerStockTransaction::where('retailer_id', $item->user_id)
                    ->where('order_id', null)
                    ->when($request->date, function ($query) use ($request) {
                        $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[0])->format('Y-m-d');
                        $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[1])->format('Y-m-d');
                        $query->whereDate('date_time', '>=', $start_date)->whereDate('date_time', '<=', $end_date);
                    });
                $stock_delivered = $stock->clone()
                    ->where('type', '+')
                    ->sum('quantity');
                $stock_returned = $stock->clone()
                    ->where('type', '-')
                    ->sum('quantity');
                // END from retailer_stock_transactions

                $total_delivered = $order_delivered + $stock_delivered;
                $total_returned = $order_returned + $stock_returned;
                $total_remaining = RetailerProduct::where('retailer_id', $item->user_id)->sum('available_stock');
                $total_remaining = '<div class="text-center">
                    <a href="'.route('retailerProductStock', [$item->user_id]).'" style="color:#000" data-toggle="tooltip" title="'. __('View Stocks').'">'.number_format($total_remaining, 0, '', ',').'</a>
                </div>';

                $param = [
                    $checkbox,
                    $item->first_name ?? '-',
                    '<div class="text-center">'.number_format($total_delivered ?? 0, 0, '', ',').'</div>',
                    '<div class="text-center">'.number_format($total_returned ?? 0, 0, '', ',').'</div>',
                    $total_remaining,
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if($is_retailer):
                    unset($param[1]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        $is_retailer = is_retailer();
        if(!$is_retailer && !$request->retailer_id):
            return GlobalFunction::sendSimpleResponse(false, 'Retailer is required!');
        endif;
        if(!$request->product_id):
            return GlobalFunction::sendSimpleResponse(false, 'Product is required!');
        endif;
        if(!$request->type):
            return GlobalFunction::sendSimpleResponse(false, 'Type is required!');
        endif;
        if(!$request->quantity):
            return GlobalFunction::sendSimpleResponse(false, 'Quantity is required!');
        endif;
        if(!$request->date_time):
            return GlobalFunction::sendSimpleResponse(false, 'Date & Time is required!');
        endif;

        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = RetailerStockTransaction::find($id);
        else:
            $msg = __('Added!');
            $query = new RetailerStockTransaction();
        endif;

        if ($is_retailer):
            $retailer_id = session('user')->user_id;
        else:
            $retailer_id = $request->retailer_id;
        endif;

        $query->retailer_id = $retailer_id;
        $query->order_id = $request->order_id ?? null;
        $query->product_id = $request->product_id;
        $query->variation_id = $request->variation_id ?? null;
        $query->type = $request->type;
        $query->quantity = $request->quantity;
        $query->remarks = $request->remarks;
        $query->date_time = date('Y-m-d H:i:s', strtotime($request->date_time));
        $query->save();

        $product = Products::find($query->product_id);
        $stock_data = $query;
        $stock_data->product_name = $product->name;
        $stock_data->variation_name = ProductVariations::find($stock_data->variation_id)->variation_name ?? null;
        $stock_data->brand = $product->brand;
        $stock_data->country = $product->country;
        $this->updateRetailerStockBalance($stock_data);

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function delete(Request $request)
    {
        if ($request->id):
            RetailerStockTransaction::find($request->id)->delete();
        else:
            RetailerStockTransaction::whereIn('id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function export(Request $request)
    {
        $is_retailer = is_retailer();
        if(!$is_retailer):
            return $this->exportReport($request);
        endif;
    
        $sort_col = $request->sort_col != null ? $request->sort_col : 'created_at';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'ASC';

        $result = RetailerStockTransaction::when($request->input('search.value'), function ($q) use ($request, $is_retailer) {
                $search = $request->input('search.value');
                $q->where(function ($query) use ($search) {
                    $query->whereHas('product', function ($query) use ($search) {
                        $query->where('product_name', 'LIKE', "%{$search}%");
                    });
                });
            })
            ->when($is_retailer, function ($query) {
                // when retailer login
                $query->where('retailer_id', session('user')->user_id);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($query) use ($request) {
                $query->where('retailer_id', $request->retailer);
            })
            ->when($request->daterange, function ($query) use ($request) {
                $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->daterange)[0])->format('Y-m-d');
                $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->daterange)[1])->format('Y-m-d');
                $query->whereDate('date_time', '>=', $start_date)->whereDate('date_time', '<=', $end_date);
            })
            ->when($request->type, function ($query) use ($request) {
                $query->where('type', $request->type);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->product_name = $item->product->product_name;
                $item->variation_name = $item->product->variation_name ?? null;
                $item->date_time = Carbon::parse($item->date_time)->format(config('app.display_datetime_format'));
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
            endforeach;
        endif;

        $fileName = 'retailer_stock_transaction_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'retailer_stock_transaction'),
            $fileName
        );
    }

    function exportReport($request)
    {
        $sort_col = $request->sort_col ?? 'created_at';
        $sort_by = $request->sort_by ?? 'DESC';
        $is_retailer = is_retailer();

        $result = Retailer::when($is_retailer, function ($query) {
                // when retailer login
                $query->where('user_id', session('user')->user_id);
            })
            ->when(is_numeric($request->retailer) && $request->retailer > 0, function ($query) use ($request) {
                $query->where('user_id', $request->retailer);
            })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                // from orders
                $order = Order::where('user_id', $item->user_id)
                    ->where('user_type', 'retailer')
                    ->join('order_items', 'order_items.order_id', '=', 'orders.id');
                $order_delivered = $order->clone()
                    ->where('status', 'completed')
                    ->sum('quantity');
                $order_returned = $order->clone()
                    ->whereExists(function ($query) {
                        $query->select(\DB::raw(1))
                            ->from('order_status_histories')
                            ->whereColumn('order_status_histories.order_id', 'orders.id')
                            ->where('order_status_histories.status', 'completed');
                    })
                    ->where('orders.status', 'cancelled')
                    ->sum('quantity');
                // END from orders

                // from retailer_stock_transactions
                $stock = RetailerStockTransaction::where('retailer_id', $item->user_id)
                    ->where('order_id', null)
                    ->when($request->date, function ($query) use ($request) {
                        $start_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[0])->format('Y-m-d');
                        $end_date = Carbon::createFromFormat('d-m-Y', explode(' to ', $request->date)[1])->format('Y-m-d');
                        $query->whereDate('date_time', '>=', $start_date)->whereDate('date_time', '<=', $end_date);
                    });
                $stock_delivered = $stock->clone()
                    ->where('type', '+')
                    ->sum('quantity');
                $stock_returned = $stock->clone()
                    ->where('type', '-')
                    ->sum('quantity');
                // END from retailer_stock_transactions

                $item->total_delivered = $order_delivered + $stock_delivered;
                $item->total_returned = $order_returned + $stock_returned;
            endforeach;
        endif;
        
        $fileName = 'retailer_stock_transaction_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'retailer_stock_transaction'),
            $fileName
        );
    }

    function productStock($retailer_id)
    {
        $moduleID = 'retailer_product_stock';
        return view('retailer-product-stock', compact('moduleID', 'retailer_id'));
    }

    function productStockList($retailer_id, Request $request)
    {
        $sort_col = $request->sort_col ?? 'available_stock';
        $sort_by = $request->sort_by ?? 'ASC';

        $result = RetailerProduct::when($request->input('search.value'), function ($q) use ($request) {
                $search = $request->input('search.value');
                $q->where(function ($query) use ($search) {
                    $query->where('product_name', 'LIKE', "%{$search}%")
                        ->orWhere('variation_name', 'LIKE', "%{$search}%");
                });
            })
            ->orderBy($sort_col, $sort_by);

        $totalData = count($result->get());
        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $product = $item->product_name;
                if(isset($item->variation_name)):
                    $product .= ' <div class="text-muted">' . $item->variation_name.'</div>';
                endif;

                $param = [
                    $product,
                    '<div class="text-right">'.$item->type.number_format($item->available_stock, 0, '', ',').'</div>',
                ];

                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }
}
