<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\Carts;
use App\Models\CartProducts;
use App\Models\CartBundles;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use App\Traits\OrderTraits;

use App\Models\UserPointTransaction;
use App\Models\UserAddressBook;

class ApiCarts extends Controller
{
    use OrderTraits;

    function add(Request $request)
    {
        $user = auth()->user();
        $cart = Carts::firstOrCreate(['user_id' => $user->id]);
        $cart_id = $cart->id;
        $product_id = $request->product_id;
        $variations = $request->variant_id ?? null;
        $gwp_id = $request->gwp_id ?? null;
        $pwp_id = $request->pwp_id ?? null;
        $bundle_id = $request->bundle_id ?? null;

        $cart_list = [];
        $product_data = [
            'product_id' => $product_id,
        ];
        if ($variations):
            foreach ($variations as $key => $variation_id):
                $cart_list[] = array_merge($product_data, [
                    'variation_id' => $variation_id,
                    'quantity' => $request->variant_qty[$key]
                ]);
            endforeach;
        else:
            $cart_list[] = array_merge($product_data, [
                'quantity' => $request->product_qty
            ]);
        endif;

        if ($bundle_id):
            if ($gwp_id):
                foreach ($gwp_id as $key => $gwp_product_id):
                    $gwp_data = [
                        'product_id' => $gwp_product_id,
                        'is_gwp' => 1
                    ];
                    $gwp_variation = $request->gwp_variant_id[$key] ?? null;
                    if ($gwp_variation):
                        foreach ($gwp_variation as $variant_key => $variation_id):
                            $cart_list[] = array_merge($gwp_data, [
                                'variation_id' => $variation_id,
                                'quantity' => $request->gwp_variant_qty[$key][$variant_key]
                            ]);
                        endforeach;
                    else:
                        $cart_list[] = array_merge($gwp_data, [
                            'quantity' => $request->gwp_qty[$key]
                        ]);
                    endif;
                endforeach;
            endif;

            if ($pwp_id):
                foreach ($pwp_id as $key => $pwp_product_id):
                    $pwp_data = [
                        'product_id' => $pwp_product_id,
                        'is_pwp' => 1
                    ];
                    $pwp_variations = $request->pwp_variant_id[$key] ?? null;
                    if ($pwp_variations):
                        foreach ($pwp_variations as $variant_key => $variation_id):
                            $cart_list[] = array_merge($pwp_data, [
                                'variation_id' => $variation_id,
                                'quantity' => $request->pwp_variant_qty[$key][$variant_key]
                            ]);
                        endforeach;
                    else:
                        $cart_list[] = array_merge($pwp_data, [
                            'quantity' => $request->pwp_qty[$key]
                        ]);
                    endif;
                endforeach;
            endif;
        endif;

        if ($cart_list):
            $cart_product_id = [];
            if ($bundle_id):
                $cart_bundle = new CartBundles();
                $cart_bundle->bundle_id = $bundle_id;
                $cart_bundle->save();
                $cart_bundle_id = $cart_bundle->id;
            endif;

            foreach ($cart_list as $item):
                $item = (object) $item;
                $existingCartProduct = CartProducts::where('cart_id', $cart_id)->where('product_id', $item->product_id)->first();

                if ($existingCartProduct) {
                    $existingCartProduct->quantity += $item->quantity;
                    $existingCartProduct->save();
                } else {
                    $cartProducts = new CartProducts();
                    $cartProducts->cart_id = $cart_id;
                    $cartProducts->cart_bundle_id = $cart_bundle_id ?? null;
                    $cartProducts->product_id = $item->product_id;
                    $cartProducts->variation_id = $item->variation_id ?? null;
                    $cartProducts->is_gwp = $item->is_gwp ?? 0;
                    $cartProducts->is_pwp = $item->is_pwp ?? 0;
                    $cartProducts->is_free = 0;
                    $cartProducts->quantity = $item->quantity;
                    $cartProducts->save();
                }
            endforeach;
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Added to cart.');
    }

    function update(Request $request)
    {
        $cart = Carts::where('user_id', auth()->user()->id)->first();

        if (isset($request->address_id)) {
            $cart->address_id = $request->address_id;
        }

        if (isset($request->cart_product_id)) {
            $cart_product = CartProducts::find($request->cart_product_id);

            if (!isset($request->type)) {
                return GlobalFunction::sendSimpleResponse(false, 'Type is required.');
            }

            if ($request->type == 'plus') {
                $cart_product->quantity += 1;
            } else {
                $cart_product->quantity -= 1;
            }

            $cart_product->save();
        }

        $cart->save();

        return GlobalFunction::sendSimpleResponse(true, 'Cart is updated.');
    }

    function delete(Request $request)
    {
        $cart_product = CartProducts::find($request->cart_product_id);
        if (!$cart_product):
            return GlobalFunction::sendSimpleResponse(false, 'Product not found.');
        endif;

        $cart_bundle_id = $cart_product->cart_bundle_id;
        if ($cart_bundle_id):
            CartBundles::find($cart_bundle_id)->delete();
            CartProducts::where('cart_bundle_id', $cart_bundle_id)->delete();
        else:
            $cart_product->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Product removed from cart.');
    }

    function summary(Request $request)
    {
        $user = auth()->user();
        if (!$user):
            return GlobalFunction::sendSimpleResponse(false, 'User not found.');
        endif;
        $tier = $user->membership_tier;
        $country = $user->country;
        $brand = $user->brand;
        $outlet_id = $request->outlet_id ?? null;
        $currency = ($country == 'malaysia') ? global_settings('currency') : global_settings('currency_sg');

        if ($outlet_id == null || $outlet_id == 'NaN') {
            return GlobalFunction::sendSimpleResponse(false, 'Please select a store to proceed.');
        }

        $cart = Carts::where('user_id', $user->id)->first();
        if (!$cart):
            return GlobalFunction::sendSimpleResponse(false, 'Cart is empty.');
        endif;
        $cart_id = $cart->id;
        $cart_products = $cart->products;
        if ($cart_products->count() == 0):
            return GlobalFunction::sendSimpleResponse(false, 'Cart is empty.');
        endif;

        $is_product_valid = $this->validateProduct($cart_products, $country, $brand);

        // get product details after product validation
        $cart_products = $cart->products()->get();
        $product_details = $this->getProductDetails($cart_products, $tier, $country);

        //check product country availability
        $address = $cart->user_address ?? null;
        $address_id = $address->id ?? null;

        // check for voucher validity & get the amount
        $is_voucher_valid = $this->validateVoucher($cart->voucher_list, $product_details, $country, $brand, $user, $cart_id);
        $is_coupon_valid = $this->validateCoupon($cart->coupon_id, $product_details, $user, $cart_id);

        // get payment details
        $state = $address ? $address->state : null;
        $voucher_amount = $is_voucher_valid['total_discount'] ?? 0;
        $delivery_discount = $is_voucher_valid['delivery_discount'] ?? null;
        $coupon_amount = $is_coupon_valid['total_discount'] ?? 0;
        $payment_details = $this->getPaymentDetails($product_details, $voucher_amount, $country, $brand, $state, $outlet_id, $address_id, $cart, $coupon_amount, $delivery_discount);

        if($payment_details['error_message'] != null):
            return GlobalFunction::sendSimpleResponse(false, $payment_details['error_message']);
        endif;

        // for frontend show purpose
        $show_msg = $cart->show_msg;
        if ($cart->show_msg == 1):
            $cart->show_msg = 0;
            $cart->save();
        endif;
        // END for frontend show purpose

        if (isset($product_details['cart_total_price'])):
            unset($product_details['cart_total_price'], $product_details['cart_total_tax']);
        endif;

        $voucher_list = $is_voucher_valid['voucher_list'];
        if ($voucher_list):
            foreach ($voucher_list as $key => $voucher):
                if ($voucher->voucher_data->type == 'discount' && $voucher->voucher_data->discount_type == 'fixed'):
                    $voucher->voucher_value = global_settings('currency') . " " . $voucher->voucher_data->value;
                elseif ($voucher->voucher_data->type == 'discount' && $voucher->voucher_data->discount_type == 'percentage'):
                    $voucher->voucher_value = $voucher->voucher_data->value . '%';
                else:
                    $voucher->voucher_value = '';
                endif;
                if (isset($voucher->voucher_data->free_product) && $voucher->voucher_data->free_product):
                    $voucher_list[$key]->voucher_data->free_product = array_values($voucher->voucher_data->free_product);
                else:
                    $voucher_list[$key]->voucher_data->free_product = null;
                endif;
                unset($voucher['free_product']);
            endforeach;
        endif;


        $coupon = null;
        if ($cart->coupon) {
            $coupon = $is_coupon_valid;
            $coupon['id'] = $cart->coupon->id;
            $coupon['name'] = $cart->coupon->name;
            $coupon['type'] = $cart->coupon->type;
            if ($cart->coupon->type == 'fixed') {
                $coupon['coupon_value'] = global_settings('currency') . ' ' . $cart->coupon->value;
            } else {
                $coupon['coupon_value'] = $cart->coupon->value . '%';
            }
        }

        $data = [
            'currency' => $currency,
            'products' => $product_details,
            'address' => $address,
            'vouchers' => $voucher_list,
            'coupon' => $coupon,
            'payment_details' => $payment_details,
            'removed_products' => $is_product_valid === true ? null : $is_product_valid,
            'removed_vouchers' => $is_voucher_valid['removed_vouchers'] ?? null,
            'show_msg' => $show_msg
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function badge()
    {
        $user = auth()->user();
        $cart = Carts::where('user_id', $user->id)->first();
        if ($cart):
            $normal_product = $cart->products()->where(function ($q) {
                $q->whereNull('cart_bundle_id')
                    ->orWhere('cart_bundle_id', 0);
            })->count();

            $bundle_product = $cart->products->where('cart_bundle_id', '>', 0)
                ->groupBy('cart_bundle_id')
                ->count();
            $cart_count = $normal_product + $bundle_product;
        endif;

        $data = ['cart_count' => $cart_count ?? 0];
        return GlobalFunction::sendDataResponse(true, '', $data);
    }
}
