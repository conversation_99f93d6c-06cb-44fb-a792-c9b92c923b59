<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\Outlets;
use Illuminate\Http\Request;
use App\Exports\GeneralExport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use App\Models\ChainStore;
use Maatwebsite\Excel\Facades\Excel;

class OutletController extends Controller
{
    function index()
    {
        if (!checkPermission('view_store')):
            return redirect(route('index'))->with('error', __('You do not have permission to view store.'));
        endif;

        $chainStoreLists = ChainStore::get();

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('storeDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('storeExport'),
                'filename' => 'stores'
            ]
        ];

        if (!checkPermission('delete_store')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_store')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'outlets';
        return view('outlets', compact('bulk_action', 'moduleID', 'chainStoreLists'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        $result = Outlets::when($request->input('search.value'), function ($q) use ($request) {
            $search = $request->input('search.value');
            $q->where('name', 'LIKE', "%{$search}%")
                ->orWhere('address', 'LIKE', "%{$search}%");
        })
            // ->where(function($query) {
            //     foreach (userBrand() as $brand) {
            //         $query->orWhereJsonContains('brand', $brand);
            //     }
            // })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->whereJsonContains('brand', $request->brand);
            // })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->store) && $request->store != 'all', function ($query) use ($request) {
            //     $query->where('chain_store_id', $request->store);
            // })
            ->orderBy($sort_col, $sort_by);
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="outletsList">
                    <span></span>
                </label>';

                if (!checkPermission('edit_store')):
                    $status = '<div class="text-center">' . ($item->is_active == 1 ? __('Yes') : __('No')) . '</div>';
                else:
                    $status = '<div class="text-center">
                        <label class="switch mb-0">
                            <input rel=' . $item->id . ' type="checkbox" class="enable"' . ($item->is_active == 1 ? ' checked' : '') . '>
                            <span class="slider round"></span>
                        </label>
                    </div>';
                endif;

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_store')):
                    $action .= '<a href="' . route('storeDetail', ['id' => $item->id]) . '" class="btn-icon btn-info mr-1" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_store')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $brand = $item->brand ? implode(', ', array_map(function($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';

                $param = [
                    $checkbox,
                    $item->name,
                    // isset($item->chainStore) ? $item->chainStore->name : '-',
                    // $brand,
                    $item->contact_number,
                    nl2br($item->address),
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_store')):
                    unset($param[8]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function addUpdate(Request $request)
    {
        if (!GlobalFunction::validatePhoneNumber($request->contact_number)):
            return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
        endif;

        // time validation
        foreach ($request->time_from as $day => $time):
            if ($time):
                if (!$request->time_to[$day]):
                    return GlobalFunction::sendSimpleResponse(false, __('Please select the end time.'));
                endif;
                // if (strtotime($time) >= strtotime($request->time_to[$day])):
                //     return GlobalFunction::sendSimpleResponse(false, __('End time must be greater than start time.'));
                // endif;
            endif;
        endforeach;
        // END time validation

        $id = $request->id ?? null;
        if ($id):
            $msg = __('Updated!');
            $query = Outlets::find($id);
        else:
            if(!$request->has('image')):
                return GlobalFunction::sendSimpleResponse(false, __('Please select image.'));
            endif;

            $msg = __('Added!');
            $query = new Outlets();
        endif;

        $query->name = $request->name;
        // $query->chain_store_id = $request->chain_store_id;
        $query->contact_number = $request->contact_number;
        // $query->brand = json_encode($request->brand);
        $query->state = $request->state;
        $query->address = $request->address;
        $query->is_active = $request->status ?? 0;
        $query->is_featured = $request->is_featured ?? 0;
        $query->description = $request->description;

        $latlong = getLatLong($request->address);
        $query->latitude = $latlong['lat'] ?? $request->latitude;
        $query->longitude = $latlong['long'] ?? $request->longitude;

        if($request->has('image')):
            $query->image = GlobalFunction::saveFileAndGivePath($request->image);
        endif;

        $operating = [];
        foreach ($request->time_from as $day => $time):
            $operating[$day] = [
                'from' => $request->time_from[$day] ? date(config('app.db_time_format'), strtotime($request->time_from[$day])) : '',
                'to' => $request->time_to[$day] ? date(config('app.db_time_format'), strtotime($request->time_to[$day])) : ''
            ];
        endforeach;

        $query->operating_hours = json_encode($operating);
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, $msg);
    }

    function detail($id)
    {
        if (!checkPermission('view_store')):
            return redirect(route('index'))->with('error', __('You do not have permission to view outlet.'));
        endif;

        $data = Outlets::find($id);
        $data->brand = json_decode($data->brand);
        if ($data->operating_hours):
            $operating_hours = json_decode($data->operating_hours, true);
            foreach ($operating_hours as $day => $time):
                if ($time['from']):
                    $operating_hours[$day]['from'] = date(config('app.display_time_format'), strtotime($time['from']));
                    $operating_hours[$day]['to'] = date(config('app.display_time_format'), strtotime($time['to']));
                endif;
            endforeach;
            $data->operating_hours = $operating_hours;
        endif;

        $moduleID = 'outlets';
        $moduleStringID = 'outletStringType';

        $chainStoreLists = ChainStore::get();

        return view('outlet-detail', compact('data', 'moduleID', 'moduleStringID', 'chainStoreLists'));
    }

    function delete(Request $request)
    {
        if ($request->id):
            Outlets::find($request->id)->delete();
            // OutletStringTypes::where('outlet_id', $request->id)->delete();
        else:
            Outlets::whereIn('id', $request->selected)->delete();
            // OutletStringTypes::whereIn('outlet_id', $request->selected)->delete();
        endif;

        return GlobalFunction::sendSimpleResponse(true, 'Deleted!');
    }

    function statusUpdate(Request $request)
    {
        $item = Outlets::find($request->id);
        $item->is_active = $request->value;
        $item->save();

        return GlobalFunction::sendSimpleResponse(true, 'Updated!');
    }

    function export(Request $request)
    {
        $sort_col = $request->sort_col != null ? $request->sort_col : 'id';
        $sort_by = $request->sort_by != null ? $request->sort_by : 'DESC';
        $result = Outlets::when($request->search, function ($q) use ($request) {
            $search = $request->search;
            $q->where('name', 'LIKE', "%{$search}%")
                ->orWhere('address', 'LIKE', "%{$search}%");
        })
            ->when($request->selected, function ($q) use ($request) {
                $q->whereIn('id', $request->selected);
            })
            // ->where(function($query) {
            //     foreach (userBrand() as $brand) {
            //         $query->orWhereJsonContains('brand', $brand);
            //     }
            // })
            // ->when(isset($request->brand) && $request->brand != 'all', function($query) use ($request) {
            //     $query->whereJsonContains('brand', $request->brand);
            // })
            ->when(isset($request->status) && $request->status != 'all', function ($query) use ($request) {
                $query->where('is_active', $request->status);
            })
            // ->when(isset($request->store) && $request->store != 'all', function ($query) use ($request) {
            //     $query->where('chain_store_id', $request->store);
            // })
            ->orderBy($sort_col, $sort_by)
            ->get();

        if ($result->count() > 0):
            foreach ($result as $item):
                $item->locate = 'https://www.google.com/maps/?q=' . $item->latitude . ',' . $item->longitude . '';
                $item->date = Carbon::parse($item->created_at)->format(config('app.display_datetime_format'));
                $item->address = str_replace('&', '&amp;', $item->address);
                $item->address = nl2br($item->address);

                $item->brand = $item->brand ? implode(', ', array_map(function($brandItem) {
                    return config('staticdata.brand.' . $brandItem);
                }, json_decode($item->brand))) : '';

                if ($item->operating_hours):
                    $operating_hours = json_decode($item->operating_hours, true);
                    $operating = '';
                    foreach ($operating_hours as $day => $time):
                        $operating .= ucfirst($day) . ': ';

                        if ($time['from']):
                            $operating .= date(config('app.display_time_format'), strtotime($time['from'])) . ' - ' . date(config('app.display_time_format'), strtotime($time['to'])) . "<br>";
                        else:
                            $operating .= "-<br>";
                        endif;
                    endforeach;
                    $item->operating_hours = $operating;
                endif;
            endforeach;
        endif;

        $fileName = 'stores_' . date('ymd') . '.xlsx';
        return Excel::download(
            new GeneralExport($result, 'outlets'),
            $fileName
        );
    }
}
