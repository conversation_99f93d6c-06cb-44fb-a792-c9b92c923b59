<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PushNotification extends Model
{
    use HasFactory;
    public $table = "push_notifications";
    protected $fillable = [
        'type',
        'type_id',
        'user_type',
        'user_id',
        'title',
        'message',
        'extra_data',
        'read_at',
    ];

    public function booking()
    {
        return $this->belongsTo(Bookings::class, 'type_id', 'id');
    }
}